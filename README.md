# IntelliTest 🚀

> **Revolutionary AI-Powered Test Automation Platform with Code Generation**

IntelliTest is a comprehensive, enterprise-grade test automation platform that transforms how teams create, refine, and manage software requirements with intelligent test case generation and **revolutionary automated code generation** capabilities.

[![Production Ready](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)](https://github.com/your-repo/intellitest)
[![Docker](https://img.shields.io/badge/Docker-Supported-blue)](https://docker.com)
[![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue)](https://typescriptlang.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-Async-green)](https://fastapi.tiangolo.com)

## ✨ Revolutionary Features

### 🤖 **AI-Powered Code Generation** (NEW!)
- **Complete Project Generation**: Generate full test automation projects from requirements
- **Multi-Framework Support**: Selenium, Playwright integration
- **Multi-Language Output**: Python, JavaScript, Java, C# code generation
- **Real-time Streaming**: Live code generation with progress tracking
- **Smart Integration**: Intelligent merging with existing codebases
- **Downloadable Packages**: Complete projects with dependencies

### 🌐 **Advanced Web Intelligence**
- **Smart Page Crawling**: Intelligent web page element detection
- **Authentication Support**: Login-protected page analysis
- **Element Classification**: Automatic categorization of interactive elements
- **Variable Extraction**: Dynamic variable detection from URLs

### 👥 **Enterprise Team Management**
- **Multi-Authentication**: Email/password + Google OAuth integration
- **Project Sharing**: Advanced member management with Owner/Member roles
- **Real-time Collaboration**: Live updates across team members
- **Advanced Permissions**: Granular access control and security

### 🎯 **Intelligent Requirements Management**
- **AI Refinement**: Ollama LLM-powered requirement improvement
- **Smart Test Generation**: Context-aware test case creation with RAG
- **Variable System**: Dynamic @variable syntax support
- **Tagging System**: Color-coded organization with auto-suggestions
- **Real-time Editing**: Auto-save with optimistic updates

### 🔒 **Enterprise Security**
- **JWT Authentication**: Secure tokens with automatic refresh
- **Google OAuth**: Seamless social authentication
- **Password Management**: Forgot password with SMTP integration
- **Role-based Access**: Owner/Member permissions with granular control

## 🏗️ Advanced Architecture

### Frontend (Next.js 14)
- **Framework**: Next.js 14 with App Router and TypeScript
- **UI Library**: Shadcn UI with Tailwind CSS design system
- **State Management**: React Context with optimistic updates
- **Authentication**: JWT with automatic refresh and secure storage
- **Performance**: Image optimization, font preloading, code splitting
- **Accessibility**: WCAG compliant with proper ARIA labels

### Backend (FastAPI)
- **Framework**: FastAPI with async/await and Python 3.9+
- **Database**: PostgreSQL with SQLAlchemy ORM and Alembic migrations
- **Authentication**: JWT + bcrypt + Google OAuth integration
- **AI Services**: Ollama LLM + Qdrant Vector Database + RAG
- **API Documentation**: OpenAPI/Swagger with interactive docs
- **Performance**: Database indexes, connection pooling, caching

### AI/ML Services
- **LLM Integration**: Ollama for local language model inference
- **Vector Database**: Qdrant for semantic search and RAG implementation
- **Code Generation**: Advanced AST parsing and intelligent code synthesis
- **Web Crawling**: Intelligent page analysis and element detection

## 📋 Prerequisites

- Docker and Docker Compose
- Git

## 🚀 Quick Start

### Development Environment

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd IntelliTest
   ```

2. **Run the setup script**
   ```bash
   ./scripts/setup-dev.sh
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Production Environment

1. **Configure environment**
   ```bash
   cp .env.prod.example .env.prod
   # Edit .env.prod with your production values
   ```

2. **Run the setup script**
   ```bash
   ./scripts/setup-prod.sh
   ```

3. **Access the application**
   - Application: http://localhost (or your domain)

## �️ Database Management

### Running Migrations

```bash
# Docker environment
docker exec intellitest_backend_dev python3 -m alembic upgrade head

# Manual environment
cd backend && source venv/bin/activate
python3 -m alembic upgrade head
```

### Database Rename (if migrating from ai_ide)

If you have an existing `ai_ide` database, rename it to `intellitest`:

```bash
# See DATABASE_RENAME_COMMANDS.md for detailed instructions
sudo -u postgres psql -c "ALTER DATABASE ai_ide RENAME TO intellitest;"
```

## 🛠️ Manual Development Setup

### Backend Setup

```bash
cd backend
python3 -m venv venv && source venv/bin/activate
pip install -r requirements.txt
cp .env.example .env  # Edit with your database config
python3 -m alembic upgrade head
uvicorn app.main:app --reload
```

### Frontend Setup

```bash
cd frontend
npm install
echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1" > .env.local
npm run dev
```

### External Services

```bash
# Qdrant Vector Database
docker run -p 6333:6333 qdrant/qdrant:latest

# Ollama LLM (install from https://ollama.ai)
ollama serve
ollama pull llama3.2:latest
```

## 📚 API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Production: http://your-domain/api/docs

### Key Endpoints

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/projects/` - List projects
- `POST /api/v1/projects/` - Create project
- `GET /api/v1/requirements/` - List requirements
- `POST /api/v1/requirements/{id}/refine` - Refine requirement with AI
- `POST /api/v1/requirements/{id}/generate-tests` - Generate test cases

## 🧪 Testing

### Backend Tests

```bash
cd backend
source venv/bin/activate
python -m pytest tests/ -v
```

### Frontend Tests

```bash
cd frontend
npm test
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
- `POSTGRES_*` - Database configuration
- `SECRET_KEY` - JWT secret key
- `OLLAMA_BASE_URL` - Ollama service URL
- `QDRANT_HOST` - Qdrant service host

#### Frontend (.env.local)
- `NEXT_PUBLIC_API_URL` - Backend API URL

## 📁 Project Structure

```
IntelliTest/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── crud/           # Database operations
│   │   ├── db/             # Database setup
│   │   ├── models/         # SQLAlchemy models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── services/       # Business logic
│   ├── tests/              # Backend tests
│   └── Dockerfile
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Next.js app router
│   │   ├── components/    # React components
│   │   ├── contexts/      # React contexts
│   │   └── lib/           # Utilities
│   └── Dockerfile
├── scripts/               # Setup scripts
├── docker-compose.dev.yml # Development compose
├── docker-compose.prod.yml # Production compose
└── nginx.conf            # Nginx configuration
```

## 🚀 Deployment

### Docker Compose (Recommended)

The application is designed to run with Docker Compose for both development and production environments.

### Manual Deployment

1. **Database Setup**
   - Install PostgreSQL
   - Create database and user
   - Run migrations

2. **Backend Deployment**
   - Install Python dependencies
   - Configure environment variables
   - Run with Gunicorn or similar

3. **Frontend Deployment**
   - Build Next.js application
   - Serve with Nginx or similar
