#!/bin/bash

# IntelliTest Development Setup (Without Database)
# This script sets up the development environment using Docker for services
# but expects PostgreSQL to be running externally

set -e

echo "🚀 Setting up IntelliTest Development Environment (No Database)"
echo "=============================================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if PostgreSQL is accessible
echo "🔍 Checking PostgreSQL connection..."
if ! command -v psql &> /dev/null; then
    echo "⚠️  psql command not found. Please ensure PostgreSQL client is installed."
    echo "   Ubuntu/Debian: sudo apt-get install postgresql-client"
    echo "   macOS: brew install postgresql"
fi

# Check if database exists
DB_HOST=${POSTGRES_SERVER:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}
DB_NAME=${POSTGRES_DB:-intellitest}
DB_USER=${POSTGRES_USER:-postgres}

echo "📊 Testing database connection to $DB_HOST:$DB_PORT/$DB_NAME..."
if command -v psql &> /dev/null; then
    if ! PGPASSWORD=${POSTGRES_PASSWORD:-password} psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        echo "❌ Cannot connect to PostgreSQL database."
        echo "   Please ensure PostgreSQL is running and database '$DB_NAME' exists."
        echo "   Create database: createdb $DB_NAME"
        exit 1
    else
        echo "✅ Database connection successful"
    fi
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p ssl
mkdir -p backend/logs

# Check if .env file exists
if [ ! -f "backend/.env" ]; then
    echo "📝 Creating backend environment file..."
    cp backend/.env.example backend/.env
    echo "⚠️  Please edit backend/.env with your database credentials"
fi

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose -f docker-compose.dev-no-db.yml up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
docker-compose -f docker-compose.dev-no-db.yml ps

# Check if Qdrant is healthy
if curl -f http://localhost:6333/health &> /dev/null; then
    echo "✅ Qdrant is healthy"
else
    echo "⚠️  Qdrant may not be ready yet"
fi

# Check if Ollama is healthy
if curl -f http://localhost:11434/api/version &> /dev/null; then
    echo "✅ Ollama is healthy"
else
    echo "⚠️  Ollama may not be ready yet"
fi

# Pull Ollama model
echo "🤖 Pulling Ollama model (this may take a while)..."
docker exec intellitest_ollama_dev ollama pull ${OLLAMA_MODEL:-llama3.2:latest}

# Check if backend is healthy
if curl -f http://localhost:8000/health &> /dev/null; then
    echo "✅ Backend is healthy"
else
    echo "⚠️  Backend may not be ready yet"
fi

# Check if frontend is healthy
if curl -f http://localhost:3000 &> /dev/null; then
    echo "✅ Frontend is healthy"
else
    echo "⚠️  Frontend may not be ready yet"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Ensure your PostgreSQL database is running"
echo "2. Run database migrations:"
echo "   cd backend"
echo "   python3 -m venv venv"
echo "   source venv/bin/activate"
echo "   pip install -r requirements.txt"
echo "   python3 -m alembic upgrade head"
echo ""
echo "🌐 Services Available:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:8000"
echo "   Qdrant:   http://localhost:6333"
echo "   Ollama:   http://localhost:11434"
echo ""
echo "📊 Useful Commands:"
echo "   View logs:    docker-compose -f docker-compose.dev-no-db.yml logs -f"
echo "   Stop:         docker-compose -f docker-compose.dev-no-db.yml down"
echo "   Restart:      docker-compose -f docker-compose.dev-no-db.yml restart"
echo ""
echo "🔧 Database Connection:"
echo "   Host: $DB_HOST"
echo "   Port: $DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo ""
