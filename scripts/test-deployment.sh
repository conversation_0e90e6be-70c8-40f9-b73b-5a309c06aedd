#!/bin/bash

# IntelliTest Deployment Test Script

set -e

echo "🧪 Testing IntelliTest Deployment..."

# Configuration
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:8000"
API_URL="$BACKEND_URL/api/v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test functions
test_endpoint() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    echo -n "Testing $description... "
    
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $status)"
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (HTTP $status, expected $expected_status)"
        return 1
    fi
}

test_json_endpoint() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    echo -n "Testing $description... "
    
    response=$(curl -s -w "\n%{http_code}" "$url" || echo -e "\n000")
    status=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $status)"
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (HTTP $status, expected $expected_status)"
        echo "Response: $body"
        return 1
    fi
}

# Check if services are running
echo "🔍 Checking if services are running..."

if ! docker ps | grep -q "intellitest_frontend"; then
    echo -e "${RED}❌ Frontend container not running${NC}"
    exit 1
fi

if ! docker ps | grep -q "intellitest_backend"; then
    echo -e "${RED}❌ Backend container not running${NC}"
    exit 1
fi

if ! docker ps | grep -q "intellitest_postgres"; then
    echo -e "${RED}❌ PostgreSQL container not running${NC}"
    exit 1
fi

echo -e "${GREEN}✓ All containers are running${NC}"

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Test backend endpoints
echo ""
echo "🔧 Testing Backend API..."

test_json_endpoint "$BACKEND_URL/" 200 "Root endpoint"
test_json_endpoint "$BACKEND_URL/health" 200 "Health check"
test_json_endpoint "$API_URL/auth/me" 401 "Auth endpoint (should require authentication)"

# Test frontend
echo ""
echo "🌐 Testing Frontend..."

test_endpoint "$FRONTEND_URL" 200 "Frontend homepage"

# Test API documentation
echo ""
echo "📚 Testing API Documentation..."

test_endpoint "$BACKEND_URL/docs" 200 "API documentation"
test_endpoint "$BACKEND_URL/openapi.json" 200 "OpenAPI specification"

# Test database connection
echo ""
echo "🗄️  Testing Database Connection..."

db_test=$(docker exec intellitest_backend_dev python -c "
from app.db.base import engine
try:
    with engine.connect() as conn:
        result = conn.execute('SELECT 1')
        print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
    exit(1)
" 2>/dev/null || echo "Database test failed")

if [[ "$db_test" == *"successful"* ]]; then
    echo -e "${GREEN}✓ Database connection working${NC}"
else
    echo -e "${RED}✗ Database connection failed${NC}"
    echo "$db_test"
fi

# Test Qdrant connection
echo ""
echo "🔍 Testing Qdrant Vector Database..."

if docker ps | grep -q "intellitest_qdrant"; then
    qdrant_test=$(curl -s "http://localhost:6333/health" || echo "failed")
    if [[ "$qdrant_test" == *"ok"* ]]; then
        echo -e "${GREEN}✓ Qdrant is healthy${NC}"
    else
        echo -e "${YELLOW}⚠ Qdrant may not be ready yet${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Qdrant container not found${NC}"
fi

# Test Ollama connection
echo ""
echo "🤖 Testing Ollama LLM Service..."

if docker ps | grep -q "intellitest_ollama"; then
    ollama_test=$(curl -s "http://localhost:11434/api/version" || echo "failed")
    if [[ "$ollama_test" == *"version"* ]]; then
        echo -e "${GREEN}✓ Ollama is running${NC}"

        # Check if model is available
        model_test=$(docker exec intellitest_ollama_dev ollama list 2>/dev/null | grep -q "llama3.2" && echo "model_found" || echo "model_not_found")
        if [[ "$model_test" == "model_found" ]]; then
            echo -e "${GREEN}✓ Llama model is available${NC}"
        else
            echo -e "${YELLOW}⚠ Llama model not found - run: docker exec intellitest_ollama_dev ollama pull llama3.2:latest${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ Ollama may not be ready yet${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Ollama container not found${NC}"
fi

# Test user registration and login flow
echo ""
echo "👤 Testing User Authentication Flow..."

# Generate random email for testing
TEST_EMAIL="test$(date +%s)@example.com"
TEST_PASSWORD="testpassword123"

# Test registration
echo -n "Testing user registration... "
register_response=$(curl -s -X POST "$API_URL/auth/register" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\",\"full_name\":\"Test User\"}" \
    -w "\n%{http_code}")

register_status=$(echo "$register_response" | tail -n1)
register_body=$(echo "$register_response" | head -n -1)

if [ "$register_status" = "200" ]; then
    echo -e "${GREEN}✓ PASS${NC}"
    
    # Extract token
    TOKEN=$(echo "$register_body" | python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])" 2>/dev/null || echo "")
    
    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✓ JWT token received${NC}"
        
        # Test authenticated endpoint
        echo -n "Testing authenticated endpoint... "
        me_response=$(curl -s "$API_URL/auth/me" \
            -H "Authorization: Bearer $TOKEN" \
            -w "\n%{http_code}")
        
        me_status=$(echo "$me_response" | tail -n1)
        
        if [ "$me_status" = "200" ]; then
            echo -e "${GREEN}✓ PASS${NC}"
        else
            echo -e "${RED}✗ FAIL${NC} (HTTP $me_status)"
        fi
    else
        echo -e "${YELLOW}⚠ Could not extract token${NC}"
    fi
else
    echo -e "${RED}✗ FAIL${NC} (HTTP $register_status)"
    echo "Response: $register_body"
fi

# Summary
echo ""
echo "📊 Test Summary"
echo "==============="

# Count running containers
running_containers=$(docker ps --filter "name=intellitest_" --format "table {{.Names}}" | grep -c "intellitest_" || echo "0")
echo "Running containers: $running_containers"

# Check service health
services_healthy=0
total_services=4

if docker ps | grep -q "intellitest_frontend"; then ((services_healthy++)); fi
if docker ps | grep -q "intellitest_backend"; then ((services_healthy++)); fi
if docker ps | grep -q "intellitest_postgres"; then ((services_healthy++)); fi
if curl -s "$BACKEND_URL/health" | grep -q "healthy"; then ((services_healthy++)); fi

echo "Healthy services: $services_healthy/$total_services"

if [ $services_healthy -eq $total_services ]; then
    echo -e "${GREEN}🎉 All tests passed! Deployment is working correctly.${NC}"
    echo ""
    echo "🌐 Access your application:"
    echo "   Frontend: $FRONTEND_URL"
    echo "   Backend API: $BACKEND_URL"
    echo "   API Docs: $BACKEND_URL/docs"
    exit 0
else
    echo -e "${YELLOW}⚠ Some services may need attention.${NC}"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   Check logs: docker-compose logs -f"
    echo "   Restart services: docker-compose restart"
    exit 1
fi
