#!/bin/bash

# IntelliTest Development Setup Script

set -e

echo "🚀 Setting up IntelliTest Development Environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p ssl

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose -f docker-compose.dev.yml up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
docker-compose -f docker-compose.dev.yml ps

# Pull Ollama model
echo "🤖 Pulling Ollama model (this may take a while)..."
docker exec intellitest_ollama_dev ollama pull llama3.2:latest

# Run database migrations
echo "🗄️  Running database migrations..."
docker exec intellitest_backend_dev python -c "
from app.db.init_db import create_tables
create_tables()
print('Database tables created successfully!')
"

echo "✅ Development environment is ready!"
echo ""
echo "🌐 Services are available at:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo "   PostgreSQL: localhost:5432"
echo "   Qdrant: http://localhost:6333"
echo "   Ollama: http://localhost:11434"
echo ""
echo "📝 To view logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "🛑 To stop: docker-compose -f docker-compose.dev.yml down"
