#!/bin/bash

# Final Integration Test Script

set -e

echo "🧪 Running Final Integration Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name=$1
    local test_command=$2
    
    echo -n "Testing $test_name... "
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ PASS${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗ FAIL${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

echo -e "${BLUE}=== Backend Tests ===${NC}"

# Test 1: Backend imports
run_test "Backend imports" "cd backend && source venv/bin/activate && python -c 'import app.main'"

# Test 2: Database models
run_test "Database models" "cd backend && source venv/bin/activate && python -c 'from app.models.user import User; from app.models.project import Project'"

# Test 3: API schemas
run_test "API schemas" "cd backend && source venv/bin/activate && python -c 'from app.schemas.user import User; from app.schemas.project import Project'"

# Test 4: Services
run_test "LLM service" "cd backend && source venv/bin/activate && python -c 'from app.services.llm_service import llm_service'"

# Test 5: RAG service
run_test "RAG service" "cd backend && source venv/bin/activate && python -c 'from app.services.rag_service import rag_service'"

echo -e "${BLUE}=== Frontend Tests ===${NC}"

# Test 6: Frontend build
run_test "Frontend build" "cd frontend && npm run build"

# Test 7: TypeScript compilation
run_test "TypeScript check" "cd frontend && npx tsc --noEmit"

echo -e "${BLUE}=== Docker Tests ===${NC}"

# Test 8: Backend Dockerfile syntax
run_test "Backend Dockerfile" "docker build -f backend/Dockerfile --target development -t test-backend backend"

# Test 9: Frontend Dockerfile syntax
run_test "Frontend Dockerfile" "docker build -f frontend/Dockerfile --target development -t test-frontend frontend"

# Test 10: Docker Compose syntax
run_test "Docker Compose dev" "docker-compose -f docker-compose.dev.yml config"

# Test 11: Docker Compose prod
run_test "Docker Compose prod" "docker-compose -f docker-compose.prod.yml config"

echo -e "${BLUE}=== Configuration Tests ===${NC}"

# Test 12: Environment files
run_test "Environment config" "test -f .env.prod.example && test -f backend/.env && test -f frontend/.env.local"

# Test 13: Scripts executable
run_test "Setup scripts" "test -x scripts/setup-dev.sh && test -x scripts/setup-prod.sh && test -x scripts/test-deployment.sh"

echo -e "${BLUE}=== Documentation Tests ===${NC}"

# Test 14: Documentation files
run_test "Documentation" "test -f README.md && test -f DEPLOYMENT.md && test -f API.md && test -f PROJECT_SUMMARY.md"

# Test 15: Project structure
run_test "Project structure" "test -d backend/app && test -d frontend/src && test -d scripts"

echo ""
echo -e "${BLUE}=== Test Summary ===${NC}"
echo "Tests passed: ${GREEN}$TESTS_PASSED${NC}"
echo "Tests failed: ${RED}$TESTS_FAILED${NC}"
echo "Total tests: $((TESTS_PASSED + TESTS_FAILED))"

# Clean up test images
echo ""
echo "🧹 Cleaning up test images..."
docker rmi test-backend test-frontend > /dev/null 2>&1 || true

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! The application is ready for deployment.${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Run development environment: ./scripts/setup-dev.sh"
    echo "2. Or run production environment: ./scripts/setup-prod.sh"
    echo "3. Test the deployment: ./scripts/test-deployment.sh"
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some tests failed. Please review and fix the issues.${NC}"
    exit 1
fi
