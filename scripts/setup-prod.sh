#!/bin/bash

# IntelliTest Production Setup Script

set -e

echo "🚀 Setting up IntelliTest Production Environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.prod exists
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod file not found. Please copy .env.prod.example to .env.prod and configure it."
    exit 1
fi

# Load environment variables
export $(cat .env.prod | xargs)

# Validate required environment variables
if [ -z "$POSTGRES_PASSWORD" ] || [ -z "$SECRET_KEY" ]; then
    echo "❌ Required environment variables are not set. Please check your .env.prod file."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p ssl

# Generate SSL certificates (self-signed for demo)
if [ ! -f "ssl/cert.pem" ]; then
    echo "🔐 Generating self-signed SSL certificates..."
    openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=${DOMAIN:-localhost}"
fi

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose -f docker-compose.prod.yml up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 60

# Check service health
echo "🔍 Checking service health..."
docker-compose -f docker-compose.prod.yml ps

# Pull Ollama model
echo "🤖 Pulling Ollama model (this may take a while)..."
docker exec intellitest_ollama_prod ollama pull ${OLLAMA_MODEL:-llama3.2:latest}

# Run database migrations
echo "🗄️  Running database migrations..."
docker exec intellitest_backend_prod python -c "
from app.db.init_db import create_tables
create_tables()
print('Database tables created successfully!')
"

echo "✅ Production environment is ready!"
echo ""
echo "🌐 Application is available at:"
echo "   HTTP: http://localhost"
echo "   HTTPS: https://localhost (with self-signed certificate)"
echo ""
echo "📝 To view logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "🛑 To stop: docker-compose -f docker-compose.prod.yml down"
echo ""
echo "⚠️  Remember to:"
echo "   - Configure proper SSL certificates for production"
echo "   - Set up proper DNS records"
echo "   - Configure firewall rules"
echo "   - Set up monitoring and backups"
