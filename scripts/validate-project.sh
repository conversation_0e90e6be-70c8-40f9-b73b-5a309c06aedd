#!/bin/bash

# Project Validation Script

echo "🔍 Validating IntelliTest Project..."

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

CHECKS_PASSED=0
CHECKS_TOTAL=0

check() {
    local name=$1
    local command=$2
    ((CHECKS_TOTAL++))
    
    echo -n "Checking $name... "
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
        ((CHECKS_PASSED++))
    else
        echo -e "${RED}✗${NC}"
    fi
}

echo -e "${BLUE}=== Project Structure ===${NC}"
check "Backend directory" "test -d backend/app"
check "Frontend directory" "test -d frontend/src"
check "Scripts directory" "test -d scripts"
check "Docker files" "test -f docker-compose.dev.yml && test -f docker-compose.prod.yml"

echo -e "${BLUE}=== Backend Validation ===${NC}"
check "Backend dependencies" "test -f backend/requirements.txt"
check "Backend Dockerfile" "test -f backend/Dockerfile"
check "Main application" "test -f backend/app/main.py"
check "Database models" "test -f backend/app/models/user.py"
check "API routes" "test -d backend/app/api"

echo -e "${BLUE}=== Frontend Validation ===${NC}"
check "Frontend dependencies" "test -f frontend/package.json"
check "Frontend Dockerfile" "test -f frontend/Dockerfile"
check "Next.js config" "test -f frontend/next.config.ts"
check "Main components" "test -d frontend/src/components"
check "API client" "test -f frontend/src/lib/api.ts"

echo -e "${BLUE}=== Documentation ===${NC}"
check "README" "test -f README.md"
check "Deployment guide" "test -f DEPLOYMENT.md"
check "API documentation" "test -f API.md"
check "Project summary" "test -f PROJECT_SUMMARY.md"

echo -e "${BLUE}=== Configuration ===${NC}"
check "Environment example" "test -f .env.prod.example"
check "Setup scripts" "test -x scripts/setup-dev.sh"
check "Nginx config" "test -f nginx.conf"

echo ""
echo -e "${BLUE}=== Summary ===${NC}"
echo "Checks passed: ${GREEN}$CHECKS_PASSED${NC}/$CHECKS_TOTAL"

if [ $CHECKS_PASSED -eq $CHECKS_TOTAL ]; then
    echo -e "${GREEN}🎉 Project validation successful!${NC}"
    echo ""
    echo "The IntelliTest is complete and ready for deployment."
    echo ""
    echo "Quick start commands:"
    echo "  Development: ./scripts/setup-dev.sh"
    echo "  Production:  ./scripts/setup-prod.sh"
    echo "  Testing:     ./scripts/test-deployment.sh"
    exit 0
else
    echo -e "${RED}❌ Some validation checks failed.${NC}"
    exit 1
fi
