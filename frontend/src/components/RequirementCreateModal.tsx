'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TextareaWithSuggestions } from '@/components/ui/textarea-with-suggestions';
import { ColoredTag } from '@/components/ui/colored-tag';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogBody,
  DialogFooter,
} from '@/components/ui/dialog';
import { BadgePlus } from 'lucide-react';

interface Tag {
  id: number;
  name: string;
  color?: string;
  created_at: string;
}

interface RequirementCreateModalProps {
  projectId: number;
  onClose: () => void;
  onRequirementCreated: (requirement: {
    id: number;
    name: string;
    slug: string;
    created_at: string;
    created_by: number;
    tags?: Tag[];
  }) => void;
}

export function RequirementCreateModal({
  projectId,
  onClose,
  onRequirementCreated,
}: Readonly<RequirementCreateModalProps>) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tag_names: [] as string[],
  });
  const [tagInput, setTagInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [filteredTags, setFilteredTags] = useState<Tag[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const MAX_TAGS = 5;

  // Load cached tags or fetch from API
  useEffect(() => {
    const cachedTags = localStorage.getItem('cached_tags');
    if (cachedTags) {
      try {
        const tags = JSON.parse(cachedTags);
        setAvailableTags(tags);
      } catch (error) {
        console.error('Error parsing cached tags:', error);
        fetchTags();
      }
    } else {
      fetchTags();
    }
  }, []);

  const fetchTags = async () => {
    try {
      const tags = await apiClient.getTags();
      setAvailableTags(tags);
      localStorage.setItem('cached_tags', JSON.stringify(tags));
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  };

  // Filter tags based on input
  useEffect(() => {
    if (tagInput.trim()) {
      const filtered = availableTags.filter(tag =>
        tag.name.toLowerCase().includes(tagInput.toLowerCase()) &&
        !formData.tag_names.includes(tag.name)
      );
      setFilteredTags(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      setFilteredTags([]);
      setShowSuggestions(false);
    }
  }, [tagInput, availableTags, formData.tag_names]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleAddTag = (tagName?: string) => {
    const tagToAdd = tagName || tagInput.trim();

    if (tagToAdd && !formData.tag_names.includes(tagToAdd)) {
      if (formData.tag_names.length >= MAX_TAGS) {
        setError(`You can only add up to ${MAX_TAGS} tags per requirement`);
        return;
      }

      setFormData((prev) => ({
        ...prev,
        tag_names: [...prev.tag_names, tagToAdd],
      }));
      setTagInput('');
      setShowSuggestions(false);
      setError(''); // Clear any previous error

      // Update cached tags if this is a new tag
      if (!availableTags.find(tag => tag.name === tagToAdd)) {
        const newTag: Tag = {
          id: Date.now(), // Temporary ID
          name: tagToAdd,
          created_at: new Date().toISOString()
        };
        const updatedTags = [...availableTags, newTag];
        setAvailableTags(updatedTags);
        localStorage.setItem('cached_tags', JSON.stringify(updatedTags));
      }
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tag_names: prev.tag_names.filter((tag) => tag !== tagToRemove),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.description.trim()) {
      setError('Name and description are required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const requirementData = {
        ...formData,
        project_id: projectId,
      };

      const newRequirement = await apiClient.createRequirement(requirementData);
      onRequirementCreated(newRequirement);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create requirement');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="!w-[800px] !max-w-[800px] !h-[90vh] !p-0">
        {/* Sticky Header */}
        <DialogHeader className="px-6 py-6 border-b bg-gray-50 rounded-t-lg flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <BadgePlus className="h-5 w-5" />
            <span>Create New Requirement</span>
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Define a new requirement for your project
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex flex-col flex-1 overflow-hidden">
          {/* Scrollable Content Area */}
          <DialogBody className="p-6 space-y-4">
            <div className="space-y-3">
              <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                Requirement Name *
              </Label>
              <Input
                id="name"
                placeholder="Enter a clear, descriptive name for your requirement"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="h-10"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Description *
              </Label>
              <TextareaWithSuggestions
                id="description"
                placeholder="Describe the requirement in detail. Be specific about what functionality, behavior, or constraints are needed. Use @variable_name to reference project variables."
                value={formData.description}
                onValueChange={(value) => handleInputChange('description', value)}
                projectId={projectId}
                className="h-[150px] max-h-[150px] overflow-y-auto resize-none text-sm leading-relaxed"
              />
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 p-3 rounded-lg">
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">💡</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-xs font-medium text-blue-800 mb-1">Pro Tip</p>
                    <p className="text-xs text-blue-700">
                      Use <code className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-xs font-mono">@variableName</code> to reference project variables and make your requirements dynamic!
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">
                Tags (Optional) - {formData.tag_names.length}/{MAX_TAGS}
              </Label>
              <div className="relative">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Add a tag (e.g., 'authentication', 'security')"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                    onFocus={() => tagInput.trim() && setShowSuggestions(filteredTags.length > 0)}
                    onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                    className="h-10"
                    disabled={formData.tag_names.length >= MAX_TAGS}
                  />
                  <Button
                    type="button"
                    onClick={() => handleAddTag()}
                    variant="outline"
                    className="h-10 px-4"
                    disabled={formData.tag_names.length >= MAX_TAGS || !tagInput.trim()}
                  >
                    Add
                  </Button>
                </div>

                {/* Tag suggestions dropdown */}
                {showSuggestions && filteredTags.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto">
                    {filteredTags.slice(0, 5).map((tag) => (
                      <button
                        key={tag.id}
                        type="button"
                        onClick={() => handleAddTag(tag.name)}
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none text-sm"
                      >
                        {tag.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {formData.tag_names.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2 p-2 bg-gray-50 rounded-lg border">
                  {formData.tag_names.map((tag) => (
                    <ColoredTag
                      key={tag}
                      name={tag}
                      variant="default"
                      size="sm"
                      removable
                      onRemove={() => handleRemoveTag(tag)}
                    />
                  ))}
                </div>
              )}
            </div>
          </DialogBody>

          {/* Sticky Bottom Panel */}
          <DialogFooter className="bg-gray-50">
            {error && (
              <div className="w-full bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm mb-4">
                <strong>Error:</strong> {error}
              </div>
            )}
            <div className="flex justify-end space-x-3 w-full">
                <Button type="button" variant="outline" onClick={onClose} className="h-11 px-6">
                Cancel
                </Button>
                <Button type="submit" disabled={loading} className="h-11 px-6">
                {loading ? 'Creating...' : 'Create Requirement'}
                </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}