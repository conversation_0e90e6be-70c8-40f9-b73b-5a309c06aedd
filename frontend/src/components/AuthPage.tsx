'use client';

import { useState, useCallback, memo } from 'react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { ArrowRight, Eye, EyeOff } from 'lucide-react';
import { apiClient } from '@/lib/api';
import { useToast } from '@/hooks/useToast';
import { ToastContainer } from '@/components/ui/toast';

function AuthPageComponent() {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState('');
  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);
  const [forgotPasswordMessage, setForgotPasswordMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState({
    fullName: '',
    email: '',
    password: ''
  });

  const { login, register } = useAuth();
  const { toasts, showToast, removeToast } = useToast();

  const validateForm = useCallback(() => {
    const errors = { fullName: '', email: '', password: '' };
    let isValid = true;

    // Validate full name for signup
    if (!isLogin) {
      if (!fullName.trim()) {
        errors.fullName = 'Full name is required';
        isValid = false;
      } else if (fullName.trim().length < 2) {
        errors.fullName = 'Full name must be at least 2 characters';
        isValid = false;
      }
    }

    // Validate email
    if (!email.trim()) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    // Validate password
    if (!password) {
      errors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    setValidationErrors(errors);
    return isValid;
  }, [isLogin, fullName, email, password]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate form before submission
    if (!validateForm()) {
      setLoading(false);
      return;
    }

    try {
      if (isLogin) {
        await login(email, password);
      } else {
        await register(email, password, fullName);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [isLogin, email, password, fullName, login, register, validateForm]);

  const handleInputChange = useCallback((field: string, value: string) => {
    // Update the field value
    switch (field) {
      case 'fullName':
        setFullName(value);
        break;
      case 'email':
        setEmail(value);
        break;
      case 'password':
        setPassword(value);
        break;
    }

    // Clear validation error for this field when user starts typing
    if (validationErrors[field as keyof typeof validationErrors]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // Clear general error when user starts typing
    if (error) {
      setError('');
    }
  }, [validationErrors, error]);

  const handleToggleMode = useCallback(() => {
    setIsLogin(!isLogin);
    setError('');
    setValidationErrors({ fullName: '', email: '', password: '' });
  }, [isLogin]);

  const handleGoogleLogin = useCallback(async () => {
    try {
      const response = await apiClient.getGoogleAuthUrl();
      window.location.href = response.authorization_url;
    } catch (error) {
      console.debug('Google login error:', error);
      setError('Failed to initiate Google login');
    }
  }, []);

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setForgotPasswordLoading(true);
    setForgotPasswordMessage('');

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

      // First, check if the user exists and their auth provider
      const checkUserResponse = await fetch(`${API_BASE_URL}/auth/check-user-auth-provider`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: forgotPasswordEmail }),
      });

      if (checkUserResponse.ok) {
        const userData = await checkUserResponse.json();

        // If user uses Google SSO, show appropriate message
        if (userData.auth_provider === 'google') {
          setShowForgotPassword(false);
          setForgotPasswordEmail('');
          setForgotPasswordMessage('');
          showToast('You are using Google Sign-In. Please use "Sign in with Google" to access your account.', 'info');
          return;
        }
      }

      // Proceed with normal forgot password flow for email users
      const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: forgotPasswordEmail }),
      });

      if (response.ok) {
        // Close modal and show success toast
        setShowForgotPassword(false);
        setForgotPasswordEmail('');
        setForgotPasswordMessage('');
        showToast('Password reset instructions have been sent to your email.', 'success');
      } else {
        const errorData = await response.json();
        setForgotPasswordMessage(errorData.detail || 'Failed to send reset email. Please try again.');
      }
    } catch (error) {
      console.debug('Forgot password error:', error);
      setForgotPasswordMessage('An error occurred. Please try again.');
    } finally {
      setForgotPasswordLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-100 via-purple-50 to-purple-200 flex items-center justify-center p-4">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/2 right-1/4 transform -translate-y-1/2 hidden lg:block">
          <div className="w-96 h-96 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full opacity-80 blur-3xl"></div>
        </div>
        {/* Mobile background decoration */}
        <div className="absolute -top-20 -right-20 lg:hidden">
          <div className="w-64 h-64 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full opacity-60 blur-2xl"></div>
        </div>
        <div className="absolute -bottom-20 -left-20 lg:hidden">
          <div className="w-48 h-48 bg-gradient-to-br from-purple-300 to-purple-500 rounded-full opacity-40 blur-xl"></div>
        </div>
      </div>

      {/* Main content card */}
      <main id="main-content" className="relative bg-white rounded-2xl lg:rounded-3xl shadow-2xl w-full max-w-sm lg:max-w-4xl min-h-[600px] flex flex-col lg:flex-row overflow-hidden">
        {/* Left side - Form */}
        <div className="flex-1 p-6 lg:p-12 flex flex-col justify-center">
          {/* Logo/Brand */}
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-black rounded-full"></div>
              <span className="text-sm font-medium text-gray-600">IntelliTest</span>
            </div>
          </div>

          {/* Welcome text */}
          <div className="mb-6 lg:mb-8">
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
              {isLogin ? 'Welcome back' : 'Create account'}
            </h1>
            <p className="text-gray-600 text-sm lg:text-base">
              {isLogin
                ? 'Welcome back! Please enter your details.'
                : 'Please enter your details to create your account.'
              }
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-6">
            {!isLogin && (
              <div>
                <Label htmlFor="fullName" className="text-sm font-medium text-gray-700 mb-2 block">
                  Full Name *
                </Label>
                <Input
                  id="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    validationErrors.fullName ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {validationErrors.fullName && (
                  <p className="text-xs text-red-600 font-medium mt-1">
                    {validationErrors.fullName}
                  </p>
                )}
              </div>
            )}

            <div>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700 mb-2 block">
                Email *
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                  validationErrors.email ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {validationErrors.email && (
                <p className="text-xs text-red-600 font-medium mt-1">
                  {validationErrors.email}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="password" className="text-sm font-medium text-gray-700 mb-2 block">
                Password *
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    validationErrors.password ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                  aria-pressed={showPassword}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" aria-hidden="true" />
                  ) : (
                    <Eye className="h-5 w-5" aria-hidden="true" />
                  )}
                </button>
              </div>
              {validationErrors.password && (
                <p className="text-xs text-red-600 font-medium mt-1">
                  {validationErrors.password}
                </p>
              )}
            </div>

            {isLogin && (
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-purple-600 hover:text-purple-500 font-medium"
                >
                  Forgot password
                </button>
              </div>
            )}

            {error && (
              <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
                {error}
              </div>
            )}

            <Button
              type="submit"
              disabled={loading}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
            >
              {loading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <>
                  {isLogin ? 'Sign in' : 'Sign up'}
                  <ArrowRight className="w-4 h-4" />
                </>
              )}
            </Button>

            {/* Google Sign In Button */}
            <Button
              type="button"
              variant="outline"
              onClick={handleGoogleLogin}
              className="w-full py-3 px-4 border border-gray-300 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2 hover:bg-gray-50 bg-white"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Sign in with Google
            </Button>
          </form>

          {/* Switch between login/register */}
          <div className="mt-8 text-center">
            <span className="text-gray-600">
              {isLogin ? "Don't have an account? " : "Already have an account? "}
            </span>
            <button
              type="button"
              onClick={handleToggleMode}
              className="text-purple-600 hover:text-purple-500 font-medium"
            >
              {isLogin ? 'Sign up' : 'Sign in'}
            </button>
          </div>
        </div>

        {/* Right side - Decorative */}
        <div className="hidden lg:flex flex-1 relative bg-gray-50 items-center justify-center overflow-hidden">
          {/* Banner Image */}
          <div className="relative w-full h-full">
            <Image
              src="/banner.webp"
              alt="IntelliTest Banner"
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>
      </main>

      {/* Footer */}
      <div className="absolute bottom-4 left-4 lg:bottom-8 lg:left-8 text-xs lg:text-sm text-gray-500">
        © IntelliTest 2025
      </div>

      {/* Forgot Password Modal */}
      <Dialog open={showForgotPassword} onOpenChange={setShowForgotPassword}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="!border-b-0">
            <DialogTitle>Reset your password</DialogTitle>
            <DialogDescription>
              Enter your email address and we&apos;ll send you a link to reset your password.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleForgotPassword}>
            <DialogBody className="space-y-4 pt-2">
              <div>
                <Label htmlFor="forgotEmail" className="text-sm font-medium text-gray-700 mb-2 block">
                  Email
                </Label>
                <Input
                  id="forgotEmail"
                  type="email"
                  placeholder="Enter your email"
                  value={forgotPasswordEmail}
                  onChange={(e) => setForgotPasswordEmail(e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              {forgotPasswordMessage && (
                <div className={`text-sm p-3 rounded-lg ${
                  forgotPasswordMessage.includes('sent')
                    ? 'text-green-600 bg-green-50'
                    : 'text-red-600 bg-red-50'
                }`}>
                  {forgotPasswordMessage}
                </div>
              )}
            </DialogBody>

            <DialogFooter className="gap-3 !border-t-0">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowForgotPassword(false);
                  setForgotPasswordEmail('');
                  setForgotPasswordMessage('');
                }}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={forgotPasswordLoading}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
              >
                {forgotPasswordLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  'Send reset link'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

export const AuthPage = memo(AuthPageComponent);
