'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { TextareaWithSuggestions } from '@/components/ui/textarea-with-suggestions';

export function ApiTest() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [textareaValue, setTextareaValue] = useState('Try typing @u to see suggestions. Variables: @username, @user_id, @login_page');

  const testHealthEndpoint = async () => {
    setLoading(true);
    setResult('Testing health endpoint...');
    
    try {
      const response = await fetch('http://localhost:8000/health', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ Health endpoint success: ${JSON.stringify(data)}`);
      } else {
        setResult(`❌ Health endpoint failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      setResult(`❌ Health endpoint error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testApiEndpoint = async () => {
    setLoading(true);
    setResult('Testing API endpoint...');
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: `test${Date.now()}@example.com`,
          password: 'testpassword',
          full_name: 'Test User'
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ API endpoint success: Got token ${data.access_token ? 'YES' : 'NO'}`);
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
        setResult(`❌ API endpoint failed: ${response.status} ${response.statusText} - ${errorData.detail}`);
      }
    } catch (error) {
      setResult(`❌ API endpoint error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testCorsPreflightEndpoint = async () => {
    setLoading(true);
    setResult('Testing CORS preflight...');
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/projects/', {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type,Authorization',
        },
      });
      
      const corsHeaders = {
        'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
        'access-control-allow-credentials': response.headers.get('access-control-allow-credentials'),
      };
      
      setResult(`✅ CORS preflight response: ${JSON.stringify(corsHeaders, null, 2)}`);
    } catch (error) {
      setResult(`❌ CORS preflight error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">API Connection Test</h2>

      <div className="space-y-4 mb-6">
        <Button onClick={testHealthEndpoint} disabled={loading}>
          Test Health Endpoint
        </Button>

        <Button onClick={testCorsPreflightEndpoint} disabled={loading}>
          Test CORS Preflight
        </Button>

        <Button onClick={testApiEndpoint} disabled={loading}>
          Test API Registration
        </Button>
      </div>

      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h3 className="font-semibold mb-2">Result:</h3>
        <pre className="whitespace-pre-wrap text-sm">
          {loading ? 'Loading...' : result || 'Click a button to test'}
        </pre>
      </div>

      <div className="bg-white p-4 rounded-lg border">
        <h3 className="text-xl font-bold mb-4">Textarea with Variable Suggestions Test</h3>
        <p className="text-sm text-gray-600 mb-4">
          Test the @ variable suggestions feature. Try typing:
          <br />• <code>@u</code> (should show suggestions)
          <br />• <code>word@u</code> (should NOT show suggestions - no space before @)
          <br />• <code>@</code> (should NOT show suggestions - need at least one character after @)
          <br />• Hover over existing @username to see the value tooltip
        </p>
        <TextareaWithSuggestions
          projectId={1}
          value={textareaValue}
          onValueChange={setTextareaValue}
          placeholder="Try typing @ followed by a character to see suggestions..."
          className="min-h-32"
        />
      </div>
    </div>
  );
}
