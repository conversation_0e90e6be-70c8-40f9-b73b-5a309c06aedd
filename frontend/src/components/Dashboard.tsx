'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import dynamic from 'next/dynamic';
import { ModalLoadingSpinner, LoadingSpinner } from '@/components/ui/loading-spinner';

// Dynamic imports for modals to reduce initial bundle size
const ProjectCreateModal = dynamic(() => import('@/components/ProjectCreateModal').then(mod => ({ default: mod.ProjectCreateModal })), {
  loading: ModalLoadingSpinner
});

const ProjectEditModal = dynamic(() => import('@/components/ProjectEditModal').then(mod => ({ default: mod.ProjectEditModal })), {
  loading: ModalLoadingSpinner
});

const ProjectDeleteModal = dynamic(() => import('@/components/ProjectDeleteModal').then(mod => ({ default: mod.ProjectDeleteModal })), {
  loading: ModalLoadingSpinner
});

const ProjectShareModal = dynamic(() => import('@/components/ProjectShareModal').then(mod => ({ default: mod.ProjectShareModal })), {
  loading: ModalLoadingSpinner
});
import { Edit, Trash2, Search, Share2, Calendar, FileText, Users, CheckSquare } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { AppHeader } from '@/components/ui/app-header';
import { Project, ProjectListItem } from '@/types';  

// Modern gradient backgrounds for project cards
const getProjectGradient = () => {
  return 'from-white via-purple-50/30 to-purple-100/20';
};

// Modern button styles with better contrast
const getEditButtonColor = () => {
  return 'bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200 hover:border-blue-300';
};

const getDeleteButtonColor = () => {
  return 'bg-red-50 hover:bg-red-100 text-red-700 border border-red-200 hover:border-red-300';
};

const getShareButtonColor = () => {
  return 'bg-emerald-50 hover:bg-emerald-100 text-emerald-700 border border-emerald-200 hover:border-emerald-300';
};

// Modern badge styling
const getDateBadgeColor = () => {
  return 'bg-purple-100 text-purple-700 border border-purple-200';
};

export function Dashboard() {
  const router = useRouter();
  const { user } = useAuth();
  const [projects, setProjects] = useState<ProjectListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingProject, setEditingProject] = useState<ProjectListItem | null>(null);
  const [deletingProject, setDeletingProject] = useState<ProjectListItem | null>(null);
  const [sharingProject, setSharingProject] = useState<ProjectListItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [editModalKey, setEditModalKey] = useState(0);

  const loadProjects = useCallback(async () => {
    try {
      const data = await apiClient.getProjects();
      setProjects(data);
    } catch (error) {
      console.error('Failed to load projects:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  const handleProjectSelect = useCallback((projectListItem: ProjectListItem) => {
    // Navigate to the project page using slug
    router.push(`/projects/${projectListItem.slug}`);
  }, [router]);

  const handleProjectCreated = useCallback((newProject: Project) => {
    // Convert Project to ProjectListItem for the list
    const projectListItem: ProjectListItem = {
      id: newProject.id,
      name: newProject.name,
      slug: newProject.slug,
      description: newProject.description,
      created_at: newProject.created_at,
      member_count: 1, // Creator is the first member
      requirement_count: 0, // New project has no requirements
      test_case_count: 0, // New project has no test cases
      user_role: 'owner' as const, // Creator is always the owner
    };
    setProjects(prev => [...prev, projectListItem]);
    setShowCreateModal(false);
  }, []);

  const handleProjectUpdated = (updatedProject: Project) => {
    // Convert Project to ProjectListItem and update the list
    const projectListItem: ProjectListItem = {
      id: updatedProject.id,
      name: updatedProject.name,
      slug: updatedProject.slug,
      description: updatedProject.description,
      created_at: updatedProject.created_at,
      member_count: projects.find(p => p.id === updatedProject.id)?.member_count || 1,
      requirement_count: projects.find(p => p.id === updatedProject.id)?.requirement_count || 0,
      test_case_count: projects.find(p => p.id === updatedProject.id)?.test_case_count || 0,
      user_role: projects.find(p => p.id === updatedProject.id)?.user_role || 'member',
    };
    setProjects(prev => prev.map(p => p.id === updatedProject.id ? projectListItem : p));
    setEditingProject(null);
  };

  const handleProjectDeleted = (projectId: number) => {
    setProjects(prev => prev.filter(p => p.id !== projectId));
    setDeletingProject(null);
  };

  const handleMembersChanged = useCallback(async (projectId: number) => {
    try {
      // Get updated member count for the specific project
      const members = await apiClient.getProjectMembers(projectId);
      setProjects(prev => prev.map(p =>
        p.id === projectId
          ? { ...p, member_count: members.length }
          : p
      ));
    } catch (error) {
      console.error('Failed to update project member count:', error);
      // Fallback to reloading all projects
      loadProjects();
    }
  }, [loadProjects]);

  // Filter projects based on search query
  const filteredProjects = useMemo(() =>
    projects.filter(project => {
      if (!searchQuery.trim()) return true;
      const query = searchQuery.toLowerCase();
      return project.name.toLowerCase().includes(query);
    }), [projects, searchQuery]
  );



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <AppHeader>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">IntelliTest</h1>
            <p className="text-sm text-gray-500">Welcome back, {user?.full_name || user?.email}</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button onClick={() => setShowCreateModal(true)}>
              New Project
            </Button>
          </div>
        </div>
      </AppHeader>

      {/* Main Content */}
      <main id="main-content" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar - Only show when there are projects */}
        {!loading && projects.length > 0 && (
          <div className="mb-6">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" aria-hidden="true" />
              <Input
                id="project-search"
                type="search"
                placeholder="Search projects by name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white/80 backdrop-blur-sm border-purple-200 focus:border-purple-400 focus:ring-purple-400/20 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg"
                aria-label="Search projects by name"
              />
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="xl" text="Loading projects..." />
          </div>
        ) : projects.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">No projects yet</h3>
            <p className="mt-2 text-gray-500">Get started by creating your first project.</p>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="mt-4"
            >
              Create Project
            </Button>
          </div>
        ) : filteredProjects.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400">
              <Search className="h-24 w-24" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">No projects found</h3>
            <p className="mt-2 text-gray-500">No projects match your search criteria. Try adjusting your search terms.</p>
            <Button
              onClick={() => setSearchQuery('')}
              variant="outline"
              className="mt-4"
            >
              Clear Search
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
              <div
                key={project.id}
                className={`group relative overflow-hidden border border-gray-200/60 shadow-sm hover:shadow-xl hover:shadow-purple-100/50 transition-all duration-300 transform hover:-translate-y-2 bg-gradient-to-br ${getProjectGradient()} cursor-pointer rounded-2xl backdrop-blur-sm`}
                onClick={() => handleProjectSelect(project)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleProjectSelect(project);
                  }
                }}
                role="button"
                tabIndex={0}
                aria-label={`Open project ${project.name}`}
              >
                {/* Subtle gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-purple-100/20 pointer-events-none" />

                {/* Action buttons - Always visible, positioned absolutely */}
                <div className="absolute top-4 right-4 flex space-x-1 z-10">
                  {/* Both owners and members can edit projects */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`h-8 w-8 p-0 rounded-xl transition-all duration-200 backdrop-blur-sm ${getEditButtonColor()}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditModalKey(prev => prev + 1);
                      setEditingProject(project);
                    }}
                    aria-label={`Edit project ${project.name}`}
                  >
                    <Edit className="h-4 w-4" aria-hidden="true" />
                  </Button>
                  {project.user_role === 'owner' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`h-8 w-8 p-0 rounded-xl transition-all duration-200 backdrop-blur-sm ${getDeleteButtonColor()}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeletingProject(project);
                      }}
                      aria-label={`Delete project ${project.name}`}
                    >
                      <Trash2 className="h-4 w-4" aria-hidden="true" />
                    </Button>
                  )}
                  {(project.user_role === 'owner' || project.user_role === 'member') && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`h-8 w-8 p-0 rounded-xl transition-all duration-200 backdrop-blur-sm ${getShareButtonColor()}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSharingProject(project);
                      }}
                      aria-label={`Share project ${project.name}`}
                    >
                      <Share2 className="h-4 w-4" aria-hidden="true" />
                    </Button>
                  )}
                </div>

                <div className="relative p-6">
                  {/* Project Name */}
                  <div className="mb-3 pr-25">
                    <h3 className="text-xl font-bold text-gray-900 line-clamp-2 leading-tight">
                      {project.name}
                    </h3>
                  </div>

                  {/* Description */}
                  <p className="text-sm text-gray-600 mb-6 line-clamp-2 leading-relaxed min-h-[2.5rem]">
                    {project.description || 'No description provided'}
                  </p>

                  {/* Stats Row */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1.5 text-gray-600">
                        <div className="p-1.5 bg-blue-50 rounded-lg">
                          <Users className="h-4 w-4 text-blue-600" />
                        </div>
                        <span className="text-sm font-medium">{project.member_count || 0}</span>
                      </div>
                      <div className="flex items-center space-x-1.5 text-gray-600">
                        <div className="p-1.5 bg-emerald-50 rounded-lg">
                          <FileText className="h-4 w-4 text-emerald-600" />
                        </div>
                        <span className="text-sm font-medium">{project.requirement_count || 0}</span>
                      </div>
                      <div className="flex items-center space-x-1.5 text-gray-600">
                        <div className="p-1.5 bg-orange-50 rounded-lg">
                          <CheckSquare className="h-4 w-4 text-orange-600" />
                        </div>
                        <span className="text-sm font-medium">{project.test_case_count || 0}</span>
                      </div>
                    </div>

                    {/* Date Badge */}
                    <div className={`flex items-center space-x-1.5 px-3 py-1.5 rounded-xl text-xs font-medium ${getDateBadgeColor()}`}>
                      <Calendar className="h-3.5 w-3.5" />
                      <span>{new Date(project.created_at).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                      })}</span>
                    </div>
                  </div>
                </div>

                {/* Hover effect indicator */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
              </div>
            ))}
          </div>
        )}
      </main>

      {/* Project Create Modal */}
      {showCreateModal && (
        <ProjectCreateModal
          onClose={() => setShowCreateModal(false)}
          onProjectCreated={handleProjectCreated}
        />
      )}

      {/* Project Edit Modal */}
      {editingProject && (
        <ProjectEditModal
          key={`edit-${editingProject.id}-${editModalKey}`}
          project={editingProject}
          onClose={() => setEditingProject(null)}
          onProjectUpdated={handleProjectUpdated}
        />
      )}

      {/* Project Delete Modal */}
      {deletingProject && (
        <ProjectDeleteModal
          project={deletingProject}
          onClose={() => setDeletingProject(null)}
          onProjectDeleted={handleProjectDeleted}
        />
      )}

      {/* Project Share Modal */}
      {sharingProject && (
        <ProjectShareModal
          isOpen={!!sharingProject}
          onClose={() => setSharingProject(null)}
          projectId={sharingProject.id}
          projectName={sharingProject.name}
          currentUserRole={sharingProject.user_role}
          onMembersChanged={() => handleMembersChanged(sharingProject.id)}
        />
      )}
    </div>
  );
}
