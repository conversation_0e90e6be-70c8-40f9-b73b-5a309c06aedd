'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle, 
  DialogBody,
  DialogFooter,
} from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { Trash2, Edit, Plus, Variable } from 'lucide-react';

interface ProjectVariable {
  id: number;
  project_id: number;
  key: string;
  value: string;
  description?: string;
  created_at: string;
  updated_at?: string;
}

interface ProjectVariablesModalProps {
  projectId: number;
  projectName: string;
  onClose: () => void;
}

export function ProjectVariablesModal({
  projectId,
  projectName,
  onClose,
}: Readonly<ProjectVariablesModalProps>) {
  const [variables, setVariables] = useState<ProjectVariable[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingVariable, setEditingVariable] = useState<ProjectVariable | null>(null);
  const [deletingVariable, setDeletingVariable] = useState<ProjectVariable | null>(null);
  const [formData, setFormData] = useState({
    key: '',
    value: '',
    description: '',
  });
  const [keyValidationError, setKeyValidationError] = useState('');
  const [valueValidationError, setValueValidationError] = useState('');

  useEffect(() => {
    loadVariables();
  }, [projectId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadVariables = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getProjectVariables(projectId);
      setVariables(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load variables');
    } finally {
      setLoading(false);
    }
  };

  const validateKey = (key: string): string => {
    if (!key.trim()) {
      return 'Key is required';
    }
    if (!/^[a-zA-Z_]/.test(key)) {
      return 'Key must start with a letter or underscore';
    }
    if (!/^[a-zA-Z_]\w*$/.test(key)) {
      return 'Key can only contain letters, numbers, and underscores';
    }
    return '';
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear general error when user starts typing
    if (error) {
      setError('');
    }

    // Validate key field in real-time
    if (field === 'key') {
      const validationError = validateKey(value);
      setKeyValidationError(validationError);
    }

    // Validate value field in real-time
    if (field === 'value') {
      if (!value.trim()) {
        setValueValidationError('Value is required');
      } else {
        setValueValidationError('');
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate key
    const keyError = validateKey(formData.key);
    if (keyError) {
      setKeyValidationError(keyError);
      return;
    }

    // Validate value
    if (!formData.value.trim()) {
      setValueValidationError('Value is required');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      if (editingVariable) {
        const updatedVariable = await apiClient.updateProjectVariable(
          projectId,
          editingVariable.id,
          formData
        );
        setVariables(prev => 
          prev.map(v => v.id === editingVariable.id ? updatedVariable : v)
        );
      } else {
        const newVariable = await apiClient.createProjectVariable(projectId, formData);
        setVariables(prev => [...prev, newVariable]);
      }

      setFormData({ key: '', value: '', description: '' });
      setKeyValidationError('');
      setValueValidationError('');
      setShowCreateForm(false);
      setEditingVariable(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save variable');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (variable: ProjectVariable) => {
    setEditingVariable(variable);
    setFormData({
      key: variable.key,
      value: variable.value,
      description: variable.description || '',
    });
    setKeyValidationError('');
    setValueValidationError('');
    setError('');
    setShowCreateForm(true);
  };



  const handleDelete = (variable: ProjectVariable) => {
    setDeletingVariable(variable);
  };

  const confirmDelete = async () => {
    if (!deletingVariable) return;

    try {
      await apiClient.deleteProjectVariable(projectId, deletingVariable.id);
      setVariables(prev => prev.filter(v => v.id !== deletingVariable.id));
      setDeletingVariable(null);
    } catch (err)
 {
      setError(err instanceof Error ? err.message : 'Failed to delete variable');
    }
  };

  const handleCancel = () => {
    setFormData({ key: '', value: '', description: '' });
    setKeyValidationError('');
    setValueValidationError('');
    setShowCreateForm(false);
    setEditingVariable(null);
    setError('');
  };

  const renderVariablesContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      );
    }

    if (variables.length === 0) {
      return (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No variables yet</h3>
          <p className="text-gray-500 mb-4">Create variables to reference in your requirements using @key syntax.</p>
          <Button
            onClick={() => setShowCreateForm(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create First Variable
          </Button>
        </div>
      );
    }

    return (
      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Key</TableHead>
              <TableHead>Value</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {variables.map((variable) => (
              <TableRow key={variable.id}>
                <TableCell className="font-mono font-medium">
                  @{variable.key}
                </TableCell>
                <TableCell className="max-w-xs truncate">
                  {variable.value}
                </TableCell>
                <TableCell className="max-w-xs truncate">
                  {variable.description || '-'}
                </TableCell>
                <TableCell>
                  {new Date(variable.created_at).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => handleEdit(variable)}
                      aria-label={`Edit variable ${variable.key}`}
                    >
                      <Edit className="h-4 w-4" aria-hidden="true" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={() => handleDelete(variable)}
                      aria-label={`Delete variable ${variable.key}`}
                    >
                      <Trash2 className="h-4 w-4" aria-hidden="true" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="!w-[720px] !max-w-[720px] !h-[85vh] !p-0">
        {/* The DialogContent is already a flex container, so the extra div is removed */}
        
        {/* Sticky Header */}
        <DialogHeader className="px-6 py-6 bg-gray-50 rounded-t-lg flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <Variable className="h-5 w-5" />
            <span>Project Variables</span>
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Manage key-value pairs for {projectName}.
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable Content Area */}
        <DialogBody className="p-6">
          {/* Create/Edit Form */}
          {showCreateForm && (
            <div className="mb-6 p-4 bg-muted border rounded-lg">
              <h3 className="text-lg font-medium mb-4">
                {editingVariable ? 'Edit Variable' : 'Create New Variable'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="key" className="text-sm font-medium">
                      Key *
                    </Label>
                    <Input
                      id="key"
                      placeholder="e.g., api_url, username"
                      value={formData.key}
                      onChange={(e) => handleInputChange('key', e.target.value)}
                      disabled={!!editingVariable}
                      className={keyValidationError ? 'border-red-500 focus:border-red-500' : ''}
                    />
                    {keyValidationError ? (
                      <p className="text-xs text-red-600 font-medium">
                        {keyValidationError}
                      </p>
                    ) : (
                      <p className="text-xs text-gray-500">
                        Use letters, numbers, and underscores only. Start with letter or underscore.
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="value" className="text-sm font-medium">
                      Value *
                    </Label>
                    <Input
                      id="value"
                      placeholder="Variable value"
                      value={formData.value}
                      onChange={(e) => handleInputChange('value', e.target.value)}
                      className={valueValidationError ? 'border-red-500 focus:border-red-500' : ''}
                    />
                    {valueValidationError && (
                      <p className="text-xs text-red-600 font-medium">
                        {valueValidationError}
                      </p>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium">
                    Description (Optional)
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="Describe what this variable is used for"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={2}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={handleCancel}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : (editingVariable ? 'Update Variable' : 'Create Variable')}
                  </Button>
                </div>
              </form>
            </div>
          )}

          {/* Variables List (now conditional) */}
          {!showCreateForm && (
              <div className="space-y-4">
                  <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">Variables ({variables.length})</h3>
                      {variables.length > 0 && (
                          <Button onClick={() => setShowCreateForm(true)}>
                              <Plus className="h-4 w-4 mr-2" />
                              Add Variable
                          </Button>
                      )}
                  </div>
                  {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                          <strong>Error:</strong> {error}
                      </div>
                  )}
                  {renderVariablesContent()}
              </div>
          )}
        </DialogBody>


        {/* Sticky Bottom Panel */}
        <DialogFooter className="bg-gray-50 flex-shrink-0">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
        </DialogFooter>
      </DialogContent>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        open={!!deletingVariable}
        onOpenChange={(open) => !open && setDeletingVariable(null)}
        title="Delete Variable"
        description={`Are you sure you want to delete the variable "${deletingVariable?.key}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={confirmDelete}
      />
    </Dialog>
  );
}