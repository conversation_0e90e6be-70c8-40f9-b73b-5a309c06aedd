'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { useToast } from '@/hooks/useToast';
import { Button } from '@/components/ui/button';

import { ColoredTag } from '@/components/ui/colored-tag';

import { RequirementCreateModal } from '@/components/RequirementCreateModal';
import { RequirementDeleteModal } from '@/components/RequirementDeleteModal';
import { ProjectVariablesModal } from '@/components/ProjectVariablesModal';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Trash2, Edit, Search, ChevronUp, ChevronDown, Ch<PERSON><PERSON><PERSON><PERSON><PERSON>, ChevronRight } from 'lucide-react';
import { AppHeader } from '@/components/ui/app-header';
import { ToastContainer } from '@/components/ui/toast';

interface Project {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  code_source?: string | null;
  automation_framework?: string | null;
  programming_language?: string | null;
  created_at: string;
  updated_at?: string;
}

interface Tag {
  id: number;
  name: string;
  color?: string;
}

interface RequirementListItem {
  id: number;
  name: string;
  slug: string;
  created_at: string;
  created_by: number;
  created_by_name?: string;
  tags: Tag[];
  test_case_count: number;
}



interface ProjectViewProps {
  project: Project;
  onBack: () => void;
}

export function ProjectView({ project, onBack }: Readonly<ProjectViewProps>) {
  const router = useRouter();
  const { toasts, showToast, removeToast } = useToast();
  const [requirements, setRequirements] = useState<RequirementListItem[]>([]);
  const [loading, setLoading] = useState(true);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [deletingRequirement, setDeletingRequirement] = useState<RequirementListItem | null>(null);
  const [showVariablesModal, setShowVariablesModal] = useState(false);

  // Search, sort, and pagination state
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<'name' | 'created_at' | 'created_by' | 'test_case_count'>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  useEffect(() => {
    loadRequirements();
  }, [project.id]); // eslint-disable-line react-hooks/exhaustive-deps

  // Filter and sort requirements
  const filteredAndSortedRequirements = React.useMemo(() => {
    let filtered = requirements;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = requirements.filter(req =>
        req.name.toLowerCase().includes(query) ||
        req.tags.some(tag => tag.name.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: string | number | Date, bValue: string | number | Date;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'created_by':
          aValue = a.created_by_name || `User ${a.created_by}`;
          bValue = b.created_by_name || `User ${b.created_by}`;
          break;
        case 'test_case_count':
          aValue = a.test_case_count;
          bValue = b.test_case_count;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [requirements, searchQuery, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedRequirements.length / itemsPerPage);
  const paginatedRequirements = filteredAndSortedRequirements.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Reset to first page when search or items per page changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, itemsPerPage]);

  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: typeof sortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ChevronUp className="h-4 w-4" /> :
      <ChevronDown className="h-4 w-4" />;
  };

  const renderRequirementsContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      );
    }

    if (requirements.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="mx-auto h-16 w-16 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No requirements yet</h3>
          <p className="mt-2 text-gray-500">Start by creating your first requirement.</p>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="mt-4"
          >
            Create Requirement
          </Button>
        </div>
      );
    }

    if (filteredAndSortedRequirements.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="mx-auto h-16 w-16 text-gray-400">
            <Search className="h-16 w-16" />
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No requirements found</h3>
          <p className="mt-2 text-gray-500">Try adjusting your search query or clear the search to see all requirements.</p>
          <Button
            variant="outline"
            onClick={() => setSearchQuery('')}
            className="mt-4"
          >
            Clear Search
          </Button>
        </div>
      );
    }

    return (
      <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50/50 border-b-2 border-gray-200">
              <TableHead
                className="cursor-pointer hover:bg-gray-100 select-none font-semibold text-gray-700 uppercase text-xs tracking-wide py-4"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center space-x-1">
                  <span>REQUIREMENT NAME</span>
                  {getSortIcon('name')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-100 select-none font-semibold text-gray-700 uppercase text-xs tracking-wide py-4"
                onClick={() => handleSort('created_at')}
              >
                <div className="flex items-center space-x-1">
                  <span>CREATED DATE</span>
                  {getSortIcon('created_at')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-100 select-none font-semibold text-gray-700 uppercase text-xs tracking-wide py-4"
                onClick={() => handleSort('created_by')}
              >
                <div className="flex items-center space-x-1">
                  <span>CREATED BY</span>
                  {getSortIcon('created_by')}
                </div>
              </TableHead>
              <TableHead className="font-semibold text-gray-700 uppercase text-xs tracking-wide py-4">TAGS</TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-100 select-none font-semibold text-gray-700 uppercase text-xs tracking-wide py-4"
                onClick={() => handleSort('test_case_count')}
              >
                <div className="flex items-center space-x-1">
                  <span>TEST CASES</span>
                  {getSortIcon('test_case_count')}
                </div>
              </TableHead>
              <TableHead className="w-[100px] font-semibold text-gray-700 uppercase text-xs tracking-wide py-4">ACTIONS</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedRequirements.map((requirement, index) => (
              <TableRow
                key={requirement.id}
                onDoubleClick={(e) => {
                  // Don't trigger if double-clicking on action buttons
                  if (!(e.target as HTMLElement).closest('[data-action-buttons]')) {
                    router.push(`/projects/${project.slug}/requirements/${requirement.slug}`);
                  }
                }}
                className={`cursor-pointer transition-colors hover:bg-purple-50/50 ${
                  index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                }`}
              >
                <TableCell className="py-4">
                  <button
                    onClick={() => window.location.href = `/projects/${project.slug}/requirements/${requirement.slug}`}
                    className="font-medium cursor-pointer text-primary hover:text-primary/80 transition-colors"
                  >
                    {requirement.name}
                  </button>
                </TableCell>
                <TableCell className="py-4 text-gray-600 text-sm">
                  {new Date(requirement.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </TableCell>
                <TableCell className="py-4 text-gray-600 text-sm">
                  {requirement.created_by_name || `User ${requirement.created_by}`}
                </TableCell>
                <TableCell className="py-4">
                  <div className="flex flex-wrap gap-1">
                    {requirement.tags.map((tag) => (
                      <ColoredTag
                        key={tag.id}
                        name={tag.name}
                        variant="outline"
                        size="sm"
                      />
                    ))}
                  </div>
                </TableCell>
                <TableCell className="py-4 text-center">
                  <span className="inline-flex items-center justify-center w-8 h-8 text-sm font-medium text-gray-700 bg-gray-100 rounded-full">
                    {requirement.test_case_count}
                  </span>
                </TableCell>
                <TableCell className="py-4">
                  <div className="flex space-x-1 justify-center" data-action-buttons>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full hover:bg-purple-100 transition-colors"
                      onClick={() => window.location.href = `/projects/${project.slug}/requirements/${requirement.slug}`}
                      aria-label={`Edit requirement ${requirement.name}`}
                    >
                      <Edit className="h-4 w-4" aria-hidden="true" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full text-red-600 hover:text-red-700 hover:bg-red-100 transition-colors"
                      onClick={() => setDeletingRequirement(requirement)}
                      aria-label={`Delete requirement ${requirement.name}`}
                    >
                      <Trash2 className="h-4 w-4" aria-hidden="true" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-gray-500">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredAndSortedRequirements.length)} of {filteredAndSortedRequirements.length} results
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(pageNum)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  const loadRequirements = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getRequirements(project.id);
      setRequirements(data);
    } catch (error) {
      console.error('Failed to load requirements:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load requirements';

      // If it's a permission error, redirect to dashboard
      if (errorMessage.includes('permission') || errorMessage.includes('forbidden')) {
        showToast('You do not have permission to access this project.', 'error');
        setTimeout(() => {
          router.push('/');
        }, 2000);
      } else {
        // For other errors, just show the error message
        setRequirements([]);
        showToast(errorMessage, 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRequirementCreated = async (newRequirement: { id: number; name: string; slug: string; created_at: string; created_by: number; tags?: Tag[] }) => {
    try {
      // Get current user info to populate created_by_name
      const currentUser = await apiClient.getCurrentUser();

      // Convert to RequirementListItem format
      const requirementListItem: RequirementListItem = {
        id: newRequirement.id,
        name: newRequirement.name,
        slug: newRequirement.slug,
        created_at: newRequirement.created_at,
        created_by: newRequirement.created_by,
        created_by_name: currentUser.full_name, // Add the user's name
        tags: newRequirement.tags || [],
        test_case_count: 0 // New requirement has no test cases
      };
      setRequirements([...requirements, requirementListItem]);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Failed to get current user info:', error);
      // Fallback: add requirement without created_by_name
      const requirementListItem: RequirementListItem = {
        id: newRequirement.id,
        name: newRequirement.name,
        slug: newRequirement.slug,
        created_at: newRequirement.created_at,
        created_by: newRequirement.created_by,
        tags: newRequirement.tags || [],
        test_case_count: 0
      };
      setRequirements([...requirements, requirementListItem]);
      setShowCreateModal(false);
    }
  };

  const handleRequirementDeleted = (requirementId: number) => {
    setRequirements(prev => prev.filter(r => r.id !== requirementId));
    setDeletingRequirement(null);
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <AppHeader>
        <div className="flex justify-between items-center">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
            <Breadcrumb
              items={[
                { label: 'Dashboard', onClick: onBack },
                { label: project.name, current: true }
              ]}
              className="mt-1"
            />
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowVariablesModal(true)}
              className="flex items-center space-x-2"
            >
              <span>⚙️ Variables</span>
            </Button>
            <Button
              onClick={() => setShowCreateModal(true)}
            >
              New Requirement
            </Button>
          </div>
        </div>
      </AppHeader>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Requirements</h2>
            <div className="text-sm text-gray-500">
              {filteredAndSortedRequirements.length} of {requirements.length} requirements
            </div>
          </div>

          {/* Search and Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
              <Input
                placeholder="Search requirements by name or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white/80 backdrop-blur-sm border-purple-200 focus:border-purple-400 focus:ring-purple-400/20 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg"
              />
            </div>
            <div className="flex gap-2">
              <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {renderRequirementsContent()}

        </div>
      </main>

      {/* Requirement Create Modal */}
      {showCreateModal && (
        <RequirementCreateModal
          projectId={project.id}
          onClose={() => setShowCreateModal(false)}
          onRequirementCreated={handleRequirementCreated}
        />
      )}

      {/* Requirement Delete Modal */}
      {deletingRequirement && (
        <RequirementDeleteModal
          requirement={deletingRequirement}
          onClose={() => setDeletingRequirement(null)}
          onRequirementDeleted={handleRequirementDeleted}
        />
      )}

      {showVariablesModal && (
        <ProjectVariablesModal
          projectId={project.id}
          projectName={project.name}
          onClose={() => setShowVariablesModal(false)}
        />
      )}

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}
