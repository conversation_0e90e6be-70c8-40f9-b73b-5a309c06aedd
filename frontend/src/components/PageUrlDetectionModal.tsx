'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogDescription, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { Trash2, Plus, Play, RefreshCw, FileCode } from 'lucide-react';
import { parseVariableReferences, getVariableByName } from '@/lib/variable-utils';
import { apiClient } from '@/lib/api';
import CodeGenerationModal from './CodeGenerationModal';

interface ProjectVariable {
  id: number;
  key: string;
  value: string;
  description?: string;
}

interface TestCase {
  id: number;
  custom_id: string;
  title: string;
  steps?: string;
  expected_result?: string;
  notes?: string;
}

interface PageUrl {
  id: string;
  variableKey: string;
  url: string;
  needsLogin: boolean;
  username?: string;
  password?: string;
  crawlStatus: 'pending' | 'crawling' | 'success' | 'failed';
  isExistingVariable?: boolean; // Track if this variable already exists in project
  crawlData?: {
    url: string;
    elements: Array<{
      tag: string;
      selector: string;
      text: string;
    }>;
  };
}

interface PageUrlDetectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  testCases: TestCase[];
  projectId: number;
  project?: {
    id: number;
    name: string;
    automation_framework?: string | null;
    programming_language?: string | null;
    code_source?: string | null;
  };
  requirementId?: number;
}

export function PageUrlDetectionModal({
  isOpen,
  onClose,
  testCases,
  projectId,
  project: projectProp,
  requirementId: requirementIdProp
}: Readonly<PageUrlDetectionModalProps>) {
  const [pageUrls, setPageUrls] = useState<PageUrl[]>([]);
  const [loading, setLoading] = useState(true);
  const [crawling, setCrawling] = useState(false);
  const [showCodeGeneration, setShowCodeGeneration] = useState(false);

  // Enhanced URL detection function that finds URLs in various formats
  const detectAllUrls = useCallback((text: string): string[] => {
    const urls: string[] = [];
    const processedUrls = new Set<string>();

    // Comprehensive URL regex patterns
    const urlPatterns = [
      // Full URLs with protocol
      /https?:\/\/(?:[-\w.])+(?::[0-9]+)?(?:\/(?:[\w/_.])*)?(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?/gi,
      // URLs without protocol but with www
      /(?:^|\s)(www\.(?:[-\w.])+(?::[0-9]+)?(?:\/(?:[\w/_.])*)?(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)/gi,
      // Domain-only URLs (e.g., google.com, example.org)
      /(?:^|\s)((?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?::[0-9]+)?(?:\/(?:[\w/_.])*)?(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)/gi
    ];

    urlPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        let url = match[1] || match[0];
        url = url.trim();

        // Skip if already processed
        if (processedUrls.has(url)) continue;

        // Normalize URL for validation
        let normalizedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          normalizedUrl = 'https://' + url;
        }

        // Validate URL
        try {
          new URL(normalizedUrl);
          urls.push(url);
          processedUrls.add(url);
        } catch {
          // Invalid URL, skip
        }
      }
    });

    return urls;
  }, []);

  const detectUrlVariables = useCallback((testCases: TestCase[], variables: ProjectVariable[]): ProjectVariable[] => {
    const urlVariables: ProjectVariable[] = [];
    const processedKeys = new Set<string>();

    // Combine all test case text
    const allText = testCases.map(tc =>
      [tc.title, tc.steps, tc.expected_result, tc.notes].filter(Boolean).join(' ')
    ).join(' ');

    // Find all variable references
    const variableRefs = parseVariableReferences(allText);

    for (const ref of variableRefs) {
      if (processedKeys.has(ref.variableName)) continue;

      const variable = getVariableByName(ref.variableName, variables);
      if (variable && isUrl(variable.value)) {
        urlVariables.push(variable);
        processedKeys.add(ref.variableName);
      }
    }

    return urlVariables;
  }, []);

  // Smart page naming function
  const generateSmartPageName = useCallback((url: string): string => {
    try {
      // Normalize URL for parsing
      let normalizedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        normalizedUrl = 'https://' + url;
      }

      const urlObj = new URL(normalizedUrl);
      let pathname = urlObj.pathname;

      // Remove trailing slash
      if (pathname.endsWith('/') && pathname.length > 1) {
        pathname = pathname.slice(0, -1);
      }

      // Split path into segments and get the last meaningful segment
      const segments = pathname.split('/').filter(segment => segment.length > 0);

      if (segments.length === 0) {
        // No path segments, use domain
        const domain = urlObj.hostname.replace('www.', '');
        const domainParts = domain.split('.');
        const mainDomain = domainParts[0];
        return `${mainDomain}_Page`;
      }

      // Get the last segment
      const lastSegment = segments[segments.length - 1];

      // Handle URL parameters - check if the last segment contains query params
      if (urlObj.search) {
        // If we have query parameters, use the last path segment before params
        // The lastSegment should already be clean since URL constructor separates pathname and search
      }

      // Clean the segment for variable naming
      let cleanSegment = lastSegment
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric characters
        .replace(/^\d+/, ''); // Remove leading numbers

      // If the cleaned segment is empty or too short, try the second-to-last segment
      if (cleanSegment.length < 2 && segments.length > 1) {
        const secondLastSegment = segments[segments.length - 2];
        cleanSegment = secondLastSegment
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '')
          .replace(/^\d+/, '');
      }

      // If still not meaningful, use domain-based naming
      if (cleanSegment.length < 2) {
        const domain = urlObj.hostname.replace('www.', '');
        const domainParts = domain.split('.');
        const mainDomain = domainParts[0];
        cleanSegment = mainDomain.toLowerCase().replace(/[^a-z0-9]/g, '');
      }

      // Ensure it starts with a letter (variable naming requirement)
      if (cleanSegment && /^\d/.test(cleanSegment)) {
        cleanSegment = 'page' + cleanSegment;
      }

      // If we have a meaningful segment, use it
      if (cleanSegment && cleanSegment.length >= 2) {
        return `${cleanSegment}_Page`;
      }

      // Fallback to generic naming
      return 'Page_001'; // This will be handled by the counter logic
    } catch (error) {
      console.error('Error generating smart page name:', error);
      return 'Page_001'; // Fallback to generic naming
    }
  }, []);

  const loadVariablesAndDetectUrls = useCallback(async () => {
    try {
      setLoading(true);
      const variablesData = await apiClient.getProjectVariables(projectId);

      // Combine all test case text
      const allText = testCases.map(tc =>
        [tc.title, tc.steps, tc.expected_result, tc.notes].filter(Boolean).join(' ')
      ).join(' ');

      // Detect URL variables from test cases (variables with URL values)
      const urlVariables = detectUrlVariables(testCases, variablesData);

      // Detect standalone URLs in test cases
      const standaloneUrls = detectAllUrls(allText);

      // Create a map of existing variable URLs for intelligent matching
      const variableUrlMap = new Map<string, ProjectVariable>();
      variablesData.forEach(variable => {
        if (isUrl(variable.value)) {
          // Normalize URLs for comparison
          let normalizedValue = variable.value;
          if (!normalizedValue.startsWith('http://') && !normalizedValue.startsWith('https://')) {
            normalizedValue = 'https://' + normalizedValue;
          }
          variableUrlMap.set(normalizedValue, variable);
        }
      });

      const pageUrlsFromVariables = urlVariables.map((variable, index) => ({
        id: `var-${index}`,
        variableKey: variable.key,
        url: variable.value,
        needsLogin: false,
        crawlStatus: 'pending' as const,
        isExistingVariable: true // Mark as existing variable
      }));

      // Process standalone URLs
      const pageUrlsFromStandalone: PageUrl[] = [];
      let pageCounter = 1;
      const usedPageNames = new Set<string>();

      // Collect existing page names to avoid duplicates
      pageUrlsFromVariables.forEach(pageUrl => {
        usedPageNames.add(pageUrl.variableKey.toLowerCase());
      });
      variablesData.forEach(variable => {
        usedPageNames.add(variable.key.toLowerCase());
      });

      standaloneUrls.forEach((url, index) => {
        // Normalize URL for comparison
        let normalizedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          normalizedUrl = 'https://' + url;
        }

        // Check if this URL matches any existing variable
        const matchingVariable = variableUrlMap.get(normalizedUrl);

        if (matchingVariable) {
          // Check if we already have this variable in our list
          const alreadyExists = pageUrlsFromVariables.some(pageUrl => pageUrl.variableKey === matchingVariable.key);
          if (!alreadyExists) {
            pageUrlsFromStandalone.push({
              id: `standalone-${index}`,
              variableKey: matchingVariable.key,
              url: url,
              needsLogin: false,
              crawlStatus: 'pending' as const,
              isExistingVariable: true // Mark as existing variable
            });
          }
        } else {
          // Generate smart page name
          let pageKey = generateSmartPageName(url);

          // If the smart name is the fallback or already used, use counter-based naming
          if (pageKey === 'Page_001' || usedPageNames.has(pageKey.toLowerCase())) {
            do {
              pageKey = `Page_${pageCounter.toString().padStart(3, '0')}`;
              pageCounter++;
            } while (usedPageNames.has(pageKey.toLowerCase()));
          }

          // Add to used names
          usedPageNames.add(pageKey.toLowerCase());

          pageUrlsFromStandalone.push({
            id: `standalone-${index}`,
            variableKey: pageKey,
            url: url,
            needsLogin: false,
            crawlStatus: 'pending' as const,
            isExistingVariable: false // Mark as new variable
          });
        }
      });

      // Combine both lists
      const allPageUrls = [...pageUrlsFromVariables, ...pageUrlsFromStandalone];
      setPageUrls(allPageUrls);

    } catch (error) {
      console.error('Failed to load variables:', error);
    } finally {
      setLoading(false);
    }
  }, [testCases, projectId, detectUrlVariables, detectAllUrls, generateSmartPageName]);

  useEffect(() => {
    if (isOpen) {
      loadVariablesAndDetectUrls();
    }
  }, [isOpen, loadVariablesAndDetectUrls]);



  const isUrl = (text: string): boolean => {
    try {
      new URL(text);
      return text.startsWith('http://') || text.startsWith('https://');
    } catch {
      return false;
    }
  };

  const addNewPageUrl = () => {
    const newId = `url-${Date.now()}`;
    setPageUrls(prev => [...prev, {
      id: newId,
      variableKey: '',
      url: '',
      needsLogin: false,
      crawlStatus: 'pending',
      isExistingVariable: false // Mark manually added URLs as new
    }]);
  };

  const updatePageUrl = (id: string, updates: Partial<PageUrl>) => {
    setPageUrls(prev => prev.map(page => 
      page.id === id ? { ...page, ...updates } : page
    ));
  };

  const removePageUrl = (id: string) => {
    setPageUrls(prev => prev.filter(page => page.id !== id));
  };

  const startCrawling = async () => {
    setCrawling(true);

    try {
      // First, save any new variables that don't exist in project variables
      const existingVariables = await apiClient.getProjectVariables(projectId);
      const existingVariableKeys = new Set(existingVariables.map(v => v.key));

      const newVariables = pageUrls.filter(page =>
        page.variableKey &&
        page.variableKey.trim() !== '' &&
        !existingVariableKeys.has(page.variableKey)
      );

      // Save new variables to project
      for (const page of newVariables) {
        try {
          await apiClient.createProjectVariable(projectId, {
            key: page.variableKey,
            value: page.url,
            description: `Auto-generated from page URL detection`
          });
          console.log(`Created project variable: ${page.variableKey} = ${page.url}`);
        } catch (error) {
          console.error(`Failed to create project variable ${page.variableKey}:`, error);
          // Continue with crawling even if variable creation fails
        }
      }

      // Update all pages to crawling status
      setPageUrls(prev => prev.map(page => ({ ...page, crawlStatus: 'crawling' as const })));

      // Create array of crawl promises for concurrent execution (max 10)
      const crawlPromises = pageUrls.map(async (page) => {
        try {
          const crawlRequest = {
            url: page.url,
            is_login_needed: page.needsLogin,
            username: page.needsLogin ? page.username : undefined,
            password: page.needsLogin ? page.password : undefined,
            skip_hidden: true,
            max_elements: 1000,
            timeout: 30000
          };

          const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
          const response = await fetch(`${API_BASE_URL}/crawler/crawl`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(crawlRequest)
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const result = await response.json() as {
            url: string;
            elements: Array<{
              tag: string;
              selector: string;
              text: string;
            }>;
          };

          // Transform the result to match our expected format
          const crawlData = {
            url: result.url,
            elements: result.elements.map((el) => ({
              tag: el.tag,
              selector: el.selector,
              text: el.text
            }))
          };

          updatePageUrl(page.id, {
            crawlStatus: 'success',
            crawlData
          });
        } catch (error) {
          console.error(`Crawling failed for ${page.url}:`, error);
          updatePageUrl(page.id, { crawlStatus: 'failed' });
        }
      });

      // Execute up to 10 concurrent crawls
      const batchSize = 10;
      for (let i = 0; i < crawlPromises.length; i += batchSize) {
        const batch = crawlPromises.slice(i, i + batchSize);
        await Promise.allSettled(batch);
      }
    } catch (error) {
      console.error('Error during crawling process:', error);
    } finally {
      setCrawling(false);
    }
  };

  const retryCrawl = async (pageId: string) => {
    const page = pageUrls.find(p => p.id === pageId);
    if (!page) return;

    updatePageUrl(pageId, { crawlStatus: 'crawling' });

    try {
      const crawlRequest = {
        url: page.url,
        is_login_needed: page.needsLogin,
        username: page.needsLogin ? page.username : undefined,
        password: page.needsLogin ? page.password : undefined,
        skip_hidden: true,
        max_elements: 1000,
        timeout: 30000
      };

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
      const response = await fetch(`${API_BASE_URL}/crawler/crawl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(crawlRequest)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as {
        url: string;
        elements: Array<{
          tag: string;
          selector: string;
          text: string;
        }>;
      };

      const crawlData = {
        url: result.url,
        elements: result.elements.map((el) => ({
          tag: el.tag,
          selector: el.selector,
          text: el.text
        }))
      };

      updatePageUrl(pageId, {
        crawlStatus: 'success',
        crawlData
      });
    } catch (error) {
      console.error(`Retry crawling failed for ${page.url}:`, error);
      updatePageUrl(pageId, { crawlStatus: 'failed' });
    }
  };



  const getCrawlStatusIcon = (status: PageUrl['crawlStatus']) => {
    switch (status) {
      case 'crawling':
        return <RefreshCw className="h-4 w-4 mt-4 animate-spin text-blue-500" />;
      case 'success':
        return <div className="h-4 w-4 mt-4 rounded-full bg-green-500 flex items-center justify-center">
          <span className="text-white text-xs">✓</span>
        </div>;
      case 'failed':
        return <div className="h-4 w-4 mt-4 rounded-full bg-red-500 flex items-center justify-center">
          <span className="text-white text-xs">✗</span>
        </div>;
      default:
        return <div className="h-4 w-4 mt-4 rounded-full bg-gray-300" />;
    }
  };

  const hasSuccessfulCrawls = pageUrls.some(page => page.crawlStatus === 'success');

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader className="sr-only">
            <DialogTitle>Loading Content</DialogTitle>
            <DialogDescription>
              Please wait while we detect page URLs from your test cases.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
  <DialogContent className="!max-w-3xl">
    <DialogHeader>
      <DialogTitle>Page URL Detection & Crawling</DialogTitle>
      <DialogDescription>
        Page URLs detected in the test cases
      </DialogDescription>
    </DialogHeader>
    
    <DialogBody>
      <div className="space-y-6">
        <div className={`border rounded-lg p-4 ${pageUrls.length > 0 ? 'bg-blue-50 border-blue-200' : 'bg-orange-50 border-orange-200'}`}>
          <p className={`text-sm ${pageUrls.length > 0 ? 'text-blue-800' : 'text-orange-800'}`}>
            {pageUrls.length > 0
              ? "Please review and confirm if these pages are accurate and related to this requirement. You can add additional pages if needed. Once confirmed, we'll crawl these pages for further processing."
              : "No page URLs found in the test cases. To generate automation code, please add the relevant page URLs so we can crawl and identify the necessary elements."
            }
          </p>
        </div>

        {pageUrls.length > 0 ? (
          <div className="space-y-4">
            {pageUrls.map((page) => (
              <Card key={page.id} className="p-4">
                <CardContent className="space-y-4 p-0">
                  <div className="flex items-center space-x-4">
                    {getCrawlStatusIcon(page.crawlStatus)}

                    <div className="flex-1 grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`key-${page.id}`}>
                          Variable Key
                          {page.isExistingVariable && (
                            <span className="text-xs text-gray-500 ml-2">(existing variable)</span>
                          )}
                        </Label>
                        <Input
                          id={`key-${page.id}`}
                          className={`mt-1 ${page.isExistingVariable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                          value={page.variableKey}
                          onChange={(e) => updatePageUrl(page.id, { variableKey: e.target.value })}
                          placeholder="e.g., login_page_url"
                          readOnly={page.isExistingVariable}
                          disabled={page.isExistingVariable}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`url-${page.id}`}>URL</Label>
                        <Input
                          id={`url-${page.id}`}
                          className="mt-1"
                          value={page.url}
                          onChange={(e) => updatePageUrl(page.id, { url: e.target.value })}
                          placeholder="https://example.com/login"
                        />
                      </div>
                    </div>

                    <div className="flex items-center mt-4 space-x-2">
                      {page.crawlStatus === 'failed' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => retryCrawl(page.id)}
                          className="text-blue-600 border-blue-600 hover:bg-blue-100"
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          Retry
                        </Button>
                      )}

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removePageUrl(page.id)}
                        className="text-red-600 border-red-600 hover:bg-red-100"
                        aria-label={`Remove page URL ${page.url || 'entry'}`}
                      >
                        <Trash2 className="h-3 w-3" aria-hidden="true" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`login-${page.id}`}
                        checked={page.needsLogin}
                        onCheckedChange={(checked) =>
                          updatePageUrl(page.id, { needsLogin: checked as boolean })
                        }
                      />
                      <Label htmlFor={`login-${page.id}`}>This page needs login to be crawled</Label>
                    </div>
                  </div>

                  {page.needsLogin && (
                    <div className="grid grid-cols-2 gap-4 pl-6 border-l-2 border-gray-200">
                      <div>
                        <Label htmlFor={`username-${page.id}`}>Username</Label>
                        <Input
                          id={`username-${page.id}`}
                          className="mt-1"
                          value={page.username || ''}
                          onChange={(e) => updatePageUrl(page.id, { username: e.target.value })}
                          placeholder="Enter username"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`password-${page.id}`}>Password</Label>
                        <Input
                          id={`password-${page.id}`}
                          className="mt-1"
                          type="password"
                          value={page.password || ''}
                          onChange={(e) => updatePageUrl(page.id, { password: e.target.value })}
                          placeholder="Enter password"
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No page URLs detected in test cases.</p>
            <p className="text-sm text-gray-400">Click &quot;Add Page URL&quot; below to manually add page URLs for crawling.</p>
          </div>
        )}
      </div>
    </DialogBody>

    <DialogFooter>
      <Button
        variant="outline"
        onClick={addNewPageUrl}
        className="border-primary text-primary hover:bg-primary hover:text-white"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add Page URL
      </Button>

      <div className="flex space-x-2">
        {hasSuccessfulCrawls && (
          <Button
            variant="outline"
            onClick={() => setShowCodeGeneration(true)}
            className="bg-secondary text-white hover:text-white hover:bg-[#014D4E]"
            disabled={!projectProp || !requirementIdProp}
          >
            <FileCode className="h-4 w-4 mr-2" />
            Generate Code
          </Button>
        )}
        
        <Button
          onClick={startCrawling}
          disabled={crawling || pageUrls.length === 0}
          className="bg-primary hover:bg-primary/90"
        >
          <Play className="h-4 w-4 mr-2" />
          {crawling ? 'Crawling...' : 'Start Crawling'}
        </Button>
      </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>

      {/* Code Generation Modal */}
      {projectProp && requirementIdProp && (
        <CodeGenerationModal
          isOpen={showCodeGeneration}
          onClose={() => setShowCodeGeneration(false)}
          project={projectProp}
          requirementId={requirementIdProp}
          testCases={testCases}
          crawlData={pageUrls.reduce((acc, pageUrl) => {
            if (pageUrl.crawlData) {
              acc[pageUrl.url] = {
                url: pageUrl.url,
                elements: pageUrl.crawlData.elements || []
              };
            }
            return acc;
          }, {} as Record<string, { url: string; elements: Array<{ tag: string; selector: string; text: string }> }>)}
        />
      )}
    </>
  );
}
