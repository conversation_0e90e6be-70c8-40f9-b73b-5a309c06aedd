import * as React from "react";
import { cn } from "@/lib/utils";
import { getTagColor, getContrastTextColor, hexToRgba } from "@/lib/tag-colors";

interface ColoredTagProps {
  name: string;
  className?: string;
  variant?: 'default' | 'outline' | 'solid';
  size?: 'sm' | 'md' | 'lg';
  removable?: boolean;
  onRemove?: () => void;
}

export function ColoredTag({ 
  name, 
  className, 
  variant = 'default',
  size = 'md',
  removable = false,
  onRemove 
}: ColoredTagProps) {
  const tagColor = getTagColor(name);
  const textColor = getContrastTextColor(tagColor);
  const bgColor = variant === 'solid' ? tagColor : hexToRgba(tagColor, 0.1);
  const borderColor = variant === 'outline' ? tagColor : 'transparent';

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-1.5 text-base'
  };

  const style = {
    backgroundColor: bgColor,
    color: variant === 'solid' ? textColor : tagColor,
    borderColor: borderColor,
  };

  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full font-medium border",
        sizeClasses[size],
        variant === 'outline' && "border-current",
        variant !== 'outline' && "border-transparent",
        className
      )}
      style={style}
    >
      {name}
      {removable && onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="ml-1.5 hover:opacity-70 focus:outline-none"
          style={{ color: variant === 'solid' ? textColor : tagColor }}
          aria-label={`Remove ${name} tag`}
        >
          ×
        </button>
      )}
    </span>
  );
}
