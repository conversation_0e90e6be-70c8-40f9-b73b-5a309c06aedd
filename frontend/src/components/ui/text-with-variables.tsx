import * as React from "react";
import { useState, useEffect, useMemo } from "react";
import { parseVariableReferences } from "@/lib/variable-utils";
import { apiClient } from "@/lib/api";

interface ProjectVariable {
  id: number;
  key: string;
  value: string;
  description?: string;
}

interface TextWithVariablesProps {
  text: string;
  projectId: number;
  className?: string;
}

export function TextWithVariables({ text, projectId, className = "" }: Readonly<TextWithVariablesProps>) {
  const [variables, setVariables] = useState<ProjectVariable[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadVariables = async () => {
      try {
        setLoading(true);
        const data = await apiClient.getProjectVariables(projectId);
        setVariables(data);
      } catch (error) {
        console.error('Failed to load project variables:', error);
      } finally {
        setLoading(false);
      }
    };

    if (projectId) {
      loadVariables();
    }
  }, [projectId]);

  const variablesMap = useMemo(() => {
    return new Map(variables.map(variable => [variable.key, variable]));
  }, [variables]);

  const renderedContent = useMemo(() => {
    if (loading) {
      return text;
    }

    const variableRefs = parseVariableReferences(text, variables);

    if (variableRefs.length === 0) {
      return text;
    }

    const parts = [];
    let lastIndex = 0;

    variableRefs.forEach((ref, index) => {
      if (ref.start > lastIndex) {
        parts.push(text.slice(lastIndex, ref.start));
      }

      const variable = variablesMap.get(ref.variableName);
      const tooltipText = variable
        ? `${variable.key}: ${variable.value}${variable.description ? ` (${variable.description})` : ''}`
        : `Variable: ${ref.variableName} (not found)`;

      parts.push(
        <span
          key={`var-${index}`}
          className="inline-block px-2 py-0.5 mx-0.5 bg-gray-100 text-gray-700 rounded text-sm font-medium cursor-help border border-gray-200 hover:bg-gray-200 transition-colors"
          title={tooltipText}
        >
          {ref.match}
        </span>
      );

      lastIndex = ref.end;
    });

    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  }, [text, variables, variablesMap, loading]);

  return <span className={className}>{renderedContent}</span>;
}