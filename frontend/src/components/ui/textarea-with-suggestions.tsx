import * as React from "react";
import { useState, useRef, useCallback, useEffect } from "react";
import getCaretCoordinates from 'textarea-caret';
import { cn } from "@/lib/utils";
import { useVariableSuggestions } from "@/hooks/useVariableSuggestions";


interface TextareaWithSuggestionsProps extends React.ComponentProps<"textarea"> {
  projectId: number;
  onValueChange?: (value: string) => void;
}

function TextareaWithSuggestions({
  className,
  projectId,
  value = '',
  onValueChange,
  onChange,
  ...props
}: Readonly<TextareaWithSuggestionsProps>) {
  const [cursorPosition, setCursorPosition] = useState(0);
  const [popoverPosition, setPopoverPosition] = useState({ top: 0, left: 0 });
  const [activeIndex, setActiveIndex] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);


  const {
    suggestions,
    showSuggestions,
    insertSuggestion,
    hideSuggestions,
  } = useVariableSuggestions({
    projectId,
    text: String(value),
    cursorPosition,
  });

  // Refs to ensure keyboard navigation has latest state without re-creating event listeners
  const activeIndexRef = useRef(activeIndex);
  activeIndexRef.current = activeIndex;

  const suggestionsRef = useRef(suggestions);
  suggestionsRef.current = suggestions;



  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const newCursorPosition = e.target.selectionStart;
    const textarea = e.target;

    setCursorPosition(newCursorPosition);

    // Calculate precise popover position using getCaretCoordinates
    if (textareaRef.current) {
      const { top, left } = getCaretCoordinates(textarea, newCursorPosition);
      setPopoverPosition({ top: top + 20, left });
    }

    // Reset active index when text changes
    setActiveIndex(0);

    if (onValueChange) {
      onValueChange(newValue);
    }

    if (onChange) {
      onChange(e);
    }
  }, [onValueChange, onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Let the global keyboard handler manage suggestion navigation
    // Only handle non-suggestion related keys here
    if (!showSuggestions && props.onKeyDown) {
      props.onKeyDown(e);
    } else if (showSuggestions && !['ArrowDown', 'ArrowUp', 'Enter', 'Tab', 'Escape'].includes(e.key)) {
      // Allow other keys to pass through when suggestions are showing
      if (props.onKeyDown) {
        props.onKeyDown(e);
      }
    }
  }, [showSuggestions, props]);

  const handleSuggestionClick = useCallback((suggestion: { key: string; value: string; description?: string }) => {
    const newValue = insertSuggestion(suggestion);

    // Hide suggestions first to prevent them from appearing at the new cursor position
    hideSuggestions();
    setActiveIndex(0); // Reset active index

    if (onValueChange) {
      onValueChange(newValue);
    }

    // Create a synthetic event for onChange if provided
    if (onChange && textareaRef.current) {
      const syntheticEvent = {
        target: { ...textareaRef.current, value: newValue },
        currentTarget: textareaRef.current,
      } as React.ChangeEvent<HTMLTextAreaElement>;
      onChange(syntheticEvent);
    }

    // Focus back to textarea and position cursor at the end of the inserted variable + space
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        // Find the position where the variable was inserted and place cursor after it (including the space)
        const insertedText = '@' + suggestion.key + ' ';
        const insertPosition = newValue.lastIndexOf(insertedText);
        const newCursorPos = insertPosition + insertedText.length;
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        setCursorPosition(newCursorPos);
      }
    }, 0);
  }, [insertSuggestion, onValueChange, onChange, hideSuggestions]);

  // Robust keyboard navigation effect
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const currentSuggestions = suggestionsRef.current;
      if (currentSuggestions.length === 0) return;

      if (event.key === 'ArrowDown') {
        event.preventDefault();
        setActiveIndex(prev => (prev + 1) % currentSuggestions.length);
      } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        setActiveIndex(prev => (prev - 1 + currentSuggestions.length) % currentSuggestions.length);
      } else if (event.key === 'Enter' || event.key === 'Tab') {
        event.preventDefault();
        handleSuggestionClick(currentSuggestions[activeIndexRef.current]);
      } else if (event.key === 'Escape') {
        event.preventDefault();
        hideSuggestions();
      }
    };

    // Add/remove listener only when the popover's visibility changes
    if (showSuggestions) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showSuggestions, hideSuggestions, handleSuggestionClick]);

  const handleBlur = useCallback((e: React.FocusEvent<HTMLTextAreaElement>) => {
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      hideSuggestions();
    }, 200);
    
    if (props.onBlur) {
      props.onBlur(e);
    }
  }, [hideSuggestions, props]);

  return (
    <div className="relative">
      <textarea
        ref={textareaRef}
        data-slot="textarea"
        className={cn(
          "border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        {...props}
      />
      
      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          className="absolute z-50 w-64 rounded-lg border bg-popover shadow-md overflow-hidden"
          style={{ top: `${popoverPosition.top}px`, left: `${popoverPosition.left}px` }}
        >
          {suggestions.map((suggestion, index) => (
            <button
              key={suggestion.key}
              type="button"
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full px-3 py-2 text-left cursor-pointer text-sm"
              style={index === activeIndex ? {
                backgroundColor: '#dfc6f1',
                color: '#331e4d',
              } : undefined}
            >
              <div className="flex items-center justify-between">
                <div>
                  <span className="font-medium">@{suggestion.key}</span>
                  {suggestion.description && (
                    <p className="text-xs text-muted-foreground mt-1">{suggestion.description}</p>
                  )}
                </div>
                <span className="text-xs text-muted-foreground ml-2 truncate max-w-[100px] italic">
                  ({suggestion.value})
                </span>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

export { TextareaWithSuggestions };
