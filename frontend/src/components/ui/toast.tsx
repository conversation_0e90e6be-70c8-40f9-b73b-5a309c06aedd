import React from 'react';
import { Toast as ToastType } from '@/hooks/useToast';
import { X, CheckCircle, XCircle, Info, AlertTriangle } from 'lucide-react';

interface ToastProps {
  toast: ToastType;
  onRemove: (id: string) => void;
}

const toastStyles = {
  success: 'bg-green-50 border-green-200 text-green-800',
  error: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-secondary/10 border-secondary/20 text-secondary',
};

const toastIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
};

export function Toast({ toast, onRemove }: Readonly<ToastProps>) {
  const Icon = toastIcons[toast.type];

  return (
    <div
      className={`
        flex items-center p-4 mb-3 rounded-lg border shadow-sm
        ${toastStyles[toast.type]}
        animate-in slide-in-from-right-full duration-300
      `}
    >
      <Icon className="h-5 w-5 mr-3 flex-shrink-0" />
      <div className="flex-1 text-sm font-medium">
        {toast.message}
      </div>
      <button
        onClick={() => onRemove(toast.id)}
        className="ml-3 flex-shrink-0 rounded-md p-1.5 hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2"
        aria-label="Close notification"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
}

interface ToastContainerProps {
  toasts: ToastType[];
  onRemove: (id: string) => void;
}

export function ToastContainer({ toasts, onRemove }: Readonly<ToastContainerProps>) {
  if (toasts?.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 w-full max-w-sm">
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} onRemove={onRemove} />
      ))}
    </div>
  );
}
