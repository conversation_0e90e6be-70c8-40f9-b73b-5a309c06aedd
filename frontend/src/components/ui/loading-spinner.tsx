'use client';

import { memo } from 'react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
  overlay?: boolean;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6', 
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

function LoadingSpinnerComponent({ 
  size = 'md', 
  className, 
  text,
  overlay = false 
}: Readonly<LoadingSpinnerProps>) {
  const spinner = (
    <div className={cn("flex flex-col items-center justify-center space-y-3", className)}>
      <div 
        className={cn(
          "animate-spin rounded-full border-b-2 border-purple-600",
          sizeClasses[size]
        )}
      />
      {text && (
        <p className="text-sm text-gray-600 animate-pulse">{text}</p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 shadow-xl border border-gray-200 !mx-auto !my-auto">
          {spinner}
        </div>
      </div>
    );
  }

  return spinner;
}

export const LoadingSpinner = memo(LoadingSpinnerComponent);

// Specific loading components for common use cases
const ModalLoadingSpinnerComponent = () => (
  <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
    <LoadingSpinner text="Loading..." size="lg" />
  </div>
);
ModalLoadingSpinnerComponent.displayName = 'ModalLoadingSpinner';
export const ModalLoadingSpinner = memo(ModalLoadingSpinnerComponent);

const PageLoadingSpinnerComponent = () => (
  <div className="min-h-screen bg-gradient-to-br from-purple-100 via-purple-50 to-purple-200 flex items-center justify-center">
    <LoadingSpinner text="Loading..." size="xl" />
  </div>
);
PageLoadingSpinnerComponent.displayName = 'PageLoadingSpinner';
export const PageLoadingSpinner = memo(PageLoadingSpinnerComponent);

const InlineLoadingSpinnerComponent = ({ text = "Loading..." }: { text?: string }) => (
  <LoadingSpinner text={text} size="sm" className="py-4" />
);
InlineLoadingSpinnerComponent.displayName = 'InlineLoadingSpinner';
export const InlineLoadingSpinner = memo(InlineLoadingSpinnerComponent);
