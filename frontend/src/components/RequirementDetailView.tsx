'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { TextareaWithSuggestions } from '@/components/ui/textarea-with-suggestions';
import { ColoredTag } from '@/components/ui/colored-tag';
import { TextWithVariables } from '@/components/ui/text-with-variables';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogBody, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Edit, Trash2, ChevronDown, ChevronRight, SquarePen, BadgePlus } from 'lucide-react';
import { AppHeader } from '@/components/ui/app-header';

import { ToastContainer } from '@/components/ui/toast';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { PageUrlDetectionModal } from '@/components/PageUrlDetectionModal';
import { useToast } from '@/hooks/useToast';
import { markdownToPlainText } from '@/lib/markdown-utils';
import { Project } from '@/types';

interface Tag {
  id: number;
  name: string;
  color?: string;
}

interface Requirement {
  id: number;
  name: string;
  description: string;
  refined_description?: string;
  tags: Tag[];
}

interface TestCase {
  id: number;
  custom_id: string;
  title: string;
  steps?: string;
  expected_result?: string;
  notes?: string;
  created_at: string;
}

interface RequirementDetailViewProps {
  requirement: Requirement;
  project: Project;
  onBack: () => void;
}

export function RequirementDetailView({ requirement: initialRequirement, project, onBack }: Readonly<RequirementDetailViewProps>) {
  const router = useRouter();
  const [requirement, setRequirement] = useState(initialRequirement);
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [loading, setLoading] = useState(false);
  const [refining, setRefining] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [editedDescription, setEditedDescription] = useState(initialRequirement.description);
  const [editingTestCase, setEditingTestCase] = useState<TestCase | null>(null);
  const [deletingTestCase, setDeletingTestCase] = useState<TestCase | null>(null);
  const [showManualTestCaseModal, setShowManualTestCaseModal] = useState(false);
  const [showEditTestCaseModal, setShowEditTestCaseModal] = useState(false);
  const [expandedTestCases, setExpandedTestCases] = useState<Set<number>>(new Set());
  const [manualTestCase, setManualTestCase] = useState({
    title: '',
    steps: '',
    expected_result: '',
    notes: ''
  });
  const [editTestCase, setEditTestCase] = useState({
    title: '',
    steps: '',
    expected_result: '',
    notes: ''
  });
  const [savingManualTestCase, setSavingManualTestCase] = useState(false);
  const [savingEditTestCase, setSavingEditTestCase] = useState(false);
  const [showPageUrlModal, setShowPageUrlModal] = useState(false);
  const [manualTestCaseErrors, setManualTestCaseErrors] = useState({
    title: '',
    steps: ''
  });
  const [editTestCaseErrors, setEditTestCaseErrors] = useState({
    title: '',
    steps: ''
  });

  const { toasts, showToast, removeToast } = useToast();

  // Check if Generate Code button should be shown
  const shouldShowGenerateCodeButton = () => {
    return (
      project.automation_framework &&
      project.programming_language &&
      testCases.length > 0
    );
  };

  const handleGenerateCode = () => {
    setShowPageUrlModal(true);
  };

  useEffect(() => {
    loadTestCases();
    loadRequirementDetails();
  }, [requirement.id]); // eslint-disable-line react-hooks/exhaustive-deps

  const toggleTestCaseExpansion = (testCaseId: number) => {
    setExpandedTestCases(prev => {
      const newSet = new Set(prev);
      if (newSet.has(testCaseId)) {
        newSet.delete(testCaseId);
      } else {
        newSet.add(testCaseId);
      }
      return newSet;
    });
  };

  const loadRequirementDetails = async () => {
    try {
      const data = await apiClient.getRequirement(requirement.id);
      setRequirement(data);
      setEditedDescription(data.description);
    } catch (error) {
      console.error('Failed to load requirement details:', error);
    }
  };

  const loadTestCases = async () => {
    try {
      const data = await apiClient.getTestCases(requirement.id);
      setTestCases(data);
    } catch (error) {
      console.error('Failed to load test cases:', error);
    }
  };

  const handleSaveDescription = async () => {
    if (editedDescription === requirement.description) return;

    setLoading(true);
    try {
      await apiClient.updateRequirement(requirement.id, {
        description: editedDescription
      });
      setRequirement(prev => ({ ...prev, description: editedDescription }));
      showToast('Requirement description saved successfully!', 'success');
    } catch (error) {
      console.error('Failed to update requirement:', error);
      showToast('Failed to save requirement description', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleRefineRequirement = async () => {
    setRefining(true);
    try {
      const response = await apiClient.refineRequirement(requirement.id);
      setRequirement(prev => ({
        ...prev,
        refined_description: response.refined_description
      }));

      // Show success toast
      showToast('✨ Refined requirement generated successfully!', 'success');

      // Scroll to the refined requirement section after a short delay
      setTimeout(() => {
        const refinedSection = document.querySelector('[data-refined-requirement]');
        if (refinedSection) {
          refinedSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100);
    } catch (error) {
      console.error('Failed to refine requirement:', error);
      showToast('Failed to refine requirement', 'error');
    } finally {
      setRefining(false);
    }
  };

  const handleAcceptRefinedDescription = async () => {
    if (!requirement.refined_description) return;

    try {
      // Convert markdown to plain text before saving
      const plainTextDescription = markdownToPlainText(requirement.refined_description);

      await apiClient.updateRequirement(requirement.id, {
        description: plainTextDescription,
        refined_description: undefined  // Clear the refined description from database
      });

      // Update both requirement state and edited description to reflect changes immediately
      setRequirement(prev => ({
        ...prev,
        description: plainTextDescription,
        refined_description: undefined
      }));

      // Update the textarea content to show the new description
      setEditedDescription(plainTextDescription);

      // Show success toast notification
      showToast('Requirement description updated successfully!', 'success');
    } catch (error) {
      console.error('Failed to update requirement:', error);
      showToast('Failed to update requirement description', 'error');
    }
  };

  const handleGenerateTestCases = async () => {
    setGenerating(true);
    try {
      const newTestCases = await apiClient.generateTestCases(requirement.id);
      setTestCases(prev => [...prev, ...newTestCases]);
    } catch (error) {
      console.error('Failed to generate test cases:', error);
    } finally {
      setGenerating(false);
    }
  };

  const handleDeleteTestCase = async (testCase: TestCase) => {
    try {
      await apiClient.deleteTestCase(requirement.id, testCase.id);
      setTestCases(prev => prev.filter(tc => tc.id !== testCase.id));
      showToast('Test case deleted successfully!', 'success');
      setDeletingTestCase(null);
    } catch (error) {
      console.error('Failed to delete test case:', error);
      showToast('Failed to delete test case', 'error');
    }
  };

  const handleEditTestCase = (testCase: TestCase) => {
    setEditingTestCase(testCase);
    setEditTestCase({
      title: testCase.title,
      steps: testCase.steps || '',
      expected_result: testCase.expected_result || '',
      notes: testCase.notes || ''
    });
    setShowEditTestCaseModal(true);
  };

  const handleSaveEditTestCase = async () => {
    // Clear previous errors
    setEditTestCaseErrors({ title: '', steps: '' });

    let hasErrors = false;
    const errors = { title: '', steps: '' };

    // Validate title
    if (!editTestCase.title.trim()) {
      errors.title = 'Title is required';
      hasErrors = true;
    }

    // Validate steps
    if (!editTestCase.steps.trim()) {
      errors.steps = 'Test steps are required';
      hasErrors = true;
    }

    if (hasErrors) {
      setEditTestCaseErrors(errors);
      return;
    }

    if (!editingTestCase) return;

    setSavingEditTestCase(true);
    try {
      const updatedTestCase = await apiClient.updateTestCase(requirement.id, editingTestCase.id, editTestCase);
      setTestCases(prev => prev.map(tc => tc.id === editingTestCase.id ? updatedTestCase : tc));
      setShowEditTestCaseModal(false);
      setEditingTestCase(null);
      setEditTestCase({ title: '', steps: '', expected_result: '', notes: '' });
      setEditTestCaseErrors({ title: '', steps: '' });
      showToast('Test case updated successfully!', 'success');
    } catch (error) {
      console.error('Failed to update test case:', error);
      showToast('Failed to update test case', 'error');
    } finally {
      setSavingEditTestCase(false);
    }
  };

  const handleManualTestCaseInputChange = (field: string, value: string) => {
    setManualTestCase(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (field === 'title' && manualTestCaseErrors.title) {
      setManualTestCaseErrors(prev => ({ ...prev, title: '' }));
    }
    if (field === 'steps' && manualTestCaseErrors.steps) {
      setManualTestCaseErrors(prev => ({ ...prev, steps: '' }));
    }
  };

  const handleEditTestCaseInputChange = (field: string, value: string) => {
    setEditTestCase(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (field === 'title' && editTestCaseErrors.title) {
      setEditTestCaseErrors(prev => ({ ...prev, title: '' }));
    }
    if (field === 'steps' && editTestCaseErrors.steps) {
      setEditTestCaseErrors(prev => ({ ...prev, steps: '' }));
    }
  };

  const handleSaveManualTestCase = async () => {
    // Clear previous errors
    setManualTestCaseErrors({ title: '', steps: '' });

    let hasErrors = false;
    const errors = { title: '', steps: '' };

    // Validate title
    if (!manualTestCase.title.trim()) {
      errors.title = 'Title is required';
      hasErrors = true;
    }

    // Validate steps
    if (!manualTestCase.steps.trim()) {
      errors.steps = 'Test steps are required';
      hasErrors = true;
    }

    if (hasErrors) {
      setManualTestCaseErrors(errors);
      return;
    }

    setSavingManualTestCase(true);
    try {
      const newTestCase = await apiClient.createTestCase(requirement.id, manualTestCase);
      setTestCases(prev => [...prev, newTestCase]);
      setManualTestCase({ title: '', steps: '', expected_result: '', notes: '' });
      setManualTestCaseErrors({ title: '', steps: '' });
      setShowManualTestCaseModal(false);
      showToast('Test case created successfully!', 'success');
    } catch (error) {
      console.error('Failed to create test case:', error);
      showToast('Failed to create test case', 'error');
    } finally {
      setSavingManualTestCase(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />

      {/* Header */}
      <AppHeader>
        <div className="flex justify-between items-center">
          <div className="flex-1">
            <h1 className="text-xl font-bold text-gray-900">{requirement.name}</h1>
            <Breadcrumb
              items={[
                { label: 'Dashboard', onClick: () => router.push('/') },
                { label: project.name, onClick: onBack },
                { label: requirement.name, current: true }
              ]}
              className="mt-1"
            />
          </div>
          {shouldShowGenerateCodeButton() && (
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleGenerateCode}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-lg"
              >
                🚀 Generate Code
              </Button>
            </div>
          )}
        </div>
      </AppHeader>

      {/* Main Content - Two Column Layout */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Requirement Editor */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Requirement Description</CardTitle>
                <CardDescription>
                  Edit and refine your requirement description
                </CardDescription>

                {/* Tags Display */}
                {requirement.tags && requirement.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200">
                    <span className="text-sm font-medium text-gray-700 mr-2">Tags:</span>
                    {requirement.tags.map((tag) => (
                      <ColoredTag
                        key={tag.id}
                        name={tag.name}
                        variant="outline"
                        size="sm"
                      />
                    ))}
                  </div>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <TextareaWithSuggestions
                  value={editedDescription}
                  onValueChange={(value) => setEditedDescription(value)}
                  projectId={project.id}
                  rows={10}
                  style={{ minHeight: '400px' }}
                  placeholder="Enter requirement description. Use @variable_name to reference project variables."
                />
                
                <div className="flex space-x-2">
                  <Button 
                    onClick={handleSaveDescription}
                    disabled={loading || editedDescription === requirement.description}
                    variant="outline"
                  >
                    {loading ? 'Saving...' : 'Save Changes'}
                  </Button>
                  
                  <Button 
                    onClick={handleRefineRequirement}
                    disabled={refining}
                    className="bg-secondary hover:bg-[#014D4E]"
                  >
                    {refining ? 'Refining...' : '✨ Refine with AI'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Refined Description */}
            {requirement.refined_description && (
              <Card data-refined-requirement>
                <CardHeader>
                  <CardTitle>AI-Refined Description</CardTitle>
                  <CardDescription>
                    Enhanced version generated by AI
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="text-sm text-green-800 whitespace-pre-wrap">
                      <TextWithVariables
                        text={requirement.refined_description || ''}
                        projectId={project.id}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 mt-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setRequirement(prev => ({
                          ...prev,
                          refined_description: undefined
                        }));
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAcceptRefinedDescription}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Accept Changes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Test Cases */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Test Cases</CardTitle>
                    <CardDescription>
                      Generated and manual test cases
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => setShowManualTestCaseModal(true)}
                      variant="outline"
                      className="border-primary text-primary hover:bg-primary hover:text-white"
                    >
                      ➕ Add Test
                    </Button>
                    <Button
                      onClick={handleGenerateTestCases}
                      disabled={generating}
                      className="bg-secondary hover:bg-[#014D4E]"
                    >
                      {generating ? 'Generating...' : '✨ Generate Tests'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {testCases.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No test cases yet.</p>
                    <p className="text-sm">Click &quot;Generate Tests&quot; to create AI-powered test cases.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {testCases.map((testCase) => {
                      const isExpanded = expandedTestCases.has(testCase.id);
                      return (
                        <div key={testCase.id} className="border rounded-lg bg-white">
                          {/* Collapsed Header Row */}
                          <div className="flex justify-between items-center p-2">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded flex-shrink-0">
                                {testCase.custom_id}
                              </span>
                              <h5 className="font-medium text-gray-900 truncate">
                                {testCase.title}
                              </h5>
                            </div>
                            <div className="flex items-center space-x-1 flex-shrink-0">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 rounded-full hover:bg-purple-100"
                                onClick={() => handleEditTestCase(testCase)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 rounded-full text-red-600 hover:text-red-700 hover:bg-red-100"
                                onClick={() => setDeletingTestCase(testCase)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => toggleTestCaseExpansion(testCase.id)}
                              >
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>

                          {/* Expanded Content */}
                          {isExpanded && (
                            <div className="px-4 pb-4 border-t">
                              {testCase.steps && (
                                <div className="mb-2 mt-3">
                                  <span className="text-xs font-medium text-gray-500">STEPS:</span>
                                  <div className="text-xs text-gray-600 mt-1">
                                    <TextWithVariables
                                      text={testCase.steps}
                                      projectId={project.id}
                                    />
                                  </div>
                                </div>
                              )}

                              {testCase.expected_result && (
                                <div className="mb-2">
                                  <span className="text-xs font-medium text-gray-500">EXPECTED:</span>
                                  <div className="text-xs text-gray-600 mt-1">
                                    <TextWithVariables
                                      text={testCase.expected_result}
                                      projectId={project.id}
                                    />
                                  </div>
                                </div>
                              )}

                              {testCase.notes && (
                                <div>
                                  <span className="text-xs font-medium text-gray-500">NOTES:</span>
                                  <div className="text-xs text-gray-600 mt-1">
                                    <TextWithVariables
                                      text={testCase.notes}
                                      projectId={project.id}
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Manual Test Case Creation Modal */}
      <Dialog open={showManualTestCaseModal} onOpenChange={setShowManualTestCaseModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <BadgePlus className="h-5 w-5" />
              <span>Create Test Case</span>
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              Create a new test case for this requirement
            </DialogDescription>
          </DialogHeader>

          <DialogBody className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={manualTestCase.title}
                onChange={(e) => handleManualTestCaseInputChange('title', e.target.value)}
                placeholder="Enter test case title"
                className={manualTestCaseErrors.title ? 'border-red-500 focus:border-red-500' : ''}
              />
              {manualTestCaseErrors.title && (
                <p className="text-xs text-red-600 font-medium">
                  {manualTestCaseErrors.title}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="steps">Test Case Steps *</Label>
              <TextareaWithSuggestions
                id="steps"
                value={manualTestCase.steps}
                onValueChange={(value) => handleManualTestCaseInputChange('steps', value)}
                projectId={project.id}
                placeholder="Enter step-by-step instructions. Use @variable_name to reference project variables."
                rows={3}
                className={`resize-none overflow-y-auto ${manualTestCaseErrors.steps ? 'border-red-500 focus:border-red-500' : ''}`}
                style={{ height: '72px' }}
              />
              {manualTestCaseErrors.steps && (
                <p className="text-xs text-red-600 font-medium">
                  {manualTestCaseErrors.steps}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="expected_result">Expected Outcome</Label>
              <TextareaWithSuggestions
                id="expected_result"
                value={manualTestCase.expected_result}
                onValueChange={(value) => setManualTestCase(prev => ({ ...prev, expected_result: value }))}
                projectId={project.id}
                placeholder="Enter expected outcome. Use @variable_name to reference project variables."
                rows={3}
                className="resize-none overflow-y-auto"
                style={{ height: '72px' }}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <TextareaWithSuggestions
                id="notes"
                value={manualTestCase.notes}
                onValueChange={(value) => setManualTestCase(prev => ({ ...prev, notes: value }))}
                projectId={project.id}
                placeholder="Enter any assumptions, dependencies, or relevant context. Use @variable_name to reference project variables."
                rows={3}
                className="resize-none overflow-y-auto"
                style={{ height: '78px' }}
              />
            </div>
          </DialogBody>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowManualTestCaseModal(false);
                setManualTestCase({ title: '', steps: '', expected_result: '', notes: '' });
                setManualTestCaseErrors({ title: '', steps: '' });
              }}
              disabled={savingManualTestCase}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveManualTestCase}
              disabled={savingManualTestCase}
              className="bg-primary hover:bg-primary/90"
            >
              {savingManualTestCase ? 'Saving...' : 'Save Test Case'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Test Case Modal */}
      <Dialog open={showEditTestCaseModal} onOpenChange={setShowEditTestCaseModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <SquarePen className="h-5 w-5" />
              <span>Edit Test Case</span>
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              Edit the details of this test case
            </DialogDescription>
          </DialogHeader>

          <DialogBody className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Title *</Label>
              <Input
                id="edit-title"
                value={editTestCase.title}
                onChange={(e) => handleEditTestCaseInputChange('title', e.target.value)}
                placeholder="Enter test case title"
                className={editTestCaseErrors.title ? 'border-red-500 focus:border-red-500' : ''}
              />
              {editTestCaseErrors.title && (
                <p className="text-xs text-red-600 font-medium">
                  {editTestCaseErrors.title}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-steps">Test Case Steps *</Label>
              <TextareaWithSuggestions
                id="edit-steps"
                value={editTestCase.steps}
                onValueChange={(value) => handleEditTestCaseInputChange('steps', value)}
                projectId={project.id}
                placeholder="Enter step-by-step instructions. Use @variable_name to reference project variables."
                rows={3}
                className={`resize-none overflow-y-auto ${editTestCaseErrors.steps ? 'border-red-500 focus:border-red-500' : ''}`}
                style={{ height: '72px' }}
              />
              {editTestCaseErrors.steps && (
                <p className="text-xs text-red-600 font-medium">
                  {editTestCaseErrors.steps}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-expected_result">Expected Outcome</Label>
              <TextareaWithSuggestions
                id="edit-expected_result"
                value={editTestCase.expected_result}
                onValueChange={(value) => setEditTestCase(prev => ({ ...prev, expected_result: value }))}
                projectId={project.id}
                placeholder="Enter expected outcome. Use @variable_name to reference project variables."
                rows={3}
                className="resize-none overflow-y-auto"
                style={{ height: '72px' }}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-notes">Notes</Label>
              <TextareaWithSuggestions
                id="edit-notes"
                value={editTestCase.notes}
                onValueChange={(value) => setEditTestCase(prev => ({ ...prev, notes: value }))}
                projectId={project.id}
                placeholder="Enter any assumptions, dependencies, or relevant context. Use @variable_name to reference project variables."
                rows={3}
                className="resize-none overflow-y-auto"
                style={{ height: '78px' }}
              />
            </div>
          </DialogBody>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowEditTestCaseModal(false);
                setEditingTestCase(null);
                setEditTestCase({ title: '', steps: '', expected_result: '', notes: '' });
                setEditTestCaseErrors({ title: '', steps: '' });
              }}
              disabled={savingEditTestCase}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveEditTestCase}
              disabled={savingEditTestCase}
              className="bg-primary hover:bg-primary/90"
            >
              {savingEditTestCase ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Test Case Confirmation Dialog */}
      <ConfirmDialog
        open={!!deletingTestCase}
        onOpenChange={(open) => !open && setDeletingTestCase(null)}
        title="Delete Test Case"
        description={`Are you sure you want to delete "${deletingTestCase?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={() => deletingTestCase && handleDeleteTestCase(deletingTestCase)}
      />

      {/* Page URL Detection Modal */}
      <PageUrlDetectionModal
        isOpen={showPageUrlModal}
        onClose={() => setShowPageUrlModal(false)}
        testCases={testCases}
        projectId={project.id}
        project={project}
        requirementId={requirement?.id}
      />
    </div>
  );
}
