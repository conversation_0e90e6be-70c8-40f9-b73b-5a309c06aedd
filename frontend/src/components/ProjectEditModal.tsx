'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { SquarePen } from 'lucide-react';

const isValidSSHUrl = (url: string): boolean => {
  if (!url.trim()) return true; // Allow empty URL

  // SSH URL patterns:
  // **************:username/repo.git
  // **************:username/repo.git
  // ssh://**************/username/repo.git
  const sshPatterns = [
    /^git@[\w.-]+:[\w.-]+\/[\w.-]+\.git$/,
    /^ssh:\/\/git@[\w.-]+\/[\w.-]+\/[\w.-]+\.git$/
  ];

  return sshPatterns.some(pattern => pattern.test(url));
};

interface Project {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  code_source?: string | null;
  automation_framework?: string | null;
  programming_language?: string | null;
  created_at: string;
  member_count: number;
  requirement_count: number;
}

interface ProjectEditModalProps {
  project: Project;
  onClose: () => void;
  onProjectUpdated: (project: Project) => void;
}

export function ProjectEditModal({ project, onClose, onProjectUpdated }: Readonly<ProjectEditModalProps>) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    code_source: '',
    automation_framework: '',
    programming_language: '',
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProjectDetails = async () => {
      if (!project.id) {
        setInitialLoading(false);
        setError("Project ID not found.");
        return;
      }

      try {
        setError('');
        setInitialLoading(true);
        const fullProject = await apiClient.getProject(project.id);
        setFormData({
          name: fullProject.name,
          description: fullProject.description || '',
          code_source: fullProject.code_source || '',
          automation_framework: fullProject.automation_framework || '',
          programming_language: fullProject.programming_language || '',
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load project details');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchProjectDetails();
  }, [project.id]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // If code_source is being cleared, also clear automation_framework and programming_language
      if (field === 'code_source' && !value.trim()) {
        newData.automation_framework = '';
        newData.programming_language = '';
      }

      // If automation_framework is being cleared, also clear programming_language
      if (field === 'automation_framework' && (!value || value === 'none')) {
        newData.programming_language = '';
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      setError('Project name is required');
      return;
    }

    // Validate SSH URL if provided
    if (formData.code_source.trim() && !isValidSSHUrl(formData.code_source)) {
      setError('Please enter a valid SSH Git repository URL (e.g., **************:username/repo.git)');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const projectData = {
        ...formData,
        automation_framework: formData.automation_framework && formData.automation_framework !== "none" ? formData.automation_framework : null,
        programming_language: formData.programming_language && formData.programming_language !== "none" ? formData.programming_language : null,
      };

      const updatedProject = await apiClient.updateProject(project.id, projectData);
      onProjectUpdated({ ...project, ...updatedProject });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update project');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <SquarePen className="h-5 w-5" />
            <span>Edit Project</span>
          </DialogTitle>
          <DialogDescription>
            Update your project details
          </DialogDescription>
        </DialogHeader>

        <DialogBody>
          {initialLoading ? (
            <div className="flex items-center justify-center p-8">
              <span>Loading...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Project Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter project name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your project"
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="automation_framework">Automation Framework</Label>
                  <Select
                    value={formData.automation_framework || "none"}
                    onValueChange={(value) => handleInputChange('automation_framework', value === "none" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select framework (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="selenium">Selenium</SelectItem>
                      <SelectItem value="playwright">Playwright</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="programming_language">Programming Language</Label>
                  <Select
                    value={formData.programming_language || "none"}
                    onValueChange={(value) => handleInputChange('programming_language', value === "none" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select language (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="python">Python</SelectItem>
                      <SelectItem value="javascript">JavaScript</SelectItem>
                      <SelectItem value="java">Java</SelectItem>
                      <SelectItem value="csharp">C#</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="code_source">SSH Git Repository URL</Label>
                <Input
                  id="code_source"
                  placeholder="**************:username/repository.git"
                  value={formData.code_source || ''}
                  onChange={(e) => handleInputChange('code_source', e.target.value)}
                />
              </div>

              {error && (
                <div className="text-red-600 text-sm">
                  {error}
                </div>
              )}
            </div>
          )}
        </DialogBody>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            type="button" 
            disabled={loading || initialLoading}
            onClick={handleSubmit}
          >
            {loading ? 'Updating...' : 'Update Project'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
