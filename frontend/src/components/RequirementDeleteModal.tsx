'use client';

import { useState } from 'react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';

interface Tag {
  id: number;
  name: string;
  color?: string;
}

interface RequirementListItem {
  id: number;
  name: string;
  created_at: string;
  created_by: number;
  tags: Tag[];
  test_case_count: number;
}

interface RequirementDeleteModalProps {
  requirement: RequirementListItem;
  onClose: () => void;
  onRequirementDeleted: (requirementId: number) => void;
}

export function RequirementDeleteModal({ requirement, onClose, onRequirementDeleted }: Readonly<RequirementDeleteModalProps>) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleDelete = async () => {
    setLoading(true);
    setError('');

    try {
      await apiClient.deleteRequirement(requirement.id);
      onRequirementDeleted(requirement.id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete requirement');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-6 w-6 text-red-600" />
            <DialogTitle className="text-red-600">Delete Requirement</DialogTitle>
          </div>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the requirement and all associated data.
          </DialogDescription>
        </DialogHeader>

        <DialogBody className="space-y-4 pt-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">
              You are about to delete &quot;{requirement.name}&quot;
            </h4>
            <div className="text-sm text-red-700 space-y-1">
              <p>• All {requirement.test_case_count} test cases will be deleted</p>
              <p>• All requirement data will be removed from the database</p>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Warning:</p>
                <p>This action is irreversible. Make sure you have backed up any important data before proceeding.</p>
              </div>
            </div>
          </div>

          {error && (
            <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded p-3">
              {error}
            </div>
          )}
        </DialogBody>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={loading}
            className="bg-red-600 hover:bg-red-700"
          >
            {loading ? 'Deleting...' : 'Delete Requirement'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
