'use client';

import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter, DialogBody } from '@/components/ui/dialog';

import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

import { Search, X, UserPlus, Crown, User } from 'lucide-react';

interface ProjectMember {
  id: number;
  user_id: number;
  user_email: string;
  user_name?: string;
  role: 'owner' | 'member';
  joined_at: string;
}

interface UserSearchResult {
  id: number;
  email: string;
  full_name?: string;
}

interface ProjectShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: number;
  projectName: string;
  currentUserRole: 'owner' | 'member';
  onMembersChanged?: () => void;
}

const roleIcons = {
  owner: Crown,
  member: User,
};

const roleLabels = {
  owner: 'Owner',
  member: 'Member',
};

// Role descriptions for future use
// const roleDescriptions = {
//   owner: 'Full access and can delete the project',
//   admin: 'Can manage members and project settings',
//   member: 'Can view and contribute to the project',
// };

export function ProjectShareModal({
  isOpen,
  onClose,
  projectId,
  projectName,
  currentUserRole,
  onMembersChanged
}: Readonly<ProjectShareModalProps>) {
  const [members, setMembers] = useState<ProjectMember[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Track pending changes
  const [pendingAdditions, setPendingAdditions] = useState<UserSearchResult[]>([]);
  const [pendingRemovals, setPendingRemovals] = useState<number[]>([]);

  const loadMembers = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await apiClient.getProjectMembers(projectId);
      setMembers(data);
    } catch (error) {
      console.error('Failed to load members:', error);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    if (isOpen) {
      loadMembers();
      // Clear pending changes when modal opens
      setPendingAdditions([]);
      setPendingRemovals([]);
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [isOpen, projectId, loadMembers]);

  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const results = await apiClient.searchUsers(query);
      // Filter out users who are already members
      const memberUserIds = members.map(m => m.user_id);
      const filteredResults = results.filter(user => !memberUserIds.includes(user.id));
      setSearchResults(filteredResults);
    } catch (error) {
      console.error('Failed to search users:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const addMemberToPending = (user: UserSearchResult) => {
    // Add to pending additions if not already there
    if (!pendingAdditions.find(u => u.id === user.id)) {
      setPendingAdditions(prev => [...prev, user]);
    }
    // Remove from search results
    setSearchResults(prev => prev.filter(u => u.id !== user.id));
    setSearchQuery('');
  };

  const removeMemberFromPending = (memberId: number) => {
    // Add to pending removals if not already there
    if (!pendingRemovals.includes(memberId)) {
      setPendingRemovals(prev => [...prev, memberId]);
    }
  };

  const cancelPendingAddition = (userId: number) => {
    setPendingAdditions(prev => prev.filter(u => u.id !== userId));
  };

  const cancelPendingRemoval = (memberId: number) => {
    setPendingRemovals(prev => prev.filter(id => id !== memberId));
  };

  const applyChanges = async () => {
    try {
      setIsLoading(true);

      // Apply additions
      for (const user of pendingAdditions) {
        await apiClient.addProjectMember(projectId, user.id, 'member');
      }

      // Apply removals
      for (const memberId of pendingRemovals) {
        await apiClient.removeProjectMember(projectId, memberId);
      }

      // Clear pending changes
      setPendingAdditions([]);
      setPendingRemovals([]);

      // Reload members and close modal
      await loadMembers();

      // Notify parent component that members have changed
      if (onMembersChanged) {
        onMembersChanged();
      }

      onClose();

      // If current user removed themselves, refresh the project list
      const currentUserMember = members.find(m => m.user_id === 1); // TODO: Get actual current user ID
      if (currentUserMember && pendingRemovals.includes(currentUserMember.id)) {
        window.location.reload(); // Simple way to refresh project list
      }
    } catch (error) {
      console.error('Failed to apply changes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return 'U';
  };

  const canManageMembers = currentUserRole === 'owner' || currentUserRole === 'member';
  const canRemoveMembers = currentUserRole === 'owner';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <UserPlus className="h-5 w-5" />
            <span>Share &quot;{projectName}&quot;</span>
          </DialogTitle>
          <DialogDescription>
            Invite people to collaborate on this project
          </DialogDescription>
        </DialogHeader>

        <DialogBody className="space-y-6">
          {/* Add Members Section */}
          {canManageMembers && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Add People</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
                <Input
                  placeholder="Search by email or name..."
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    searchUsers(e.target.value);
                  }}
                  className="pl-10 bg-white/80 backdrop-blur-sm border-purple-200 focus:border-purple-400 focus:ring-purple-400/20 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg"
                />
              </div>

              {/* Search Results */}
              {isSearching ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Searching users...</p>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="border rounded-md max-h-40 overflow-y-auto">
                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-3 hover:bg-gray-50 border-b last:border-b-0"
                    >
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {getInitials(user.full_name, user.email)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{user.full_name || user.email}</p>
                          {user.full_name && (
                            <p className="text-xs text-gray-500">{user.email}</p>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addMemberToPending(user)}
                        className="text-xs"
                      >
                        Add
                      </Button>
                    </div>
                  ))}
                </div>
              ) : null}
            </div>
          )}

          {/* Pending Additions */}
          {pendingAdditions.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-medium text-green-700">
                To be Added ({pendingAdditions.length})
              </Label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {pendingAdditions.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded-md"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-xs">
                          {getInitials(user.full_name, user.email)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{user.full_name || user.email}</p>
                        {user.full_name && (
                          <p className="text-xs text-gray-500">{user.email}</p>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => cancelPendingAddition(user.id)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Current Members Section */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              People with access ({members.length})
            </Label>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {members.map((member) => {
                  const RoleIcon = roleIcons[member.role];
                  const isCurrentUser = member.user_id === 1; // TODO: Get current user ID
                  const isPendingRemoval = pendingRemovals.includes(member.id);

                  return (
                    <div
                      key={member.id}
                      className={`flex items-center justify-between p-3 border rounded-md hover:bg-gray-50 ${
                        isPendingRemoval ? 'bg-red-50 border-red-200 opacity-60' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>
                            {getInitials(member.user_name, member.user_email)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className={`text-sm font-medium ${isPendingRemoval ? 'line-through' : ''}`}>
                              {member.user_name || member.user_email}
                              {isCurrentUser && (
                                <span className="text-xs text-gray-500 ml-1">(You)</span>
                              )}
                            </p>
                          </div>
                          {member.user_name && (
                            <p className={`text-xs text-gray-500 ${isPendingRemoval ? 'line-through' : ''}`}>
                              {member.user_email}
                            </p>
                          )}
                          {isPendingRemoval && (
                            <p className="text-xs text-red-600 font-medium">To be removed</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge variant={member.role === 'owner' ? 'secondary' : 'outline'} className="flex items-center space-x-1">
                          <RoleIcon className="h-3 w-3" />
                          <span>{roleLabels[member.role]}</span>
                        </Badge>

                        {/* Show different buttons based on pending removal state */}
                        {isPendingRemoval ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => cancelPendingRemoval(member.id)}
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-100"
                            aria-label="Cancel removal"
                          >
                            <X className="h-4 w-4 rotate-45" aria-hidden="true" />
                          </Button>
                        ) : (
                          <>
                            {/* Only owners can remove members */}
                            {canRemoveMembers && (
                              <>
                                {member.role === 'owner' && isCurrentUser ? (
                                  // Owner can only remove themselves if there are other members
                                  members.length > 1 ? (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => removeMemberFromPending(member.id)}
                                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100"
                                      aria-label="Leave project"
                                    >
                                      <X className="h-4 w-4" aria-hidden="true" />
                                    </Button>
                                  ) : (
                                    <div
                                      className="h-8 w-8 p-0 flex items-center justify-center text-gray-400 cursor-not-allowed"
                                      title="Cannot leave project as the only member"
                                    >
                                      <X className="h-4 w-4" aria-hidden="true" />
                                    </div>
                                  )
                                ) : member.role !== 'owner' ? (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeMemberFromPending(member.id)}
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100"
                                    aria-label={`Remove ${member.user_email} from project`}
                                  >
                                    <X className="h-4 w-4" aria-hidden="true" />
                                  </Button>
                                ) : null}
                              </>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </DialogBody>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={applyChanges}
            disabled={isLoading || (pendingAdditions.length === 0 && pendingRemovals.length === 0)}
          >
            {isLoading ? 'Applying Changes...' : 'Apply Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    
  );
}
