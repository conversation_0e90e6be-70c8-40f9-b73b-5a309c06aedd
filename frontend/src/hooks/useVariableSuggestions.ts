import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/api';

interface ProjectVariable {
  id: number;
  key: string;
  value: string;
  description?: string;
}

interface VariableSuggestion {
  key: string;
  value: string;
  description?: string;
}

interface UseVariableSuggestionsProps {
  projectId: number;
  text: string;
  cursorPosition: number;
}

interface UseVariableSuggestionsReturn {
  suggestions: VariableSuggestion[];
  showSuggestions: boolean;
  suggestionPosition: { start: number; end: number } | null;
  insertSuggestion: (suggestion: VariableSuggestion) => string;
  hideSuggestions: () => void;
}

export function useVariableSuggestions({
  projectId,
  text,
  cursorPosition,
}: UseVariableSuggestionsProps): UseVariableSuggestionsReturn {
  const [variables, setVariables] = useState<ProjectVariable[]>([]);
  const [suggestions, setSuggestions] = useState<VariableSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestionPosition, setSuggestionPosition] = useState<{ start: number; end: number } | null>(null);

  // Load project variables
  useEffect(() => {
    const loadVariables = async () => {
      try {
        const data = await apiClient.getProjectVariables(projectId);
        setVariables(data);
      } catch (error) {
        console.error('Failed to load project variables:', error);
      }
    };

    if (projectId) {
      loadVariables();
    }
  }, [projectId]);

  // Check for @ mentions and filter suggestions
  useEffect(() => {
    if (!text || cursorPosition < 0) {
      setShowSuggestions(false);
      setSuggestions([]);
      setSuggestionPosition(null);
      return;
    }

    // Use regex to match @ mentions that are either at start or preceded by space
    // and require at least one character after @
    const textUpToCursor = text.substring(0, cursorPosition);
    const mentionTriggerRegex = /(?:^|\s)@(\w+)$/;
    const match = RegExp(mentionTriggerRegex).exec(textUpToCursor);

    if (!match) {
      setShowSuggestions(false);
      setSuggestions([]);
      setSuggestionPosition(null);
      return;
    }

    const partialName = match[1]; // The captured group after @
    const atPosition = textUpToCursor.lastIndexOf('@');

    // Filter variables based on partial name
    const filteredSuggestions = variables
      .filter(variable =>
        variable.key.toLowerCase().startsWith(partialName.toLowerCase())
      )
      .map(variable => ({
        key: variable.key,
        value: variable.value,
        description: variable.description,
      }))
      .slice(0, 5); // Limit to 5 suggestions

    setSuggestions(filteredSuggestions);
    setShowSuggestions(filteredSuggestions.length > 0);
    setSuggestionPosition({ start: atPosition, end: cursorPosition });
  }, [text, cursorPosition, variables]);

  const insertSuggestion = useCallback((suggestion: VariableSuggestion): string => {
    if (!suggestionPosition) return text;

    const before = text.substring(0, suggestionPosition.start);
    const after = text.substring(suggestionPosition.end);

    // Add a space after the variable to prevent immediate re-triggering of suggestions
    return before + '@' + suggestion.key + ' ' + after;
  }, [text, suggestionPosition]);

  const hideSuggestions = useCallback(() => {
    setShowSuggestions(false);
    setSuggestions([]);
    setSuggestionPosition(null);
  }, []);

  return {
    suggestions,
    showSuggestions,
    suggestionPosition,
    insertSuggestion,
    hideSuggestions,
  };
}
