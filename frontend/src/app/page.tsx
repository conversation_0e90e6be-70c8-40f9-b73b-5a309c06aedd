'use client';

import { useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AuthPage } from '@/components/AuthPage';
import { Dashboard } from '@/components/Dashboard';
import { PageLoadingSpinner } from '@/components/ui/loading-spinner';

function HomeContent() {
  const { user, loading } = useAuth();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Handle Google OAuth callback tokens
    const token = searchParams.get('token');
    const refreshToken = searchParams.get('refresh_token');

    if (token) {
      localStorage.setItem('token', token);

      // Store refresh token if provided (Google SSO now provides it)
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken);
      }

      // Remove tokens from URL
      window.history.replaceState({}, document.title, window.location.pathname);
      // Reload to trigger auth context update
      window.location.reload();
    }
  }, [searchParams]);

  if (loading) {
    return <PageLoadingSpinner />;
  }

  if (!user) {
    return <AuthPage />;
  }

  return <Dashboard />;
}

export default function Home() {
  return (
    <Suspense fallback={<PageLoadingSpinner />}>
      <HomeContent />
    </Suspense>
  );
}
