'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { ProjectView } from '@/components/ProjectView';
import { Project } from '@/types';
import { useToast } from '@/hooks/useToast';

export default function ProjectPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const { showToast } = useToast();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const slug = params.slug as string;

  useEffect(() => {
    const loadProject = async () => {
      // Wait for authentication to complete
      if (authLoading) return;

      // Redirect to login if not authenticated
      if (!user) {
        router.push('/');
        return;
      }

      try {
        setLoading(true);
        const projectData = await apiClient.getProjectBySlug(slug);
        setProject(projectData);
      } catch (error) {
        console.error('Failed to load project:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to load project';

        if (errorMessage.includes('permission') || errorMessage.includes('forbidden')) {
          setError('You do not have permission to access this project');
          showToast('You do not have permission to access this project.', 'error');
          setTimeout(() => {
            router.push('/');
          }, 3000);
        } else {
          setError('Project not found');
          showToast(errorMessage, 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      loadProject();
    }
  }, [slug, user, authLoading, router, showToast]);

  const handleBack = () => {
    router.push('/');
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 flex items-center justify-center px-4">
        <div className="max-w-md mx-auto text-center">
          {/* Error Icon */}
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-purple-200 rounded-full blur-2xl opacity-30"></div>
            <div className="relative bg-white/80 backdrop-blur-sm rounded-full p-6 shadow-lg border border-purple-100">
              <svg className="h-16 w-16 text-purple-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 mb-3">Project Not Found</h1>
            <p className="text-gray-600 leading-relaxed">
              The project you're looking for doesn't exist or you don't have access to it.
            </p>
          </div>

          {/* Action Button */}
          <button
            onClick={handleBack}
            className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 font-medium"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return <ProjectView key={project.id} project={project} onBack={handleBack} />;
}
