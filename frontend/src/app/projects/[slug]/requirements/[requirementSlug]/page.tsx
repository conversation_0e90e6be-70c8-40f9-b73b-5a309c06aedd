'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { RequirementDetailView } from '@/components/RequirementDetailView';
import { Project } from '@/types';
import { useToast } from '@/hooks/useToast';

interface Requirement {
  id: number;
  name: string;
  slug: string;
  description: string;
  refined_description?: string;
  tags: Array<{
    id: number;
    name: string;
    color?: string;
  }>;
}

export default function RequirementPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const { showToast } = useToast();
  const [project, setProject] = useState<Project | null>(null);
  const [requirement, setRequirement] = useState<Requirement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const projectSlug = params.slug as string;
  const requirementSlug = params.requirementSlug as string;

  useEffect(() => {
    const loadData = async () => {
      // Wait for authentication to complete
      if (authLoading) return;

      // Redirect to login if not authenticated
      if (!user) {
        router.push('/');
        return;
      }

      try {
        setLoading(true);

        // Load project first
        const projectData = await apiClient.getProjectBySlug(projectSlug);
        setProject(projectData);

        // Load requirement by slug
        const requirementData = await apiClient.getRequirementBySlug(projectSlug, requirementSlug);
        setRequirement(requirementData);
      } catch (error) {
        console.error('Failed to load data:', error);
        setError('Requirement not found');
        showToast('Failed to load requirement. Please try again.', 'error');
      } finally {
        setLoading(false);
      }
    };

    if (projectSlug && requirementSlug) {
      loadData();
    }
  }, [projectSlug, requirementSlug, user, authLoading, router, showToast]);

  const handleBack = () => {
    router.push(`/projects/${projectSlug}`);
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error || !project || !requirement) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 flex items-center justify-center px-4">
        <div className="max-w-md mx-auto text-center">
          {/* Error Icon */}
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-purple-200 rounded-full blur-2xl opacity-30"></div>
            <div className="relative bg-white/80 backdrop-blur-sm rounded-full p-6 shadow-lg border border-purple-100">
              <svg className="h-16 w-16 text-purple-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 mb-3">Requirement Not Found</h1>
            <p className="text-gray-600 leading-relaxed">
              The requirement you're looking for doesn't exist or you don't have access to it.
            </p>
          </div>

          {/* Action Button */}
          <button
            onClick={handleBack}
            className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 font-medium"
          >
            Back to Project
          </button>
        </div>
      </div>
    );
  }

  return (
    <RequirementDetailView
      requirement={requirement}
      project={project}
      onBack={handleBack}
    />
  );
}
