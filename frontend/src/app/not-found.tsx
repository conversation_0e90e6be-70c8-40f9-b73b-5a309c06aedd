'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft, Search } from 'lucide-react';

export default function NotFound() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Animated Search Icon */}
        <div className="relative mb-8">
          <div className="absolute inset-0 bg-purple-200 rounded-full blur-3xl opacity-30 animate-pulse"></div>
          <div className="relative bg-white/80 backdrop-blur-sm rounded-full p-8 shadow-xl border border-purple-100">
            {/* Magnifying glass with animated search motion */}
            <div className="relative h-24 w-24 mx-auto">
              <Search className="h-20 w-20 text-purple-400 absolute top-2 left-2 animate-pulse" />
              {/* Animated search beam */}
              <div className="absolute top-0 left-0 h-24 w-24 animate-spin" style={{ animationDuration: '3s' }}>
                <div className="h-1 w-8 bg-gradient-to-r from-purple-400 to-transparent rounded-full absolute top-12 left-16 opacity-60"></div>
              </div>
              {/* Floating dots representing search results */}
              <div className="absolute top-4 right-2 w-1 h-1 bg-purple-300 rounded-full animate-ping opacity-75"></div>
              <div className="absolute top-8 right-4 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping opacity-60" style={{ animationDelay: '0.5s' }}></div>
              <div className="absolute top-16 right-1 w-1 h-1 bg-purple-200 rounded-full animate-ping opacity-50" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>
        </div>

        {/* Error Code */}
        <div className="mb-6">
          <h1 className="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-purple-400 mb-2">
            404
          </h1>
          <div className="h-1 w-24 bg-gradient-to-r from-purple-600 to-purple-400 mx-auto rounded-full"></div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h2 className="text-3xl font-semibold text-gray-900 mb-4">
            Page Not Found
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed max-w-md mx-auto">
            The page you're looking for doesn't exist or has been moved. 
            Let's get you back on track.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <Link href="/">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
            >
              <Home className="h-5 w-5 mr-2" />
              Go Home
            </Button>
          </Link>
          
          <Button 
            variant="outline" 
            size="lg"
            onClick={() => window.history.back()}
            className="border-purple-200 text-purple-600 hover:bg-purple-50 hover:border-purple-300 shadow-sm hover:shadow-md transition-all duration-200"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Go Back
          </Button>
        </div>

        {/* Search Suggestion */}
        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-purple-100 shadow-sm">
          <div className="flex items-center justify-center mb-3">
            <Search className="h-5 w-5 text-purple-400 mr-2" />
            <span className="text-sm font-medium text-gray-700">Looking for something specific?</span>
          </div>
          <p className="text-sm text-gray-600">
            Try searching from the dashboard or check out your recent projects.
          </p>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-purple-300 rounded-full animate-ping opacity-75"></div>
        <div className="absolute top-40 right-32 w-1 h-1 bg-purple-400 rounded-full animate-ping opacity-50" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-32 left-16 w-1.5 h-1.5 bg-purple-200 rounded-full animate-ping opacity-60" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 right-20 w-1 h-1 bg-purple-300 rounded-full animate-ping opacity-40" style={{ animationDelay: '0.5s' }}></div>
      </div>
    </div>
  );
}
