@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-heading: var(--font-sora);
  --font-mono: var(--font-fira-code);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0); /* White */
  --foreground: oklch(0.2 0.01 285); /* Dark text */
  --card: oklch(1 0 0); /* White */
  --card-foreground: oklch(0.2 0.01 285); /* Dark text */
  --popover: oklch(1 0 0); /* White */
  --popover-foreground: oklch(0.2 0.01 285); /* Dark text */
  --primary: oklch(0.72 0.15 310); /* Lavender #B57EDC */
  --primary-foreground: oklch(1 0 0); /* White text on lavender */
  --secondary: oklch(0.5 0.12 180); /* Teal #008080 */
  --secondary-foreground: oklch(1 0 0); /* White text on teal */
  --muted: oklch(0.96 0.005 285); /* Light gray */
  --muted-foreground: oklch(0.55 0.01 285); /* Medium gray */
  --accent: oklch(1 0 0); /* White */
  --accent-foreground: oklch(0.2 0.01 285); /* Dark text */
  --destructive: oklch(0.577 0.245 27.325); /* Red for errors */
  --border: oklch(0.92 0.004 285); /* Light border */
  --input: oklch(0.92 0.004 285); /* Light input background */
  --ring: oklch(0.72 0.15 310); /* Lavender focus ring */
  --chart-1: oklch(0.72 0.15 310); /* Lavender */
  --chart-2: oklch(0.5 0.12 180); /* Teal */
  --chart-3: oklch(0.8 0.1 320); /* Light lavender */
  --chart-4: oklch(0.6 0.1 170); /* Dark teal */
  --chart-5: oklch(0.9 0.05 300); /* Very light lavender */
  --sidebar: oklch(1 0 0); /* White */
  --sidebar-foreground: oklch(0.2 0.01 285); /* Dark text */
  --sidebar-primary: oklch(0.72 0.15 310); /* Lavender */
  --sidebar-primary-foreground: oklch(1 0 0); /* White */
  --sidebar-accent: oklch(0.96 0.005 285); /* Light gray */
  --sidebar-accent-foreground: oklch(0.2 0.01 285); /* Dark text */
  --sidebar-border: oklch(0.92 0.004 285); /* Light border */
  --sidebar-ring: oklch(0.72 0.15 310); /* Lavender */
}

.dark {
  --background: oklch(0.15 0.01 285); /* Dark background */
  --foreground: oklch(0.95 0 0); /* Light text */
  --card: oklch(0.2 0.01 285); /* Dark card */
  --card-foreground: oklch(0.95 0 0); /* Light text */
  --popover: oklch(0.2 0.01 285); /* Dark popover */
  --popover-foreground: oklch(0.95 0 0); /* Light text */
  --primary: oklch(0.8 0.12 310); /* Lighter lavender for dark mode */
  --primary-foreground: oklch(0.15 0.01 285); /* Dark text on light lavender */
  --secondary: oklch(0.6 0.1 180); /* Lighter teal for dark mode */
  --secondary-foreground: oklch(0.95 0 0); /* Light text */
  --muted: oklch(0.25 0.01 285); /* Dark muted */
  --muted-foreground: oklch(0.7 0.01 285); /* Light muted text */
  --accent: oklch(0.25 0.01 285); /* Dark accent */
  --accent-foreground: oklch(0.95 0 0); /* Light text */
  --destructive: oklch(0.7 0.19 22); /* Red for errors */
  --border: oklch(1 0 0 / 10%); /* Subtle border */
  --input: oklch(1 0 0 / 15%); /* Subtle input background */
  --ring: oklch(0.8 0.12 310); /* Lavender focus ring */
  --chart-1: oklch(0.8 0.12 310); /* Lighter lavender */
  --chart-2: oklch(0.6 0.1 180); /* Lighter teal */
  --chart-3: oklch(0.85 0.08 320); /* Very light lavender */
  --chart-4: oklch(0.65 0.08 170); /* Light teal */
  --chart-5: oklch(0.9 0.04 300); /* Pale lavender */
  --sidebar: oklch(0.2 0.01 285); /* Dark sidebar */
  --sidebar-foreground: oklch(0.95 0 0); /* Light text */
  --sidebar-primary: oklch(0.8 0.12 310); /* Lighter lavender */
  --sidebar-primary-foreground: oklch(0.15 0.01 285); /* Dark text */
  --sidebar-accent: oklch(0.25 0.01 285); /* Dark accent */
  --sidebar-accent-foreground: oklch(0.95 0 0); /* Light text */
  --sidebar-border: oklch(1 0 0 / 10%); /* Subtle border */
  --sidebar-ring: oklch(0.8 0.12 310); /* Lavender */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Headings use Sora font */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-sora), ui-sans-serif, system-ui, sans-serif;
    font-weight: 600;
  }

  /* Specific heading styles */
  h1 {
    @apply text-3xl font-bold;
  }

  h2 {
    @apply text-2xl font-semibold;
  }

  h3 {
    @apply text-xl font-semibold;
  }

  h4 {
    @apply text-lg font-medium;
  }

  h5 {
    @apply text-base font-medium;
  }

  h6 {
    @apply text-sm font-medium;
  }
}

@layer utilities {
  .font-heading {
    font-family: var(--font-sora), ui-sans-serif, system-ui, sans-serif;
  }

  .font-body {
    font-family: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
  }

  .font-code {
    font-family: var(--font-fira-code), ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  }

  .max-w-\[90\%\] {
    max-width: 90%;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Custom very light purple colors for project cards */
  .from-purple-25 {
    --tw-gradient-from: #faf7ff var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(250 247 255 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-purple-50 {
    --tw-gradient-to: #f3f0ff var(--tw-gradient-to-position);
  }
}
