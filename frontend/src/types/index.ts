export interface Project {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  code_source?: string | null;
  automation_framework?: string | null;
  programming_language?: string | null;
  created_at: string;
  updated_at?: string;
}

export interface ProjectListItem {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  created_at: string;
  member_count: number;
  requirement_count: number;
  test_case_count?: number;
  user_role: 'owner' | 'member';
}
