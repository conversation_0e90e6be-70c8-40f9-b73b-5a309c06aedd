'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { apiClient } from '@/lib/api';

interface User {
  id: number;
  email: string;
  full_name?: string;
  is_active: boolean;
  auth_provider?: string;
  profile_image_url?: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName?: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { readonly children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const handleUnauthorized = useCallback(() => {
    console.debug('Handling unauthorized access - logging out user');
    setUser(null);
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('cached_tags');
  }, []);

  useEffect(() => {
    // Set up the unauthorized handler for the API client
    apiClient.setUnauthorizedHandler(handleUnauthorized);

    const checkTokenExpiry = () => {
      const token = localStorage.getItem('token');
      const refreshToken = localStorage.getItem('refresh_token');

      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Math.floor(Date.now() / 1000);

          if (payload.exp) {
            const timeUntilExpiry = payload.exp - currentTime;
            const expiryDate = new Date(payload.exp * 1000);

            console.debug(`Token expiry check: expires at ${expiryDate.toISOString()}, time until expiry: ${timeUntilExpiry} seconds`);

            if (payload.exp < currentTime) {
              // Token is expired, but don't logout immediately if we have a refresh token
              // The API client will handle the refresh automatically
              if (!refreshToken) {
                console.debug(`Token is expired and no refresh token available, logging out user`);
                handleUnauthorized();
                return false;
              } else {
                console.debug(`Token is expired but refresh token available, API client will handle refresh`);
              }
            }

            // Warn if token expires within 15 minutes (but don't logout)
            if (timeUntilExpiry < 900) {
              console.warn(`Token expires in ${Math.floor(timeUntilExpiry / 60)} minutes`);
            }
          }
          return true;
        } catch (error) {
          console.error('Error parsing token:', error);
          handleUnauthorized();
          return false;
        }
      }

      // No token but have refresh token - this is okay, API client will handle it
      return refreshToken !== null;
    };

    const initializeAuth = async () => {
      const token = localStorage.getItem('token');
      const refreshToken = localStorage.getItem('refresh_token');

      if (token || refreshToken) {
        // Check token expiry (but don't fail if expired and we have refresh token)
        checkTokenExpiry();

        try {
          const userData = await apiClient.getCurrentUser();
          setUser(userData);
        } catch (error) {
          console.error('Failed to get current user:', error);
          handleUnauthorized();
        }
      }
      setLoading(false);
    };

    initializeAuth();

    // Set up periodic token expiry check (every 2 minutes for more responsive refresh)
    const tokenCheckInterval = setInterval(() => {
      const token = localStorage.getItem('token');
      const refreshToken = localStorage.getItem('refresh_token');
      if (token || refreshToken) {
        checkTokenExpiry();
      }
    }, 2 * 60 * 1000); // 2 minutes

    // Handle browser tab close/refresh - cleanup tokens
    const handleBeforeUnload = () => {
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        // Use sendBeacon for reliable cleanup on page unload
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
        const token = localStorage.getItem('token');

        if (token) {
          try {
            // Use sendBeacon to ensure the request is sent even if the page is closing
            const data = JSON.stringify({ refresh_token: refreshToken, revoke_all: true });
            navigator.sendBeacon(`${API_BASE_URL}/auth/revoke-token`, data);
          } catch (error) {
            console.error('Error sending beacon for token cleanup:', error);
          }
        }
      }
    };

    // Add beforeunload listener for cleanup
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      clearInterval(tokenCheckInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [handleUnauthorized]); // Include handleUnauthorized dependency

  const cacheTags = useCallback(async () => {
    try {
      const tags = await apiClient.getTags();
      localStorage.setItem('cached_tags', JSON.stringify(tags));
    } catch (error) {
      console.error('Error caching tags:', error);
    }
  }, []);

  const login = useCallback(async (email: string, password: string) => {
    const response = await apiClient.login(email, password);
    localStorage.setItem('token', response.access_token);

    // Parallel requests to reduce chaining
    const [userData] = await Promise.all([
      apiClient.getCurrentUser(),
      // Cache tags in background without blocking user experience
      cacheTags().catch(error => console.error('Error caching tags:', error))
    ]);

    setUser(userData);
  }, [cacheTags]);

  const register = useCallback(async (email: string, password: string, fullName?: string) => {
    const response = await apiClient.register(email, password, fullName);
    localStorage.setItem('token', response.access_token);

    // Parallel requests to reduce chaining
    const [userData] = await Promise.all([
      apiClient.getCurrentUser(),
      // Cache tags in background without blocking user experience
      cacheTags().catch(error => console.error('Error caching tags:', error))
    ]);

    setUser(userData);
  }, [cacheTags]);

  const logout = useCallback(async () => {
    try {
      // Call API client logout to revoke refresh tokens on server
      await apiClient.logout();
    } catch (error) {
      console.error('Error during logout:', error);
    }
    setUser(null);
  }, []);

  const contextValue = useMemo(
    () => ({ user, login, register, logout, loading }),
    [user, login, register, logout, loading]
  );

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
