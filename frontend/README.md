# IntelliTest Frontend 🎨

> **Modern React Frontend with AI-Powered Code Generation UI**

The IntelliTest frontend is a sophisticated Next.js 14 application built with TypeScript, featuring a modern design system and advanced AI-powered code generation interface.

## 🚀 Features

### 🎯 **Core Application Features**
- **Authentication System**: Email/password + Google OAuth integration
- **Project Management**: Intuitive project creation with stepper wizard
- **Requirements Editor**: Real-time editing with auto-save functionality
- **Test Case Management**: AI-powered test case generation and management
- **Code Generation UI**: Revolutionary streaming code generation interface
- **Web Crawling Interface**: Intelligent page element detection and analysis

### 🎨 **UI/UX Excellence**
- **Modern Design System**: Shadcn UI with Tailwind CSS
- **Responsive Design**: Mobile-first approach with accessibility
- **Real-time Updates**: Live data synchronization across components
- **Performance Optimized**: Lazy loading, code splitting, and caching
- **Accessibility**: WCAG compliant with proper ARIA labels

### 🔧 **Technical Features**
- **TypeScript**: Full type safety across the application
- **App Router**: Next.js 14 App Router with server components
- **State Management**: React Context with optimistic updates
- **API Integration**: Custom API client with error handling and retry logic
- **Font Optimization**: Google Fonts with preloading optimization

## 🏗️ Architecture

### **Framework & Language**
- **Next.js 14**: React framework with App Router
- **TypeScript**: Strict type checking for better development experience
- **React 18**: Latest React features with concurrent rendering

### **Styling & UI**
- **Tailwind CSS**: Utility-first CSS framework with custom design system
- **Shadcn UI**: High-quality React components built on Radix UI
- **Lucide React**: Beautiful icon library
- **Custom Fonts**: Poppins, Sora, and Fira Code with optimization

### **State Management**
- **React Context**: Global state management for auth and toasts
- **Optimistic Updates**: Immediate UI updates with server synchronization
- **Local Storage**: Secure token storage with automatic cleanup

### **API Integration**
- **Custom API Client**: Centralized API communication with error handling
- **JWT Authentication**: Automatic token refresh and secure storage
- **Error Handling**: Comprehensive error boundaries and user feedback

## 📁 Project Structure

```
frontend/
├── src/
│   ├── app/                          # Next.js App Router pages
│   │   ├── (auth)/                   # Authentication pages
│   │   ├── dashboard/                # Main dashboard
│   │   ├── projects/                 # Project management
│   │   └── layout.tsx                # Root layout
│   ├── components/                   # React components
│   │   ├── ui/                       # Shadcn UI components
│   │   ├── AuthPage.tsx              # Authentication interface
│   │   ├── Dashboard.tsx             # Main dashboard
│   │   ├── ProjectView.tsx           # Project management
│   │   ├── RequirementDetailView.tsx # Requirement editing
│   │   ├── CodeGenerationModal.tsx   # AI code generation UI
│   │   ├── PageUrlDetectionModal.tsx # Web crawling interface
│   │   └── ProtectedRoute.tsx        # Route protection
│   ├── contexts/                     # React contexts
│   │   ├── AuthContext.tsx           # Authentication state
│   │   └── ToastContext.tsx          # Toast notifications
│   ├── lib/                          # Utilities and configurations
│   │   ├── api.ts                    # API client
│   │   ├── utils.ts                  # Utility functions
│   │   └── fonts.ts                  # Font configurations
│   └── types/                        # TypeScript type definitions
├── public/                           # Static assets
├── tailwind.config.js                # Tailwind CSS configuration
├── next.config.js                    # Next.js configuration
├── tsconfig.json                     # TypeScript configuration
└── package.json                      # Dependencies and scripts
```

## 🚀 Getting Started

### **Prerequisites**
- Node.js 18+ and npm/yarn
- Backend API running on http://localhost:8000

### **Development Setup**

1. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Configure environment**
   ```bash
   # Create environment file
   echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1" > .env.local
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Access the application**
   - Open [http://localhost:3000](http://localhost:3000)
   - The page auto-updates as you edit files

### **Production Build**

```bash
# Build for production
npm run build

# Start production server
npm start

# Or export static files
npm run build && npm run export
```

## 🎨 Key Components

### **Authentication Components**
- **AuthPage**: Complete authentication interface with login/register
- **ProtectedRoute**: Route protection with automatic redirects
- **Google OAuth**: Seamless social authentication integration

### **Project Management**
- **Dashboard**: Project overview with statistics and quick actions
- **ProjectView**: Comprehensive project management interface
- **ProjectShareModal**: Advanced member management with roles

### **Requirements & Testing**
- **RequirementDetailView**: Rich text editor with real-time collaboration
- **TestCaseModal**: AI-powered test case generation interface
- **TagSystem**: Color-coded organization with auto-suggestions

### **AI-Powered Features**
- **CodeGenerationModal**: Revolutionary streaming code generation UI
- **PageUrlDetectionModal**: Intelligent web crawling interface
- **Real-time Streaming**: Live progress tracking with chat interface

### **UI Components**
- **Toast System**: Beautiful notifications with auto-dismiss
- **Loading States**: Skeleton loaders and progress indicators
- **Form Components**: Validated forms with error handling
- **Modal System**: Accessible modal dialogs with proper focus management

## 🔧 Development

### **Available Scripts**

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript compiler
```

### **Environment Variables**

```bash
# Required
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Optional
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

### **Code Quality**
- **ESLint**: Code linting with Next.js recommended rules
- **Prettier**: Code formatting with consistent style
- **TypeScript**: Strict type checking for better development experience
- **Husky**: Pre-commit hooks for code quality

## 🚀 Deployment

### **Docker Deployment**
```bash
# Build Docker image
docker build -t intellitest-frontend .

# Run container
docker run -p 3000:3000 intellitest-frontend
```

### **Vercel Deployment**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### **Static Export**
```bash
# Build and export
npm run build
npm run export

# Serve static files with any web server
```

## 📚 Learn More

### **Next.js Resources**
- [Next.js Documentation](https://nextjs.org/docs) - Learn about Next.js features and API
- [Learn Next.js](https://nextjs.org/learn) - Interactive Next.js tutorial
- [Next.js GitHub](https://github.com/vercel/next.js) - Source code and contributions

### **UI/UX Resources**
- [Shadcn UI](https://ui.shadcn.com/) - Component library documentation
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Radix UI](https://www.radix-ui.com/) - Low-level UI primitives

---
