#!/usr/bin/env python3
"""
Database setup script for IntelliTest
"""
import os
import sys
from sqlalchemy import create_engine, text
from app.core.config import settings

def create_database():
    """Create the database if it doesn't exist."""
    # Connect to PostgreSQL server (not to specific database)
    server_url = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/postgres"
    
    try:
        engine = create_engine(server_url)
        with engine.connect() as conn:
            # Check if database exists
            result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{settings.POSTGRES_DB}'"))
            if not result.fetchone():
                # Create database
                conn.execute(text("COMMIT"))  # End any existing transaction
                conn.execute(text(f"CREATE DATABASE {settings.POSTGRES_DB}"))
                print(f"Database '{settings.POSTGRES_DB}' created successfully!")
            else:
                print(f"Database '{settings.POSTGRES_DB}' already exists.")
    except Exception as e:
        print(f"Error creating database: {e}")
        print("Please ensure PostgreSQL is running and credentials are correct.")
        return False
    
    return True

def test_connection():
    """Test connection to the application database."""
    try:
        engine = create_engine(settings.DATABASE_URL)
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("Database connection successful!")
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False

if __name__ == "__main__":
    print("Setting up database...")
    print(f"Database URL: {settings.DATABASE_URL}")
    
    if create_database():
        if test_connection():
            print("Database setup completed successfully!")
        else:
            print("Database setup failed!")
            sys.exit(1)
    else:
        print("Database creation failed!")
        sys.exit(1)
