#!/usr/bin/env python3

import os
import sys
import re
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

def generate_slug(name: str, max_length: int = 50) -> str:
    """Generate a URL-friendly slug from text."""
    # Convert to lowercase
    slug = name.lower()
    
    # Replace spaces and special characters with hyphens
    slug = re.sub(r'[^\w\s-]', '', slug)
    slug = re.sub(r'[-\s]+', '-', slug)
    
    # Remove leading/trailing hyphens
    slug = slug.strip('-')
    
    # Truncate to max length
    if len(slug) > max_length:
        slug = slug[:max_length].rstrip('-')
    
    # Ensure it's not empty
    if not slug:
        slug = 'item'
    
    return slug

def main():
    # Create database connection
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Fix projects without slugs
        projects = db.execute(text("SELECT id, name FROM projects WHERE slug IS NULL")).fetchall()
        print(f"Found {len(projects)} projects without slugs")
        
        used_slugs = set()
        
        for project in projects:
            base_slug = generate_slug(project.name)
            slug = base_slug
            counter = 1
            
            # Check if slug already exists
            while slug in used_slugs or db.execute(text("SELECT 1 FROM projects WHERE slug = :slug"), {"slug": slug}).fetchone():
                counter += 1
                slug = f"{base_slug}-{counter}"
            
            used_slugs.add(slug)
            
            # Update the project
            db.execute(text("UPDATE projects SET slug = :slug WHERE id = :id"), {"slug": slug, "id": project.id})
            print(f"Updated project {project.id}: '{project.name}' -> '{slug}'")
        
        # Fix requirements without slugs
        requirements = db.execute(text("SELECT id, name, project_id FROM requirements WHERE slug IS NULL")).fetchall()
        print(f"Found {len(requirements)} requirements without slugs")
        
        # Group by project to ensure uniqueness within each project
        project_slugs = {}
        
        for requirement in requirements:
            project_id = requirement.project_id
            if project_id not in project_slugs:
                # Get existing slugs for this project
                existing = db.execute(text("SELECT slug FROM requirements WHERE project_id = :project_id AND slug IS NOT NULL"), {"project_id": project_id}).fetchall()
                project_slugs[project_id] = set(row.slug for row in existing)
            
            base_slug = generate_slug(requirement.name)
            slug = base_slug
            counter = 1
            
            # Check if slug already exists in this project
            while slug in project_slugs[project_id]:
                counter += 1
                slug = f"{base_slug}-{counter}"
            
            project_slugs[project_id].add(slug)
            
            # Update the requirement
            db.execute(text("UPDATE requirements SET slug = :slug WHERE id = :id"), {"slug": slug, "id": requirement.id})
            print(f"Updated requirement {requirement.id}: '{requirement.name}' -> '{slug}'")
        
        # Commit all changes
        db.commit()
        print("All slugs updated successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
