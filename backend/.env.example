# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=intellitest
POSTGRES_PORT=5432

# JWT Configuration
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=960

# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2:latest

# Logging Configuration
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_RETENTION_DAYS=5

# Email Configuration (for password reset emails)
# For Gmail, you'll need to use an App Password instead of your regular password
# Go to Google Account settings > Security > 2-Step Verification > App passwords
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=IntelliTest
FRONTEND_URL=http://localhost:3000

# Alternative SMTP providers:
# For Outlook/Hotmail:
# SMTP_SERVER=smtp-mail.outlook.com
# SMTP_PORT=587

# For Yahoo:
# SMTP_SERVER=smtp.mail.yahoo.com
# SMTP_PORT=587

# For custom SMTP server:
# SMTP_SERVER=your-smtp-server.com
# SMTP_PORT=587
