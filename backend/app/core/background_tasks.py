"""Background tasks for the application."""
import asyncio
import logging
from datetime import datetime, timezone
from app.db.session import <PERSON>Local
from app.crud.refresh_token import cleanup_expired_tokens

logger = logging.getLogger(__name__)

# Global variable to store the cleanup task
_cleanup_task = None

async def cleanup_expired_tokens_task():
    """Background task to periodically clean up expired refresh tokens."""
    while True:
        try:
            db = SessionLocal()
            try:
                count = cleanup_expired_tokens(db)
                if count > 0:
                    logger.info(f"Cleaned up {count} expired refresh tokens")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Error in token cleanup task: {e}")
        
        # Wait 1 hour before next cleanup
        await asyncio.sleep(3600)  # 1 hour

async def start_token_cleanup_task():
    """Start the token cleanup background task."""
    global _cleanup_task
    if _cleanup_task is None or _cleanup_task.done():
        _cleanup_task = asyncio.create_task(cleanup_expired_tokens_task())
        logger.info("Started token cleanup background task")
    return _cleanup_task

def stop_token_cleanup_task():
    """Stop the token cleanup background task."""
    global _cleanup_task
    if _cleanup_task and not _cleanup_task.done():
        _cleanup_task.cancel()
        logger.info("Stopped token cleanup background task")
