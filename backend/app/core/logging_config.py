import logging
import logging.handlers
import os
import glob
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

class DailyRotatingFileHandler(logging.handlers.TimedRotatingFileHandler):
    """Custom handler that automatically cleans up old log files."""
    
    def __init__(self, filename: str, when: str = 'midnight', interval: int = 1, 
                 backupCount: int = 5, encoding: Optional[str] = None, 
                 delay: bool = False, utc: bool = False, atTime=None):
        super().__init__(filename, when, interval, backupCount, encoding, delay, utc, atTime)
        self.cleanup_old_logs()
    
    def doRollover(self):
        """Override to perform cleanup after rollover."""
        super().doRollover()
        self.cleanup_old_logs()
    
    def cleanup_old_logs(self):
        """Remove log files older than backupCount days."""
        try:
            log_dir = os.path.dirname(self.baseFilename)
            base_name = os.path.basename(self.baseFilename)
            
            # Find all log files matching the pattern
            pattern = os.path.join(log_dir, f"{base_name}.*")
            log_files = glob.glob(pattern)
            
            # Get current time
            now = datetime.now()
            cutoff_time = now - timedelta(days=self.backupCount)
            
            for log_file in log_files:
                try:
                    # Get file modification time
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    
                    # Remove if older than cutoff
                    if file_time < cutoff_time:
                        os.remove(log_file)
                        # Log cleanup is silent to avoid circular logging
                except (OSError, ValueError):
                    # Ignore cleanup errors to avoid circular logging
                    pass

        except Exception:
            # Ignore cleanup errors to avoid circular logging
            pass

def setup_logging(log_level: str = "INFO", log_dir: str = "logs") -> logging.Logger:
    """
    Set up logging with daily rotation and automatic cleanup.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to store log files
        
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    Path(log_dir).mkdir(exist_ok=True)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler with daily rotation (keep 5 days) - WARN level minimum
    log_file = os.path.join(log_dir, "app.log")
    file_handler = DailyRotatingFileHandler(
        filename=log_file,
        when='midnight',
        interval=1,
        backupCount=5,  # Keep 5 days of logs
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.WARNING)  # File logs only WARN and above
    logger.addHandler(file_handler)
    
    # Console handler for development
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    logger.addHandler(console_handler)
    
    # Set specific loggers to appropriate levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for a specific module."""
    return logging.getLogger(name)

# Application-specific loggers
def get_api_logger() -> logging.Logger:
    """Get logger for API operations."""
    return logging.getLogger("api")

def get_db_logger() -> logging.Logger:
    """Get logger for database operations."""
    return logging.getLogger("database")

def get_auth_logger() -> logging.Logger:
    """Get logger for authentication operations."""
    return logging.getLogger("auth")

def get_llm_logger() -> logging.Logger:
    """Get logger for LLM service operations."""
    return logging.getLogger("llm")
