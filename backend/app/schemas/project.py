from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from app.models.project import AutomationFramework, ProgrammingLanguage
from app.models.project_member import MemberRole

class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    code_source: Optional[str] = None
    automation_framework: Optional[AutomationFramework] = None
    programming_language: Optional[ProgrammingLanguage] = None

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    code_source: Optional[str] = None
    automation_framework: Optional[AutomationFramework] = None
    programming_language: Optional[ProgrammingLanguage] = None

class ProjectMemberBase(BaseModel):
    user_id: int
    role: MemberRole = MemberRole.MEMBER

class ProjectMemberCreate(ProjectMemberBase):
    pass

class ProjectMember(ProjectMemberBase):
    id: int
    project_id: int
    user_email: str
    user_name: Optional[str] = None
    joined_at: datetime

    class Config:
        from_attributes = True

class ProjectInDBBase(ProjectBase):
    id: int
    slug: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Project(ProjectInDBBase):
    members: List[ProjectMember] = []

class ProjectInDB(ProjectInDBBase):
    pass

class ProjectListItem(BaseModel):
    id: int
    name: str
    slug: str
    description: Optional[str] = None
    created_at: datetime
    member_count: int
    requirement_count: int
    test_case_count: int
    user_role: MemberRole

    class Config:
        from_attributes = True
