from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from app.models.code_generation import CodeGenerationStatus, CodeGenerationType

class CodeGenerationRequest(BaseModel):
    """Schema for code generation request."""
    requirement_id: int
    project_id: int
    git_url: Optional[str] = None
    ssh_private_key: Optional[str] = None
    ssh_passphrase: Optional[str] = None
    automation_framework: str
    programming_language: str
    page_elements: Optional[Dict[str, Any]] = None
    additional_context: Optional[str] = None

class CodeGenerationSessionCreate(BaseModel):
    """Schema for creating a code generation session."""
    project_id: int
    requirement_id: int
    generation_type: CodeGenerationType
    git_url: Optional[str] = None
    automation_framework: str
    programming_language: str
    test_cases_data: Optional[Dict[str, Any]] = None
    page_elements_data: Optional[Dict[str, Any]] = None

class CodeGenerationSessionUpdate(BaseModel):
    """Schema for updating a code generation session."""
    status: Optional[CodeGenerationStatus] = None
    generated_files_count: Optional[int] = None
    zip_file_path: Optional[str] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    existing_code_context: Optional[str] = None

class GeneratedFileBase(BaseModel):
    """Base schema for generated files."""
    file_path: str
    file_type: str
    file_size: int
    description: Optional[str] = None
    is_modified: bool = False

class GeneratedFileCreate(GeneratedFileBase):
    """Schema for creating a generated file record."""
    content_hash: Optional[str] = None
    original_file_backup: Optional[str] = None

class GeneratedFile(GeneratedFileBase):
    """Schema for generated file response."""
    id: int
    session_id: int
    content_hash: Optional[str] = None
    is_saved: bool
    save_error: Optional[str] = None
    created_at: datetime
    saved_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class CodeGenerationLogCreate(BaseModel):
    """Schema for creating a code generation log entry."""
    log_level: str
    message: str
    event_type: Optional[str] = None
    log_metadata: Optional[Dict[str, Any]] = None

class CodeGenerationLog(BaseModel):
    """Schema for code generation log response."""
    id: int
    session_id: int
    log_level: str
    message: str
    event_type: Optional[str] = None
    log_metadata: Optional[Dict[str, Any]] = None
    created_at: datetime

    class Config:
        from_attributes = True

class CodeGenerationSession(BaseModel):
    """Schema for code generation session response."""
    id: int
    project_id: int
    requirement_id: int
    user_id: int
    session_id: str
    status: CodeGenerationStatus
    generation_type: CodeGenerationType
    git_url: Optional[str] = None
    automation_framework: str
    programming_language: str
    generated_files_count: int
    zip_file_path: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class CodeGenerationSessionWithFiles(CodeGenerationSession):
    """Schema for code generation session with generated files."""
    generated_files: List[GeneratedFile] = []

class CodeGenerationSessionWithLogs(CodeGenerationSession):
    """Schema for code generation session with logs."""
    logs: List[CodeGenerationLog] = []

class ProjectCodeMetadataBase(BaseModel):
    """Base schema for project code metadata."""
    git_url: Optional[str] = None
    is_empty_repo: Optional[bool] = None
    total_files: int = 0
    code_files_count: int = 0

class ProjectCodeMetadataCreate(ProjectCodeMetadataBase):
    """Schema for creating project code metadata."""
    project_id: int

class ProjectCodeMetadataUpdate(BaseModel):
    """Schema for updating project code metadata."""
    git_url: Optional[str] = None
    last_clone_at: Optional[datetime] = None
    clone_status: Optional[str] = None
    clone_error: Optional[str] = None
    is_empty_repo: Optional[bool] = None
    total_files: Optional[int] = None
    code_files_count: Optional[int] = None
    last_analysis_at: Optional[datetime] = None
    is_embedded: Optional[bool] = None
    embedding_status: Optional[str] = None
    embedded_files_count: Optional[int] = None
    last_embedding_at: Optional[datetime] = None
    embedding_error: Optional[str] = None
    total_generations: Optional[int] = None
    successful_generations: Optional[int] = None
    last_generation_at: Optional[datetime] = None

class ProjectCodeMetadata(ProjectCodeMetadataBase):
    """Schema for project code metadata response."""
    id: int
    project_id: int
    last_clone_at: Optional[datetime] = None
    clone_status: Optional[str] = None
    clone_error: Optional[str] = None
    last_analysis_at: Optional[datetime] = None
    is_embedded: bool
    embedding_status: Optional[str] = None
    embedded_files_count: int
    last_embedding_at: Optional[datetime] = None
    embedding_error: Optional[str] = None
    total_generations: int
    successful_generations: int
    last_generation_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class CodeGenerationProgress(BaseModel):
    """Schema for streaming code generation progress."""
    session_id: str
    type: str  # 'status', 'file', 'structure', 'analysis', 'complete', 'error'
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class CodeGenerationStreamResponse(BaseModel):
    """Schema for streaming response."""
    event: str
    data: Dict[str, Any]

class CodeGenerationSummary(BaseModel):
    """Schema for code generation summary."""
    session_id: str
    status: CodeGenerationStatus
    total_files: int
    generated_files: int
    failed_files: int
    generation_time_seconds: Optional[float] = None
    zip_file_size: Optional[int] = None
    error_summary: Optional[str] = None

class RepositoryAnalysis(BaseModel):
    """Schema for repository analysis results."""
    is_empty: bool
    total_files: int
    code_files: int
    test_files: int
    config_files: int
    documentation_files: int
    structure: Dict[str, Any]
    detected_frameworks: List[str] = []
    detected_languages: List[str] = []
    recommendations: List[str] = []

class CodeGenerationStats(BaseModel):
    """Schema for code generation statistics."""
    total_sessions: int
    successful_sessions: int
    failed_sessions: int
    in_progress_sessions: int
    total_files_generated: int
    average_generation_time: Optional[float] = None
    most_common_frameworks: List[Dict[str, Any]] = []
    most_common_languages: List[Dict[str, Any]] = []
    recent_sessions: List[CodeGenerationSession] = []

class FileContent(BaseModel):
    """Schema for file content."""
    path: str
    content: str
    type: str
    description: Optional[str] = None
    size: int

class GeneratedCodeResponse(BaseModel):
    """Schema for generated code response."""
    files: List[FileContent]
    structure: Dict[str, Any]
    summary: str
    recommendations: List[str] = []
