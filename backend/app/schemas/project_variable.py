from typing import Optional
from pydantic import BaseModel
from datetime import datetime

class ProjectVariableBase(BaseModel):
    key: str
    value: str
    description: Optional[str] = None

class ProjectVariableCreate(ProjectVariableBase):
    pass

class ProjectVariableUpdate(ProjectVariableBase):
    key: Optional[str] = None
    value: Optional[str] = None

class ProjectVariableInDBBase(ProjectVariableBase):
    id: int
    project_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProjectVariable(ProjectVariableInDBBase):
    pass

class ProjectVariableInDB(ProjectVariableInDBBase):
    pass
