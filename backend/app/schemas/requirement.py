from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime

class RequirementBase(BaseModel):
    name: str
    description: str
    refined_description: Optional[str] = None

class RequirementCreate(RequirementBase):
    project_id: int
    tag_names: List[str] = []

class RequirementUpdate(RequirementBase):
    name: Optional[str] = None
    description: Optional[str] = None
    tag_names: Optional[List[str]] = None

class TagBase(BaseModel):
    name: str
    color: Optional[str] = None

class TagCreate(TagBase):
    pass

class Tag(TagBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class TestCaseBase(BaseModel):
    title: str
    steps: str
    expected_result: Optional[str] = None
    notes: Optional[str] = None

class TestCaseCreate(TestCaseBase):
    custom_id: Optional[str] = None  # Will be auto-generated if not provided

class TestCase(TestCaseBase):
    id: int
    custom_id: str
    requirement_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class RequirementInDBBase(RequirementBase):
    id: int
    slug: str
    project_id: int
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Requirement(RequirementInDBBase):
    tags: List[Tag] = []
    test_cases: List[TestCase] = []

class RequirementInDB(RequirementInDBBase):
    pass

class RequirementListItem(BaseModel):
    id: int
    name: str
    slug: str
    created_at: datetime
    created_by: int
    created_by_name: Optional[str] = None
    tags: List[Tag] = []
    test_case_count: int = 0

    class Config:
        from_attributes = True
