import re
from typing import Optional
from sqlalchemy.orm import Session


def generate_slug(text: str, max_length: int = 50) -> str:
    """
    Generate a URL-friendly slug from text.
    
    Args:
        text: The text to convert to a slug
        max_length: Maximum length of the slug
        
    Returns:
        A URL-friendly slug
    """
    # Convert to lowercase
    slug = text.lower()
    
    # Replace spaces and special characters with hyphens
    slug = re.sub(r'[^\w\s-]', '', slug)
    slug = re.sub(r'[-\s]+', '-', slug)
    
    # Remove leading/trailing hyphens
    slug = slug.strip('-')
    
    # Truncate to max length
    if len(slug) > max_length:
        slug = slug[:max_length].rstrip('-')
    
    # Ensure it's not empty
    if not slug:
        slug = 'item'
    
    return slug


def generate_unique_slug(
    db: Session, 
    model_class, 
    text: str, 
    max_length: int = 50,
    parent_id_field: Optional[str] = None,
    parent_id: Optional[int] = None
) -> str:
    """
    Generate a unique slug for a model.
    
    Args:
        db: Database session
        model_class: The SQLAlchemy model class
        text: The text to convert to a slug
        max_length: Maximum length of the slug
        parent_id_field: Field name for parent ID (e.g., 'project_id')
        parent_id: Parent ID value for scoped uniqueness
        
    Returns:
        A unique slug
    """
    base_slug = generate_slug(text, max_length - 4)  # Reserve space for counter
    slug = base_slug
    counter = 1
    
    while True:
        # Build query
        query = db.query(model_class).filter(model_class.slug == slug)
        
        # Add parent scope if specified
        if parent_id_field and parent_id is not None:
            parent_field = getattr(model_class, parent_id_field)
            query = query.filter(parent_field == parent_id)
        
        # Check if slug exists
        if not query.first():
            return slug
        
        # Generate next slug with counter
        counter += 1
        slug = f"{base_slug}-{counter}"
        
        # Ensure we don't exceed max length
        if len(slug) > max_length:
            # Truncate base slug to make room for counter
            available_length = max_length - len(f"-{counter}")
            truncated_base = base_slug[:available_length].rstrip('-')
            slug = f"{truncated_base}-{counter}"


def generate_project_slug(db: Session, name: str) -> str:
    """Generate a unique slug for a project."""
    from app.models.project import Project
    return generate_unique_slug(db, Project, name)


def generate_requirement_slug(db: Session, name: str, project_id: int) -> str:
    """Generate a unique slug for a requirement within a project."""
    from app.models.requirement import Requirement
    return generate_unique_slug(
        db, 
        Requirement, 
        name, 
        parent_id_field='project_id', 
        parent_id=project_id
    )
