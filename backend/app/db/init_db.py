from app.db.base import Base, engine
from app.models import user, project, project_member, requirement, test_case, tag, project_variable, code_generation

def create_tables():
    """Create all tables in the database."""
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """Drop all tables in the database."""
    Base.metadata.drop_all(bind=engine)

if __name__ == "__main__":
    create_tables()
    print("Database tables created successfully!")
