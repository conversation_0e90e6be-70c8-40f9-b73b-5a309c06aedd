from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON>olean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import uuid
from datetime import datetime, timezone, timedelta


class RefreshToken(Base):
    """Model for storing refresh tokens."""
    __tablename__ = "refresh_tokens"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    token = Column(String, unique=True, nullable=False, index=True)
    
    # Token metadata
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Security and tracking
    is_active = Column(Boolean, default=True, nullable=False)
    device_info = Column(Text, nullable=True)  # Store device/browser info
    ip_address = Column(String, nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Revocation info
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    revoked_reason = Column(String, nullable=True)  # 'logout', 'security', 'expired', etc.
    
    # Relationships
    user = relationship("User", back_populates="refresh_tokens")
    
    @classmethod
    def generate_token(cls) -> str:
        """Generate a secure random refresh token."""
        return str(uuid.uuid4())
    
    @classmethod
    def create_for_user(
        cls, 
        user_id: int, 
        expires_in_days: int = 30,
        device_info: str = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> 'RefreshToken':
        """Create a new refresh token for a user."""
        token = cls.generate_token()
        expires_at = datetime.now(timezone.utc) + timedelta(days=expires_in_days)
        
        return cls(
            user_id=user_id,
            token=token,
            expires_at=expires_at,
            device_info=device_info,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def is_expired(self) -> bool:
        """Check if the refresh token is expired."""
        return datetime.now(timezone.utc) > self.expires_at
    
    def is_valid(self) -> bool:
        """Check if the refresh token is valid (active and not expired)."""
        return self.is_active and not self.is_expired()
    
    def revoke(self, reason: str = "manual"):
        """Revoke the refresh token."""
        self.is_active = False
        self.revoked_at = datetime.now(timezone.utc)
        self.revoked_reason = reason
    
    def update_last_used(self):
        """Update the last used timestamp."""
        self.last_used_at = datetime.now(timezone.utc)
    
    def extend_expiration(self, days: int = 30):
        """Extend the token expiration (for sliding sessions)."""
        self.expires_at = datetime.now(timezone.utc) + timedelta(days=days)
