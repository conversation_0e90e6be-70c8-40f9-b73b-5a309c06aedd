from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

class TestCase(Base):
    __tablename__ = "test_cases"

    id = Column(Integer, primary_key=True, index=True)
    custom_id = Column(String, nullable=False, index=True)  # TC-001, TC-002, etc.
    title = Column(String, nullable=False)
    steps = Column(Text, nullable=True)
    expected_result = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)  # Add notes field
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    requirement = relationship("Requirement", back_populates="test_cases")
