from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

class Requirement(Base):
    __tablename__ = "requirements"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    slug = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=False)
    refined_description = Column(Text, nullable=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    __table_args__ = (UniqueConstraint('project_id', 'slug', name='_project_requirement_slug_uc'),)

    # Relationships
    project = relationship("Project", back_populates="requirements")
    created_by_user = relationship("User", back_populates="created_requirements")
    test_cases = relationship("TestCase", back_populates="requirement", cascade="all, delete-orphan")
    requirement_tags = relationship("RequirementTag", back_populates="requirement", cascade="all, delete-orphan")
    tags = relationship("Tag", secondary="requirement_tags", viewonly=True)
    code_generation_sessions = relationship("CodeGenerationSession", back_populates="requirement", cascade="all, delete-orphan")
