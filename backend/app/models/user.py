from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    auth_provider = Column(String, default="email", nullable=False)  # "email" or "google"
    profile_image_url = Column(String, nullable=True)  # For Google profile images
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project_memberships = relationship("ProjectMember", back_populates="user", cascade="all, delete-orphan")
    created_requirements = relationship("Requirement", back_populates="created_by_user")  # Don't cascade delete requirements when user is deleted
    code_generation_sessions = relationship("CodeGenerationSession", back_populates="user")  # Don't cascade delete sessions when user is deleted
    refresh_tokens = relationship("RefreshToken", back_populates="user", cascade="all, delete-orphan")
