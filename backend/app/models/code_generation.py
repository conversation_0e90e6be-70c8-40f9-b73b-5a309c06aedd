from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class CodeGenerationStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class CodeGenerationType(str, enum.Enum):
    EMPTY_REPO = "empty_repo"
    EXISTING_REPO = "existing_repo"
    STANDALONE = "standalone"

class CodeGenerationSession(Base):
    """Model for tracking code generation sessions."""
    __tablename__ = "code_generation_sessions"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False)
    user_id = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=False)
    
    # Session details
    session_id = Column(String, unique=True, nullable=False, index=True)  # UUID for tracking
    status = Column(Enum(CodeGenerationStatus), default=CodeGenerationStatus.PENDING)
    generation_type = Column(Enum(CodeGenerationType), nullable=False)
    
    # Configuration
    git_url = Column(String, nullable=True)
    automation_framework = Column(String, nullable=False)
    programming_language = Column(String, nullable=False)
    
    # Generation data
    test_cases_data = Column(JSON, nullable=True)  # Serialized test cases
    page_elements_data = Column(JSON, nullable=True)  # Serialized page elements
    existing_code_context = Column(Text, nullable=True)  # Context from existing code
    
    # Results
    generated_files_count = Column(Integer, default=0)
    zip_file_path = Column(String, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="code_generation_sessions")
    requirement = relationship("Requirement", back_populates="code_generation_sessions")
    user = relationship("User", back_populates="code_generation_sessions")
    generated_files = relationship("GeneratedFile", back_populates="session", cascade="all, delete-orphan")

class GeneratedFile(Base):
    """Model for tracking individual generated files."""
    __tablename__ = "generated_files"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("code_generation_sessions.id"), nullable=False)
    
    # File details
    file_path = Column(String, nullable=False)  # Relative path within the repository
    file_type = Column(String, nullable=False)  # e.g., 'test_class', 'page_object', 'config'
    file_size = Column(Integer, nullable=False)  # Size in bytes
    content_hash = Column(String, nullable=True)  # Hash of the content for integrity
    
    # Metadata
    description = Column(Text, nullable=True)
    is_modified = Column(Boolean, default=False)  # Whether this file modified an existing file
    original_file_backup = Column(Text, nullable=True)  # Backup of original content if modified
    
    # Status
    is_saved = Column(Boolean, default=False)
    save_error = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    saved_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    session = relationship("CodeGenerationSession", back_populates="generated_files")

class CodeGenerationLog(Base):
    """Model for logging code generation progress and events."""
    __tablename__ = "code_generation_logs"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("code_generation_sessions.id"), nullable=False)
    
    # Log details
    log_level = Column(String, nullable=False)  # INFO, WARNING, ERROR, DEBUG
    message = Column(Text, nullable=False)
    event_type = Column(String, nullable=True)  # e.g., 'file_generated', 'error_occurred'
    
    # Additional data
    log_metadata = Column(JSON, nullable=True)  # Additional structured data
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    session = relationship("CodeGenerationSession")

class ProjectCodeMetadata(Base):
    """Model for storing metadata about project code repositories."""
    __tablename__ = "project_code_metadata"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, unique=True)
    
    # Repository information
    git_url = Column(String, nullable=True)
    last_clone_at = Column(DateTime(timezone=True), nullable=True)
    clone_status = Column(String, nullable=True)  # 'success', 'failed', 'pending'
    clone_error = Column(Text, nullable=True)
    
    # Repository analysis
    is_empty_repo = Column(Boolean, nullable=True)
    total_files = Column(Integer, default=0)
    code_files_count = Column(Integer, default=0)
    last_analysis_at = Column(DateTime(timezone=True), nullable=True)
    
    # Code embedding status
    is_embedded = Column(Boolean, default=False)
    embedding_status = Column(String, nullable=True)  # 'pending', 'in_progress', 'completed', 'failed'
    embedded_files_count = Column(Integer, default=0)
    last_embedding_at = Column(DateTime(timezone=True), nullable=True)
    embedding_error = Column(Text, nullable=True)
    
    # Statistics
    total_generations = Column(Integer, default=0)
    successful_generations = Column(Integer, default=0)
    last_generation_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="code_metadata")

# Add relationships to existing models
# These would be added to the existing model files

# In app/models/project.py, add:
# code_generation_sessions = relationship("CodeGenerationSession", back_populates="project")
# code_metadata = relationship("ProjectCodeMetadata", back_populates="project", uselist=False)

# In app/models/requirement.py, add:
# code_generation_sessions = relationship("CodeGenerationSession", back_populates="requirement")

# In app/models/user.py, add:
# code_generation_sessions = relationship("CodeGenerationSession", back_populates="user")
