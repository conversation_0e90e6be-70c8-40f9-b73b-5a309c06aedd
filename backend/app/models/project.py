from sqlalchemy import Column, Integer, String, DateTime, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class AutomationFramework(str, enum.Enum):
    SELENIUM = "selenium"
    PLAYWRIGHT = "playwright"

class ProgrammingLanguage(str, enum.Enum):
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    JAVA = "java"
    CSHARP = "csharp"

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    slug = Column(String, nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    code_source = Column(String, nullable=True)  # Git repo URL or local path
    automation_framework = Column(Enum(AutomationFramework), nullable=True)
    programming_language = Column(Enum(ProgrammingLanguage), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    members = relationship("ProjectMember", back_populates="project", cascade="all, delete-orphan")
    requirements = relationship("Requirement", back_populates="project", cascade="all, delete-orphan")
    variables = relationship("ProjectVariable", back_populates="project", cascade="all, delete-orphan")
    code_generation_sessions = relationship("CodeGenerationSession", back_populates="project", cascade="all, delete-orphan")
