from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

class Tag(Base):
    __tablename__ = "tags"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True, index=True)
    color = Column(String, nullable=True)  # Hex color code
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    requirement_tags = relationship("RequirementTag", back_populates="tag")

class RequirementTag(Base):
    __tablename__ = "requirement_tags"

    id = Column(Integer, primary_key=True, index=True)
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False)
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    __table_args__ = (UniqueConstraint('requirement_id', 'tag_id', name='_requirement_tag_uc'),)

    # Relationships
    requirement = relationship("Requirement", back_populates="requirement_tags")
    tag = relationship("Tag", back_populates="requirement_tags")
