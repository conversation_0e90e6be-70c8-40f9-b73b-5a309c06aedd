from typing import Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.core.security import verify_token
from app.crud.user import get_user
from app.models.user import User
import logging

logger = logging.getLogger(__name__)

security = HTTPBearer()

def get_current_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Get current authenticated user."""
    logger.debug("Authentication started")

    try:
        token = credentials.credentials
        logger.debug(f"Token received: {token[:20]}..." if token else "No token")

        user_id = verify_token(token)
        logger.debug(f"Token verification result: {user_id}")

        if user_id is None:
            logger.error("Token verification failed - user_id is None")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.debug(f"Looking up user with ID: {user_id}")
        user = get_user(db, user_id=int(user_id))

        if user is None:
            logger.error(f"User not found in database: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        if not user.is_active:
            logger.error(f"User is inactive: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )

        logger.debug(f"Authentication successful for user: {user.id} ({user.email})")
        return user

    except HTTPException as e:
        logger.error(f"Authentication failed with HTTP {e.status_code}: {e.detail}")
        raise
    except Exception as e:
        logger.error(f"Unexpected authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication error"
        )

def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Get current active user."""
    return current_user
