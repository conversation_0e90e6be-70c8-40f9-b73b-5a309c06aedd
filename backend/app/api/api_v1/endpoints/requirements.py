from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_active_user
from app.crud.requirement import (
    get_requirements_for_project,
    get_requirement as crud_get_requirement,
    get_requirement_by_slug,
    create_requirement as crud_create_requirement,
    update_requirement as crud_update_requirement,
    delete_requirement as crud_delete_requirement,
    get_tags
)
from app.crud.test_case import (
    get_test_cases_for_requirement,
    create_test_case,
    update_test_case,
    delete_test_case,
    get_test_case,
    create_multiple_test_cases
)
from app.crud.project_variable import get_project_variables, replace_variable_references
from app.services.llm_service import llm_service
from app.services.rag_service import rag_service
from app.crud.project import is_project_member, get_user_role_in_project, get_project_by_slug
from app.models.user import User
from app.models.project_member import MemberRole
from app.schemas.requirement import (
    Requirement, RequirementCreate, RequirementUpdate, RequirementListItem, Tag, TestCase, TestCaseCreate
)

router = APIRouter()

@router.get("/", response_model=List[RequirementListItem])
def get_requirements(
    project_id: int = Query(..., description="Project ID"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all requirements for a project."""
    # Check if user is a member of the project
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )

    requirements_with_counts = get_requirements_for_project(db, project_id, skip=skip, limit=limit)

    result = []
    for requirement, test_case_count, created_by_name in requirements_with_counts:
        result.append(RequirementListItem(
            id=requirement.id,
            name=requirement.name,
            slug=requirement.slug,
            created_at=requirement.created_at,
            created_by=requirement.created_by,
            created_by_name=created_by_name,
            tags=requirement.tags,
            test_case_count=test_case_count or 0
        ))

    return result

@router.post("/", response_model=Requirement)
def create_requirement(
    requirement: RequirementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new requirement."""
    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create requirements in this project"
        )

    return crud_create_requirement(db, requirement, current_user.id)

@router.get("/{requirement_id}", response_model=Requirement)
def get_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get requirement details."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this requirement"
        )

    return requirement

@router.get("/project/{project_slug}/requirement/{requirement_slug}", response_model=Requirement)
def get_requirement_by_slug_endpoint(
    project_slug: str,
    requirement_slug: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get requirement details by project and requirement slugs."""
    # First get the project by slug
    project = get_project_by_slug(db, project_slug)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, project.id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )

    # Get the requirement by slug within the project
    requirement = get_requirement_by_slug(db, project.id, requirement_slug)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    return requirement

@router.put("/{requirement_id}", response_model=Requirement)
def update_requirement(
    requirement_id: int,
    requirement_update: RequirementUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a requirement."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this requirement"
        )

    updated_requirement = crud_update_requirement(db, requirement_id, requirement_update)
    return updated_requirement

@router.delete("/{requirement_id}")
def delete_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Delete a requirement."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    

    success = crud_delete_requirement(db, requirement_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    return {"message": "Requirement deleted successfully"}

@router.get("/tags/", response_model=List[Tag])
def get_available_tags(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all available tags."""
    return get_tags(db, skip=skip, limit=limit)

# Test Cases endpoints
@router.get("/{requirement_id}/test-cases", response_model=List[TestCase])
def get_test_cases(
    requirement_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all test cases for a requirement."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this requirement"
        )

    return get_test_cases_for_requirement(db, requirement_id, skip=skip, limit=limit)

@router.post("/{requirement_id}/test-cases", response_model=TestCase)
def create_test_case_endpoint(
    requirement_id: int,
    test_case: TestCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new test case for a requirement."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create test cases for this requirement"
        )

    return create_test_case(db, test_case, requirement_id)

@router.put("/{requirement_id}/test-cases/{test_case_id}", response_model=TestCase)
def update_test_case_endpoint(
    requirement_id: int,
    test_case_id: int,
    test_case_update: TestCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a test case."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update test cases for this requirement"
        )

    test_case = update_test_case(db, test_case_id, test_case_update)
    if not test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Test case not found"
        )

    return test_case

@router.delete("/{requirement_id}/test-cases/{test_case_id}")
def delete_test_case_endpoint(
    requirement_id: int,
    test_case_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Delete a test case."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    

    success = delete_test_case(db, test_case_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Test case not found"
        )

    return {"message": "Test case deleted successfully"}

@router.post("/{requirement_id}/refine")
async def refine_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Refine a requirement using LLM."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to refine this requirement"
        )

    # Get project variables for context
    variables = get_project_variables(db, requirement.project_id)
    context = f"Project variables: {', '.join([f'{v.key}={v.value}' for v in variables])}" if variables else ""

    # Process requirement text with variable substitution
    processed_text = replace_variable_references(requirement.description, variables)

    # Get RAG context with conflict detection from similar requirements
    rag_context_with_conflicts = rag_service.get_context_with_conflict_detection(
        processed_text,
        requirement.project_id,
        current_requirement_id=requirement.id
    )

    # Call LLM service with conflict detection
    refined_text = await llm_service.refine_requirement(
        processed_text,
        context,
        rag_context_with_conflicts["context"],
        rag_context_with_conflicts["conflicts"]
    )

    if not refined_text:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refine requirement. Please check if Ollama is running."
        )

    # Don't save to database - just return the refined text for UI to handle
    return {
        "message": "Requirement refined successfully",
        "refined_description": refined_text
    }

@router.post("/{requirement_id}/generate-tests", response_model=List[TestCase])
async def generate_test_cases(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Generate test cases for a requirement using LLM."""
    requirement = crud_get_requirement(db, requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requirement not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, requirement.project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to generate test cases for this requirement"
        )

    # Get existing test cases for this requirement to avoid duplicates
    existing_test_cases = get_test_cases_for_requirement(db, requirement_id)

    # Get project variables for context
    variables = get_project_variables(db, requirement.project_id)
    context = f"Project variables: {', '.join([f'{v.key}={v.value}' for v in variables])}" if variables else ""

    # Use refined description if available, otherwise use original
    requirement_text = requirement.refined_description or requirement.description
    processed_text = replace_variable_references(requirement_text, variables)

    # Get RAG context from similar requirements
    rag_context = rag_service.get_context_for_requirement(processed_text, requirement.project_id)

    # Call LLM service with existing test cases context
    test_cases_data = await llm_service.generate_test_cases(processed_text, context, rag_context, existing_test_cases)

    if not test_cases_data:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="We encountered an issue while generating test cases. Please try again shortly."
        )

    # Create test cases in database
    try:
        created_test_cases = create_multiple_test_cases(db, test_cases_data, requirement_id)
        return created_test_cases
    except Exception as e:
        print(f"Error creating test cases in database: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="We encountered an issue while saving test cases. Please try again shortly."
        )
