from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_active_user
from app.crud.project import (
    get_projects_with_stats,
    get_project,
    get_project_by_slug,
    create_project as crud_create_project,
    update_project as crud_update_project,
    delete_project as crud_delete_project,
    get_user_role_in_project,
    is_project_member,
    get_project_members,
    add_project_member,
    update_project_member_role,
    remove_project_member
)
from app.crud.project_variable import (
    get_project_variables as crud_get_project_variables,
    create_project_variable,
    update_project_variable,
    delete_project_variable,
    get_project_variable_by_key
)
from app.models.user import User
from app.models.project_member import MemberRole
from app.schemas.project import Project, ProjectCreate, ProjectUpdate, ProjectListItem, ProjectMember, ProjectMemberCreate
from app.schemas.project_variable import ProjectVariable, ProjectVariableCreate, ProjectVariableUpdate

router = APIRouter()

@router.get("/", response_model=List[ProjectListItem])
def get_projects(
    skip: int = Query(0, ge=0, description="Number of projects to skip"),
    limit: int = Query(100, ge=1, le=200, description="Maximum number of projects to return"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all projects for the current user with optimized queries."""
    projects_with_stats = get_projects_with_stats(db, current_user.id, skip=skip, limit=limit)

    # Pre-fetch user roles to avoid N+1 queries
    project_ids = [project.id for project, _, _, _ in projects_with_stats]
    user_roles = {}
    if project_ids:
        # This could be optimized further with a single query
        for project_id in project_ids:
            user_roles[project_id] = get_user_role_in_project(db, project_id, current_user.id)

    result = []
    for project, member_count, requirement_count, test_case_count in projects_with_stats:
        result.append(ProjectListItem(
            id=project.id,
            name=project.name,
            slug=project.slug,
            description=project.description,
            created_at=project.created_at,
            member_count=member_count or 0,
            requirement_count=requirement_count or 0,
            test_case_count=test_case_count or 0,
            user_role=user_roles.get(project.id)
        ))

    return result

@router.post("/", response_model=Project)
def create_project(
    project: ProjectCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new project."""
    return crud_create_project(db, project, current_user.id)

@router.get("/{project_id}", response_model=Project)
def get_project_detail(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get project details."""
    project = get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )

    return project

@router.get("/slug/{slug}", response_model=Project)
def get_project_by_slug_endpoint(
    slug: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get project details by slug."""
    project = get_project_by_slug(db, slug)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )

    # Check if user is a member of the project
    if not is_project_member(db, project.id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )

    return project

@router.put("/{project_id}", response_model=Project)
def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a project."""
    # Check if user is a member of the project (both owners and members can edit)
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this project"
        )

    project = crud_update_project(db, project_id, project_update)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )

    return project

@router.delete("/{project_id}")
def delete_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Delete a project."""
    # Check if project exists first
    project = get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )

    # Check if user is the owner
    user_role = get_user_role_in_project(db, project_id, current_user.id)
    if user_role != MemberRole.OWNER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only project owners can delete projects"
        )

    success = crud_delete_project(db, project_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete project"
        )

    return {"message": "Project deleted successfully"}

# Project Variables endpoints
@router.get("/{project_id}/variables", response_model=List[ProjectVariable])
def get_project_variables(
    project_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all variables for a project."""
    # Check if user is a member of the project
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )

    return crud_get_project_variables(db, project_id, skip=skip, limit=limit)

@router.post("/{project_id}/variables", response_model=ProjectVariable)
def create_project_variable_endpoint(
    project_id: int,
    variable: ProjectVariableCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new project variable."""
    # Check if user has owner role
    user_role = get_user_role_in_project(db, project_id, current_user.id)
    if user_role not in [MemberRole.OWNER, MemberRole.MEMBER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create variables in this project"
        )

    # Check if variable with same key already exists
    existing_var = get_project_variable_by_key(db, project_id, variable.key)
    if existing_var:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Variable with this key already exists"
        )

    return create_project_variable(db, variable, project_id)

@router.put("/{project_id}/variables/{variable_id}", response_model=ProjectVariable)
def update_project_variable_endpoint(
    project_id: int,
    variable_id: int,
    variable_update: ProjectVariableUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a project variable."""
    # Check if user has owner role
    user_role = get_user_role_in_project(db, project_id, current_user.id)
    if user_role not in [MemberRole.OWNER, MemberRole.MEMBER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update variables in this project"
        )

    variable = update_project_variable(db, variable_id, variable_update)
    if not variable:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Variable not found"
        )

    return variable

@router.delete("/{project_id}/variables/{variable_id}")
def delete_project_variable_endpoint(
    project_id: int,
    variable_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Delete a project variable."""
    # Check if user has owner role
    user_role = get_user_role_in_project(db, project_id, current_user.id)
    if user_role not in [MemberRole.OWNER, MemberRole.MEMBER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete variables in this project"
        )

    success = delete_project_variable(db, variable_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Variable not found"
        )

    return {"message": "Variable deleted successfully"}

# Project Members endpoints
@router.get("/{project_id}/members", response_model=List[ProjectMember])
def get_project_members_endpoint(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all members of a project."""
    # Check if user is a member of the project
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )

    members = get_project_members(db, project_id)

    # Convert to response format with user details
    result = []
    for member in members:
        result.append(ProjectMember(
            id=member.id,
            user_id=member.user_id,
            user_email=member.user.email,
            user_name=member.user.full_name,
            role=member.role,
            project_id=member.project_id,
            joined_at=member.joined_at
        ))

    return result

@router.post("/{project_id}/members", response_model=ProjectMember)
def add_project_member_endpoint(
    project_id: int,
    member_data: ProjectMemberCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Add a new member to a project."""
    # Check if user is a member (all members can add other members)
    if not is_project_member(db, project_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to add members to this project"
        )

    try:
        member = add_project_member(db, project_id, member_data.user_id, member_data.role)

        # Return with user details
        return ProjectMember(
            id=member.id,
            user_id=member.user_id,
            user_email=member.user.email,
            user_name=member.user.full_name,
            role=member.role,
            project_id=member.project_id,
            joined_at=member.joined_at
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/{project_id}/members/{member_id}", response_model=ProjectMember)
def update_project_member_role_endpoint(
    project_id: int,
    member_id: int,
    role_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a project member's role."""
    # Check if user is the owner (only owners can change roles)
    user_role = get_user_role_in_project(db, project_id, current_user.id)
    if user_role != MemberRole.OWNER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only project owners can change member roles"
        )

    try:
        role = MemberRole(role_data.get("role"))
        member = update_project_member_role(db, project_id, member_id, role)
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Member not found"
            )

        # Return with user details
        return ProjectMember(
            id=member.id,
            user_id=member.user_id,
            user_email=member.user.email,
            user_name=member.user.full_name,
            role=member.role,
            project_id=member.project_id,
            joined_at=member.joined_at
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/{project_id}/members/{member_id}")
def remove_project_member_endpoint(
    project_id: int,
    member_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Remove a member from a project."""
    # Get the member being removed to check if it's the current user
    from app.crud.project import get_project_member
    member_to_remove = get_project_member(db, member_id)

    # Allow owners to remove anyone, or allow users to remove themselves
    user_role = get_user_role_in_project(db, project_id, current_user.id)
    is_removing_self = member_to_remove and member_to_remove.user_id == current_user.id

    if user_role != MemberRole.OWNER and not is_removing_self:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only project owners can remove members, or you can remove yourself"
        )

    # Additional check: If owner is removing themselves, ensure there are other members
    if is_removing_self and user_role == MemberRole.OWNER:
        total_members = len(get_project_members(db, project_id))
        if total_members <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot leave project as the only member. Add other members first or delete the project."
            )

    try:
        success = remove_project_member(db, project_id, member_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Member not found"
            )

        return {"message": "Member removed successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
