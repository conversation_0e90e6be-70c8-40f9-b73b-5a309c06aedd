from datetime import <PERSON><PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_active_user
from app.core.config import settings
from app.core.security import (
    create_access_token, create_password_reset_token, verify_password_reset_token,
    get_password_hash, create_tokens_for_user, refresh_access_token,
    revoke_refresh_token, revoke_all_user_tokens
)
from app.crud.user import create_user, authenticate_user, get_user_by_email, update_user
from app.schemas.auth import (
    Token, TokenPair, UserLogin, UserRegister, ForgotPasswordRequest,
    ChangePasswordRequest, ValidateResetTokenRequest, ResetPasswordRequest,
    RefreshTokenRequest, RefreshTokenResponse, RevokeTokenRequest
)
from app.schemas.user import User, UserCreate
from app.services.email_service import send_password_reset_email
from app.services.google_oauth import google_oauth_service

router = APIRouter()

@router.post("/register", response_model=TokenPair)
def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """Register a new user."""
    # Check if user already exists
    existing_user = get_user_by_email(db, email=user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new user
    user_create = UserCreate(
        email=user_data.email,
        password=user_data.password,
        full_name=user_data.full_name
    )
    user = create_user(db, user_create)

    # Create token pair
    access_token, refresh_token = create_tokens_for_user(user.id)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/login", response_model=TokenPair)
def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """Login user and return token pair."""
    user = authenticate_user(db, user_credentials.email, user_credentials.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create token pair
    access_token, refresh_token = create_tokens_for_user(user.id)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.get("/me", response_model=User)
def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """Get current user information."""
    return current_user



@router.post("/check-user-auth-provider")
def check_user_auth_provider(request: ForgotPasswordRequest, db: Session = Depends(get_db)):
    """Check user's authentication provider."""
    user = get_user_by_email(db, email=request.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return {
        "email": user.email,
        "auth_provider": user.auth_provider
    }

@router.post("/forgot-password")
def forgot_password(request: ForgotPasswordRequest, db: Session = Depends(get_db)):
    """Send password reset email."""
    # Check if user exists
    user = get_user_by_email(db, email=request.email)
    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If the email exists, a password reset link has been sent."}

    # Don't send reset email for Google users
    if user.auth_provider == "google":
        return {"message": "If the email exists, a password reset link has been sent."}

    # Generate password reset token
    reset_token = create_password_reset_token(user.email)

    # Send password reset email
    try:
        send_password_reset_email(user.email, user.full_name or "User", reset_token)
        return {"message": "If the email exists, a password reset link has been sent."}
    except Exception as e:
        # Log the error but don't expose it to the user
        print(f"Failed to send password reset email: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send password reset email. Please try again later."
        )

@router.post("/change-password")
def change_password(
    request: ChangePasswordRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Change user password."""
    # Verify current password
    if not authenticate_user(db, current_user.email, request.current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Update password
    from app.schemas.user import UserUpdate
    user_update = UserUpdate(password=request.new_password)
    updated_user = update_user(db, current_user.id, user_update)

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )

    return {"message": "Password changed successfully"}

@router.post("/validate-reset-token")
def validate_reset_token(request: ValidateResetTokenRequest):
    """Validate password reset token."""
    email = verify_password_reset_token(request.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    return {"message": "Token is valid", "email": email}

@router.post("/reset-password")
def reset_password(request: ResetPasswordRequest, db: Session = Depends(get_db)):
    """Reset password using reset token."""
    # Verify the reset token
    email = verify_password_reset_token(request.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )

    # Get user by email
    user = get_user_by_email(db, email=email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update user password
    from app.schemas.user import UserUpdate
    user_update = UserUpdate(password=request.new_password)
    updated_user = update_user(db, user.id, user_update)

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )

    return {"message": "Password reset successfully"}

@router.get("/google")
def google_login():
    """Initiate Google OAuth login."""
    authorization_url = google_oauth_service.get_authorization_url()
    return {"authorization_url": authorization_url}

@router.get("/google/callback")
async def google_callback(
    code: str = Query(...),
    state: str = Query(None),
    db: Session = Depends(get_db)
):
    """Handle Google OAuth callback."""
    # Exchange code for token
    token_data = await google_oauth_service.exchange_code_for_token(code)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to exchange code for token"
        )

    # Get user info
    user_info = await google_oauth_service.get_user_info(token_data["access_token"])
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to get user information"
        )

    # Check if user exists
    user = get_user_by_email(db, user_info["email"])

    if not user:
        # Create new user
        user_create = UserCreate(
            email=user_info["email"],
            full_name=user_info.get("name"),
            password="",  # No password for OAuth users
            is_active=True,
            auth_provider="google",
            profile_image_url=user_info.get("picture")  # Google profile image
        )
        user = create_user(db, user_create)
    else:
        # Update existing user's profile image if it's a Google user
        if user.auth_provider == "google" and user_info.get("picture"):
            from app.schemas.user import UserUpdate
            user_update = UserUpdate(profile_image_url=user_info.get("picture"))
            user = update_user(db, user.id, user_update)

    # Create token pair (same as login/register)
    access_token, refresh_token = create_tokens_for_user(user.id)

    # Redirect to frontend with both tokens
    frontend_url = f"{settings.FRONTEND_URL}?token={access_token}&refresh_token={refresh_token}"
    return RedirectResponse(url=frontend_url)


@router.post("/refresh", response_model=RefreshTokenResponse)
def refresh_token(request: RefreshTokenRequest):
    """Refresh access token using refresh token."""
    result = refresh_access_token(request.refresh_token)

    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token, refresh_token = result

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }


@router.post("/revoke")
def revoke_token(
    request: RevokeTokenRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Revoke refresh token(s)."""
    if request.revoke_all:
        # Revoke all refresh tokens for the user
        count = revoke_all_user_tokens(current_user.id, "logout_all")
        return {"message": f"Revoked {count} refresh tokens"}
    else:
        # Revoke specific refresh token
        success = revoke_refresh_token(request.refresh_token, "manual")
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Refresh token not found"
            )
        return {"message": "Refresh token revoked successfully"}


@router.post("/logout")
def logout(current_user: User = Depends(get_current_active_user)):
    """Logout user by revoking all refresh tokens."""
    count = revoke_all_user_tokens(current_user.id, "logout")
    return {"message": f"Logged out successfully. Revoked {count} refresh tokens."}
