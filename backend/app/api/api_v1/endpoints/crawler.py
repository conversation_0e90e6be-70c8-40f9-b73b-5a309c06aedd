from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, HttpUrl, Field
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_active_user
from app.models.user import User
from app.services.web_crawler_service import (
    WebCrawlerService, 
    AuthCredentials, 
    CrawlOptions, 
    crawl_website,
    AuthenticationError,
    CrawlerError
)
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


class CrawlRequest(BaseModel):
    """Request model for crawling a website"""
    url: HttpUrl = Field(..., description="URL to crawl")
    is_login_needed: bool = Field(False, description="Whether login is required")
    username: Optional[str] = Field(None, description="Username for authentication")
    password: Optional[str] = Field(None, description="Password for authentication")
    username_selector: Optional[str] = Field(None, description="CSS selector for username field")
    password_selector: Optional[str] = Field(None, description="CSS selector for password field")
    submit_selector: Optional[str] = Field(None, description="CSS selector for submit button")
    skip_hidden: bool = Field(True, description="Skip hidden elements")
    skip_tags: Optional[List[str]] = Field(None, description="Tags to skip")
    only_tags: Optional[List[str]] = Field(None, description="Only include these tags")
    timeout: int = Field(30000, description="Timeout in milliseconds", ge=5000, le=120000)
    max_elements: int = Field(1000, description="Maximum number of elements to extract", ge=1, le=5000)


class ElementResponse(BaseModel):
    """Response model for extracted element"""
    tag: str = Field(..., description="HTML tag name")
    selector: str = Field(..., description="CSS selector")
    xpath: str = Field(..., description="XPath selector")
    text: str = Field(..., description="Text content")
    attributes: Dict[str, str] = Field(..., description="Element attributes")
    is_interactive: bool = Field(..., description="Whether element is interactive")


class CrawlResponse(BaseModel):
    """Response model for crawl results"""
    url: str = Field(..., description="Crawled URL")
    elements: List[ElementResponse] = Field(..., description="Extracted elements")
    total_elements: int = Field(..., description="Total number of elements extracted")
    timestamp: float = Field(..., description="Crawl timestamp")


@router.post("/crawl", response_model=CrawlResponse)
async def crawl_website_endpoint(
    request: CrawlRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Crawl a website and extract DOM elements with their selectors and text content.
    
    This endpoint provides a comprehensive web crawling service that can:
    - Extract all DOM elements with their tag names, selectors, and text content
    - Handle authentication if login is required
    - Filter elements based on various criteria
    - Generate both CSS selectors and XPath expressions
    - Identify interactive elements
    
    **Authentication Support:**
    - Set `is_login_needed` to `true` if the website requires login
    - Provide `username` and `password` for authentication
    - Optionally customize selectors for login form elements
    
    **Filtering Options:**
    - `skip_hidden`: Skip elements that are not visible (default: true)
    - `skip_tags`: List of HTML tags to skip (e.g., ["script", "style"])
    - `only_tags`: Only extract elements with these tags (e.g., ["input", "button"])
    - `max_elements`: Maximum number of elements to extract (default: 1000)
    
    **Response Format:**
    Each extracted element includes:
    - `tag`: HTML tag name (e.g., "input", "div", "button")
    - `selector`: CSS selector for the element
    - `xpath`: XPath expression for the element
    - `text`: Text content of the element
    - `attributes`: All HTML attributes as key-value pairs
    - `is_interactive`: Boolean indicating if element is interactive
    """
    try:
        logger.info(f"User {current_user.id} initiated crawl for URL: {request.url}")
        
        # Prepare crawl options
        options = CrawlOptions(
            skip_hidden=request.skip_hidden,
            skip_tags=request.skip_tags,
            only_tags=request.only_tags,
            timeout=request.timeout,
            max_elements=request.max_elements
        )
        
        # Prepare authentication credentials if needed
        auth_credentials = None
        if request.is_login_needed:
            if not request.username or not request.password:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username and password are required when is_login_needed is true"
                )
            
            auth_credentials = AuthCredentials(
                username=request.username,
                password=request.password,
                username_selector=request.username_selector or AuthCredentials.username_selector,
                password_selector=request.password_selector or AuthCredentials.password_selector,
                submit_selector=request.submit_selector or AuthCredentials.submit_selector
            )
        
        # Perform crawling
        async with WebCrawlerService() as crawler:
            result = await crawler.crawl(
                url=str(request.url),
                is_login_needed=request.is_login_needed,
                auth_credentials=auth_credentials,
                options=options
            )
        
        logger.debug(f"Crawl completed successfully. Extracted {result['total_elements']} elements")

        return CrawlResponse(**result)
        
    except AuthenticationError as e:
        logger.error(f"Authentication failed for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}"
        )
    except CrawlerError as e:
        logger.error(f"Crawler error for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Crawling failed: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during crawl for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during crawling"
        )


@router.get("/health")
async def crawler_health_check():
    """Health check endpoint for the crawler service"""
    try:
        # Test browser initialization
        async with WebCrawlerService() as crawler:
            pass
        return {"status": "healthy", "service": "web_crawler"}
    except Exception as e:
        logger.error(f"Crawler health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Crawler service is not available"
        )
