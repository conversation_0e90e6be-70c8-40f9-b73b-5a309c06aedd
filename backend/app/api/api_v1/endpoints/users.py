from typing import List
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_active_user
from app.crud.user import search_users, get_users
from app.models.user import User
from app.schemas.user import User as UserSchema

router = APIRouter()

@router.get("/search", response_model=List[UserSchema])
def search_users_endpoint(
    q: str = Query(..., description="Search query for email or full name"),
    limit: int = Query(10, description="Maximum number of results"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Search users by email or full name."""
    return search_users(db, q, limit)

@router.get("/", response_model=List[UserSchema])
def get_users_endpoint(
    skip: int = Query(0, description="Number of users to skip"),
    limit: int = Query(100, description="Maximum number of users to return"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all users."""
    return get_users(db, skip, limit)
