import logging
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.core.logging_config import setup_logging, get_api_logger
from app.api.api_v1.api import api_router

# Setup logging
setup_logging(log_level=settings.LOG_LEVEL, log_dir=settings.LOG_DIR)
logger = get_api_logger()

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="IntelliTest Backend"
)

logger.debug(f"Starting {settings.PROJECT_NAME} v{settings.VERSION}")

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "Cache-Control",
        "Pragma",
    ],
    expose_headers=["*"],
)

app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    logger.debug("Root endpoint accessed")
    return {"message": "IntelliTest API"}

@app.get("/health")
async def health_check():
    logger.debug("Health check endpoint accessed")
    return {"status": "healthy"}

@app.on_event("startup")
async def startup_event():
    logger.debug("Application startup complete")

    # Start background task for token cleanup
    from app.core.background_tasks import start_token_cleanup_task
    cleanup_task = await start_token_cleanup_task()
    logger.debug("Token cleanup background task started")

@app.on_event("shutdown")
async def shutdown_event():
    logger.debug("Application shutdown initiated")

    # Stop background tasks
    try:
        from app.core.background_tasks import stop_token_cleanup_task
        stop_token_cleanup_task()
        logger.debug("Background tasks stopped successfully")
    except Exception as e:
        logger.error(f"Error stopping background tasks: {e}")

    # Clean up streaming resources
    try:
        from app.services.streaming_service import streaming_store
        streaming_store.cleanup_all_streams()
        logger.debug("Streaming resources cleaned up successfully")
    except Exception as e:
        logger.error(f"Error cleaning up streaming resources: {e}")

    # Close database connections
    try:
        from app.db.base import engine
        engine.dispose()
        logger.debug("Database connections closed successfully")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")

    logger.debug("Application shutdown complete")
