from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime, timezone, timedelta
from app.models.refresh_token import RefreshToken
import logging

logger = logging.getLogger(__name__)


def create_refresh_token(
    db: Session,
    user_id: int,
    expires_in_days: int = 30,
    device_info: str = None,
    ip_address: str = None,
    user_agent: str = None
) -> RefreshToken:
    """Create a new refresh token for a user."""
    refresh_token = RefreshToken.create_for_user(
        user_id=user_id,
        expires_in_days=expires_in_days,
        device_info=device_info,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    db.add(refresh_token)
    db.commit()
    db.refresh(refresh_token)
    
    logger.info(f"Created refresh token for user {user_id}, expires at {refresh_token.expires_at}")
    return refresh_token


def get_refresh_token(db: Session, token: str) -> Optional[RefreshToken]:
    """Get a refresh token by token string."""
    return db.query(RefreshToken).filter(
        and_(
            RefreshToken.token == token,
            RefreshToken.is_active == True
        )
    ).first()


def get_user_refresh_tokens(db: Session, user_id: int, active_only: bool = True) -> List[RefreshToken]:
    """Get all refresh tokens for a user."""
    query = db.query(RefreshToken).filter(RefreshToken.user_id == user_id)
    
    if active_only:
        query = query.filter(RefreshToken.is_active == True)
    
    return query.order_by(RefreshToken.created_at.desc()).all()


def revoke_refresh_token(db: Session, token: str, reason: str = "manual") -> bool:
    """Delete a specific refresh token."""
    refresh_token = get_refresh_token(db, token)
    if not refresh_token:
        return False

    user_id = refresh_token.user_id
    db.delete(refresh_token)
    db.commit()

    logger.info(f"Deleted refresh token for user {user_id}, reason: {reason}")
    return True


def revoke_user_refresh_tokens(
    db: Session,
    user_id: int,
    reason: str = "logout_all",
    exclude_token: str = None
) -> int:
    """Delete all refresh tokens for a user, optionally excluding one token."""
    query = db.query(RefreshToken).filter(
        and_(
            RefreshToken.user_id == user_id,
            RefreshToken.is_active == True
        )
    )

    if exclude_token:
        query = query.filter(RefreshToken.token != exclude_token)

    tokens = query.all()
    count = len(tokens)

    for token in tokens:
        db.delete(token)

    if count > 0:
        db.commit()
        logger.info(f"Deleted {count} refresh tokens for user {user_id}, reason: {reason}")

    return count


def update_token_last_used(db: Session, token: str) -> bool:
    """Update the last used timestamp for a refresh token."""
    refresh_token = get_refresh_token(db, token)
    if not refresh_token:
        return False
    
    refresh_token.update_last_used()
    db.commit()
    return True


def extend_token_expiration(db: Session, token: str, days: int = 30) -> bool:
    """Extend the expiration of a refresh token (for sliding sessions)."""
    refresh_token = get_refresh_token(db, token)
    if not refresh_token or not refresh_token.is_valid():
        return False
    
    refresh_token.extend_expiration(days)
    db.commit()
    
    logger.info(f"Extended refresh token expiration for user {refresh_token.user_id} to {refresh_token.expires_at}")
    return True


def cleanup_expired_tokens(db: Session) -> int:
    """Clean up expired and inactive refresh tokens."""
    current_time = datetime.now(timezone.utc)

    # Delete all expired tokens and inactive tokens
    expired_tokens = db.query(RefreshToken).filter(
        or_(
            RefreshToken.expires_at < current_time,
            RefreshToken.is_active == False  # Delete all inactive tokens immediately
        )
    ).all()

    count = len(expired_tokens)

    for token in expired_tokens:
        db.delete(token)

    if count > 0:
        db.commit()
        logger.info(f"Cleaned up {count} expired/inactive refresh tokens")

    return count


def validate_and_refresh_token(db: Session, token: str) -> Optional[RefreshToken]:
    """Validate a refresh token and update its last used timestamp."""
    refresh_token = get_refresh_token(db, token)
    
    if not refresh_token:
        logger.warning(f"Refresh token not found: {token[:10]}...")
        return None
    
    if not refresh_token.is_valid():
        logger.warning(f"Invalid refresh token for user {refresh_token.user_id}: expired or inactive")
        return None
    
    # Update last used timestamp
    refresh_token.update_last_used()
    db.commit()
    
    return refresh_token


def get_token_stats(db: Session, user_id: int) -> dict:
    """Get statistics about user's refresh tokens."""
    total_tokens = db.query(RefreshToken).filter(RefreshToken.user_id == user_id).count()
    active_tokens = db.query(RefreshToken).filter(
        and_(
            RefreshToken.user_id == user_id,
            RefreshToken.is_active == True
        )
    ).count()
    
    return {
        "total_tokens": total_tokens,
        "active_tokens": active_tokens,
        "revoked_tokens": total_tokens - active_tokens
    }
