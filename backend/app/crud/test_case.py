from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from app.models.test_case import TestCase
from app.schemas.requirement import TestCaseCreate
from app.services.rag_service import rag_service
import re

def generate_next_custom_id(db: Session, requirement_id: int) -> str:
    """Generate the next custom test case ID for a requirement (TC-001, TC-002, etc.)."""
    # Get all test cases for this requirement with TC- format
    test_cases = (
        db.query(TestCase)
        .filter(TestCase.requirement_id == requirement_id)
        .filter(TestCase.custom_id.like('TC-%'))
        .all()
    )

    if not test_cases:
        return "TC-001"

    # Extract numbers from all custom_ids and find the maximum
    max_num = 0
    for test_case in test_cases:
        match = re.search(r'TC-(\d+)', test_case.custom_id)
        if match:
            num = int(match.group(1))
            max_num = max(max_num, num)

    # Return the next number
    next_num = max_num + 1
    return f"TC-{next_num:03d}"

def get_test_case(db: Session, test_case_id: int) -> Optional[TestCase]:
    return db.query(TestCase).filter(TestCase.id == test_case_id).first()

def get_test_cases_for_requirement(db: Session, requirement_id: int, skip: int = 0, limit: int = 100) -> List[TestCase]:
    return (
        db.query(TestCase)
        .filter(TestCase.requirement_id == requirement_id)
        .order_by(TestCase.custom_id)  # Order by custom_id to get TC-001, TC-002, etc. in order
        .offset(skip)
        .limit(limit)
        .all()
    )

def create_test_case(db: Session, test_case: TestCaseCreate, requirement_id: int) -> TestCase:
    from app.models.requirement import Requirement

    # Generate custom ID if not provided
    custom_id = test_case.custom_id or generate_next_custom_id(db, requirement_id)

    db_test_case = TestCase(
        custom_id=custom_id,
        title=test_case.title,
        steps=test_case.steps,
        expected_result=test_case.expected_result,
        notes=test_case.notes,
        requirement_id=requirement_id
    )
    db.add(db_test_case)
    db.commit()
    db.refresh(db_test_case)

    # Get the requirement to access project_id
    requirement = db.query(Requirement).filter(Requirement.id == requirement_id).first()
    if requirement:
        # Create text for embedding (combine title, steps, expected result, and notes)
        text_parts = [test_case.title]
        if test_case.steps:
            text_parts.append(f"Steps: {test_case.steps}")
        if test_case.expected_result:
            text_parts.append(f"Expected: {test_case.expected_result}")
        if test_case.notes:
            text_parts.append(f"Notes: {test_case.notes}")

        text = " | ".join(text_parts)

        # Add to vector database
        rag_service.add_test_case(
            test_case_id=db_test_case.id,
            requirement_id=requirement_id,
            project_id=requirement.project_id,
            text=text,
            metadata={
                "title": test_case.title,
                "requirement_id": requirement_id,
                "project_id": requirement.project_id
            }
        )

    return db_test_case

def create_multiple_test_cases(db: Session, test_cases: List[TestCaseCreate], requirement_id: int) -> List[TestCase]:
    """Create multiple test cases for a requirement."""
    from app.models.requirement import Requirement

    db_test_cases = []
    for i, test_case in enumerate(test_cases):
        # Always generate a new custom ID, ignoring any provided by LLM
        custom_id = generate_next_custom_id(db, requirement_id)

        db_test_case = TestCase(
            custom_id=custom_id,
            title=test_case.title,
            steps=test_case.steps,
            expected_result=test_case.expected_result,
            notes=test_case.notes,
            requirement_id=requirement_id
        )
        db.add(db_test_case)
        db_test_cases.append(db_test_case)

        # Commit after each test case to ensure custom_id generation works correctly for the next one
        db.commit()
        db.refresh(db_test_case)

        # Add to vector database
        requirement = db.query(Requirement).filter(Requirement.id == requirement_id).first()
        if requirement:
            text_parts = [test_case.title]
            if test_case.steps:
                text_parts.append(f"Steps: {test_case.steps}")
            if test_case.expected_result:
                text_parts.append(f"Expected: {test_case.expected_result}")
            if test_case.notes:
                text_parts.append(f"Notes: {test_case.notes}")

            text = " | ".join(text_parts)

            rag_service.add_test_case(
                test_case_id=db_test_case.id,
                requirement_id=requirement_id,
                project_id=requirement.project_id,
                text=text,
                metadata={
                    "title": test_case.title,
                    "requirement_id": requirement_id,
                    "project_id": requirement.project_id
                }
            )

    return db_test_cases

def update_test_case(db: Session, test_case_id: int, test_case_update: TestCaseCreate) -> Optional[TestCase]:
    test_case = get_test_case(db, test_case_id)
    if not test_case:
        return None
    
    update_data = test_case_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(test_case, field, value)
    
    db.commit()
    db.refresh(test_case)
    return test_case

def delete_test_case(db: Session, test_case_id: int) -> bool:
    test_case = get_test_case(db, test_case_id)
    if not test_case:
        return False

    try:
        db.delete(test_case)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error deleting test case {test_case_id}: {str(e)}")
        raise e
