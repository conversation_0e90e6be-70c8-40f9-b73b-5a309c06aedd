from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.project_variable import ProjectVariable
from app.schemas.project_variable import ProjectVariableCreate, ProjectVariableUpdate

def get_project_variable(db: Session, variable_id: int) -> Optional[ProjectVariable]:
    return db.query(ProjectVariable).filter(ProjectVariable.id == variable_id).first()

def get_project_variables(db: Session, project_id: int, skip: int = 0, limit: int = 100) -> List[ProjectVariable]:
    return (
        db.query(ProjectVariable)
        .filter(ProjectVariable.project_id == project_id)
        .offset(skip)
        .limit(limit)
        .all()
    )

def get_project_variable_by_key(db: Session, project_id: int, key: str) -> Optional[ProjectVariable]:
    return (
        db.query(ProjectVariable)
        .filter(ProjectVariable.project_id == project_id, ProjectVariable.key == key)
        .first()
    )

def create_project_variable(db: Session, variable: ProjectVariableCreate, project_id: int) -> ProjectVariable:
    db_variable = ProjectVariable(
        project_id=project_id,
        key=variable.key,
        value=variable.value,
        description=variable.description
    )
    db.add(db_variable)
    db.commit()
    db.refresh(db_variable)
    return db_variable

def update_project_variable(db: Session, variable_id: int, variable_update: ProjectVariableUpdate) -> Optional[ProjectVariable]:
    variable = get_project_variable(db, variable_id)
    if not variable:
        return None
    
    update_data = variable_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(variable, field, value)
    
    db.commit()
    db.refresh(variable)
    return variable

def delete_project_variable(db: Session, variable_id: int) -> bool:
    variable = get_project_variable(db, variable_id)
    if not variable:
        return False
    
    db.delete(variable)
    db.commit()
    return True

def replace_variable_references(text: str, variables: List[ProjectVariable]) -> str:
    """Replace @key references with variable values."""
    result = text
    for variable in variables:
        result = result.replace(f"@{variable.key}", variable.value)
    return result
