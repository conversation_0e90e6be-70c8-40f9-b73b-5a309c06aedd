from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from datetime import datetime
from app.models.code_generation import (
    CodeGenerationSession, GeneratedFile, CodeGenerationLog, 
    ProjectCodeMetadata, CodeGenerationStatus
)
from app.schemas.code_generation import (
    CodeGenerationSessionCreate, CodeGenerationSessionUpdate,
    GeneratedFileCreate, CodeGenerationLogCreate,
    ProjectCodeMetadataCreate, ProjectCodeMetadataUpdate
)

def create_code_generation_session(
    db: Session,
    session_data: Dict[str, Any]
) -> CodeGenerationSession:
    """Create a new code generation session."""
    # Remove timestamp fields that should use server defaults
    # to avoid overriding the server_default with None values
    filtered_data = {k: v for k, v in session_data.items()
                    if k not in ['created_at', 'updated_at', 'started_at', 'completed_at']
                    or v is not None}

    db_session = CodeGenerationSession(**filtered_data)
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    return db_session

def get_code_generation_session(
    db: Session, 
    session_id: str
) -> Optional[CodeGenerationSession]:
    """Get a code generation session by session ID."""
    return db.query(CodeGenerationSession).filter(
        CodeGenerationSession.session_id == session_id
    ).first()

def get_code_generation_session_by_id(
    db: Session, 
    id: int
) -> Optional[CodeGenerationSession]:
    """Get a code generation session by database ID."""
    return db.query(CodeGenerationSession).filter(
        CodeGenerationSession.id == id
    ).first()

def update_code_generation_session(
    db: Session, 
    session_id: str, 
    update_data: Dict[str, Any]
) -> Optional[CodeGenerationSession]:
    """Update a code generation session."""
    session = get_code_generation_session(db, session_id)
    if not session:
        return None
    
    for field, value in update_data.items():
        if field in ["started_at", "completed_at"] and value == "now":
            value = datetime.utcnow()
        setattr(session, field, value)
    
    db.commit()
    db.refresh(session)
    return session

def get_sessions_for_project(
    db: Session, 
    project_id: int, 
    skip: int = 0, 
    limit: int = 100
) -> List[CodeGenerationSession]:
    """Get code generation sessions for a project."""
    return (
        db.query(CodeGenerationSession)
        .filter(CodeGenerationSession.project_id == project_id)
        .order_by(desc(CodeGenerationSession.created_at))
        .offset(skip)
        .limit(limit)
        .all()
    )

def get_sessions_for_user(
    db: Session, 
    user_id: int, 
    skip: int = 0, 
    limit: int = 100
) -> List[CodeGenerationSession]:
    """Get code generation sessions for a user."""
    return (
        db.query(CodeGenerationSession)
        .filter(CodeGenerationSession.user_id == user_id)
        .order_by(desc(CodeGenerationSession.created_at))
        .offset(skip)
        .limit(limit)
        .all()
    )

def get_sessions_for_requirement(
    db: Session, 
    requirement_id: int
) -> List[CodeGenerationSession]:
    """Get code generation sessions for a requirement."""
    return (
        db.query(CodeGenerationSession)
        .filter(CodeGenerationSession.requirement_id == requirement_id)
        .order_by(desc(CodeGenerationSession.created_at))
        .all()
    )

def delete_code_generation_session(
    db: Session, 
    session_id: str
) -> bool:
    """Delete a code generation session and related data."""
    session = get_code_generation_session(db, session_id)
    if not session:
        return False
    
    # Delete related generated files and logs (cascade should handle this)
    db.delete(session)
    db.commit()
    return True

def create_generated_file(
    db: Session, 
    session_id: int, 
    file_data: GeneratedFileCreate
) -> GeneratedFile:
    """Create a generated file record."""
    db_file = GeneratedFile(
        session_id=session_id,
        **file_data.dict()
    )
    db.add(db_file)
    db.commit()
    db.refresh(db_file)
    return db_file

def get_generated_files_for_session(
    db: Session, 
    session_id: int
) -> List[GeneratedFile]:
    """Get all generated files for a session."""
    return (
        db.query(GeneratedFile)
        .filter(GeneratedFile.session_id == session_id)
        .order_by(GeneratedFile.created_at)
        .all()
    )

def update_generated_file(
    db: Session, 
    file_id: int, 
    update_data: Dict[str, Any]
) -> Optional[GeneratedFile]:
    """Update a generated file record."""
    file_record = db.query(GeneratedFile).filter(GeneratedFile.id == file_id).first()
    if not file_record:
        return None
    
    for field, value in update_data.items():
        if field == "saved_at" and value == "now":
            value = datetime.utcnow()
        setattr(file_record, field, value)
    
    db.commit()
    db.refresh(file_record)
    return file_record

def create_code_generation_log(
    db: Session, 
    session_id: int, 
    log_data: CodeGenerationLogCreate
) -> CodeGenerationLog:
    """Create a code generation log entry."""
    db_log = CodeGenerationLog(
        session_id=session_id,
        **log_data.dict()
    )
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log

def get_logs_for_session(
    db: Session, 
    session_id: int, 
    log_level: Optional[str] = None
) -> List[CodeGenerationLog]:
    """Get logs for a code generation session."""
    query = db.query(CodeGenerationLog).filter(CodeGenerationLog.session_id == session_id)
    
    if log_level:
        query = query.filter(CodeGenerationLog.log_level == log_level)
    
    return query.order_by(CodeGenerationLog.created_at).all()

def create_project_code_metadata(
    db: Session, 
    metadata_data: ProjectCodeMetadataCreate
) -> ProjectCodeMetadata:
    """Create project code metadata."""
    db_metadata = ProjectCodeMetadata(**metadata_data.dict())
    db.add(db_metadata)
    db.commit()
    db.refresh(db_metadata)
    return db_metadata

def get_project_code_metadata(
    db: Session, 
    project_id: int
) -> Optional[ProjectCodeMetadata]:
    """Get project code metadata."""
    return db.query(ProjectCodeMetadata).filter(
        ProjectCodeMetadata.project_id == project_id
    ).first()

def update_project_code_metadata(
    db: Session, 
    project_id: int, 
    update_data: ProjectCodeMetadataUpdate
) -> Optional[ProjectCodeMetadata]:
    """Update project code metadata."""
    metadata = get_project_code_metadata(db, project_id)
    if not metadata:
        # Create if doesn't exist
        create_data = ProjectCodeMetadataCreate(
            project_id=project_id,
            **update_data.dict(exclude_unset=True)
        )
        return create_project_code_metadata(db, create_data)
    
    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        if field.endswith("_at") and value == "now":
            value = datetime.utcnow()
        setattr(metadata, field, value)
    
    db.commit()
    db.refresh(metadata)
    return metadata

def get_code_generation_stats(db: Session, project_id: Optional[int] = None) -> Dict[str, Any]:
    """Get code generation statistics."""
    query = db.query(CodeGenerationSession)
    
    if project_id:
        query = query.filter(CodeGenerationSession.project_id == project_id)
    
    total_sessions = query.count()
    successful_sessions = query.filter(
        CodeGenerationSession.status == CodeGenerationStatus.COMPLETED
    ).count()
    failed_sessions = query.filter(
        CodeGenerationSession.status == CodeGenerationStatus.FAILED
    ).count()
    in_progress_sessions = query.filter(
        CodeGenerationSession.status == CodeGenerationStatus.IN_PROGRESS
    ).count()
    
    # Get total files generated
    total_files = db.query(func.sum(CodeGenerationSession.generated_files_count)).scalar() or 0
    
    # Get average generation time for completed sessions
    completed_sessions = query.filter(
        CodeGenerationSession.status == CodeGenerationStatus.COMPLETED,
        CodeGenerationSession.started_at.isnot(None),
        CodeGenerationSession.completed_at.isnot(None)
    ).all()
    
    generation_times = []
    for session in completed_sessions:
        if session.started_at and session.completed_at:
            duration = (session.completed_at - session.started_at).total_seconds()
            generation_times.append(duration)
    
    avg_generation_time = sum(generation_times) / len(generation_times) if generation_times else None
    
    # Get most common frameworks and languages
    framework_stats = (
        db.query(
            CodeGenerationSession.automation_framework,
            func.count(CodeGenerationSession.automation_framework).label('count')
        )
        .group_by(CodeGenerationSession.automation_framework)
        .order_by(desc('count'))
        .limit(5)
        .all()
    )
    
    language_stats = (
        db.query(
            CodeGenerationSession.programming_language,
            func.count(CodeGenerationSession.programming_language).label('count')
        )
        .group_by(CodeGenerationSession.programming_language)
        .order_by(desc('count'))
        .limit(5)
        .all()
    )
    
    return {
        "total_sessions": total_sessions,
        "successful_sessions": successful_sessions,
        "failed_sessions": failed_sessions,
        "in_progress_sessions": in_progress_sessions,
        "total_files_generated": total_files,
        "average_generation_time": avg_generation_time,
        "most_common_frameworks": [
            {"framework": fw, "count": count} for fw, count in framework_stats
        ],
        "most_common_languages": [
            {"language": lang, "count": count} for lang, count in language_stats
        ]
    }

def cleanup_old_sessions(
    db: Session, 
    days_old: int = 30, 
    keep_successful: bool = True
) -> Dict[str, int]:
    """Clean up old code generation sessions."""
    from datetime import timedelta
    
    cutoff_date = datetime.utcnow() - timedelta(days=days_old)
    
    query = db.query(CodeGenerationSession).filter(
        CodeGenerationSession.created_at < cutoff_date
    )
    
    if keep_successful:
        query = query.filter(
            CodeGenerationSession.status != CodeGenerationStatus.COMPLETED
        )
    
    sessions_to_delete = query.all()
    deleted_count = len(sessions_to_delete)
    
    for session in sessions_to_delete:
        db.delete(session)
    
    db.commit()
    
    return {
        "deleted_sessions": deleted_count,
        "cutoff_date": cutoff_date.isoformat()
    }
