import os
import shutil
import tempfile
import stat
import subprocess
from typing import Optional, List, Dict, Any
from pathlib import Path
import git
from git import Repo, GitCommandError
import logging

logger = logging.getLogger(__name__)

class GitRepositoryService:
    """Service for managing Git repository operations for code generation."""
    
    def __init__(self, base_path: str = "ProjectCode"):
        """
        Initialize the Git Repository Service.
        
        Args:
            base_path: Base directory for storing project repositories
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
        
        # Files that indicate an empty repository
        self.non_code_files = {
            'README.md', 'readme.md', 'README.txt', 'readme.txt',
            '.gitignore', '.gitattributes',
            'LICENSE', 'LICENSE.txt', 'LICENSE.md', 'license', 'license.txt',
            'CHANGELOG.md', 'CHANGELOG.txt', 'changelog.md',
            'CONTRIBUTING.md', 'contributing.md',
            'CODE_OF_CONDUCT.md', 'code_of_conduct.md'
        }
    
    def get_project_path(self, project_name: str) -> Path:
        """Get the path for a specific project."""
        return self.base_path / project_name
    
    def get_repo_path(self, project_name: str) -> Path:
        """Get the path for the cloned repository within a project."""
        return self.get_project_path(project_name) / "repo"
    
    def get_generated_path(self, project_name: str, requirement_name: str) -> Path:
        """Get the path for generated code for a specific requirement."""
        return self.get_repo_path(project_name) / "generated" / requirement_name

    def _is_ssh_url(self, git_url: str) -> bool:
        """Check if the git URL is an SSH URL."""
        return git_url.startswith('git@') or git_url.startswith('ssh://')

    def _validate_ssh_key_format(self, ssh_private_key: str) -> tuple[bool, str]:
        """
        Validate SSH private key format.

        Args:
            ssh_private_key: The private SSH key content

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not ssh_private_key or not ssh_private_key.strip():
            return False, "SSH key is empty"

        key_content = ssh_private_key.strip()

        # Check for common SSH key formats
        valid_headers = [
            "-----BEGIN RSA PRIVATE KEY-----",
            "-----BEGIN DSA PRIVATE KEY-----",
            "-----BEGIN EC PRIVATE KEY-----",
            "-----BEGIN OPENSSH PRIVATE KEY-----",
            "-----BEGIN PRIVATE KEY-----"
        ]

        valid_footers = [
            "-----END RSA PRIVATE KEY-----",
            "-----END DSA PRIVATE KEY-----",
            "-----END EC PRIVATE KEY-----",
            "-----END OPENSSH PRIVATE KEY-----",
            "-----END PRIVATE KEY-----"
        ]

        # Check if key has proper header
        has_valid_header = any(key_content.startswith(header) for header in valid_headers)
        if not has_valid_header:
            return False, f"SSH key must start with a valid header like {valid_headers[0]}"

        # Check if key has proper footer
        has_valid_footer = any(key_content.endswith(footer) for footer in valid_footers)
        if not has_valid_footer:
            return False, f"SSH key must end with a valid footer like {valid_footers[0]}"

        # Check for line breaks in key content
        lines = key_content.split('\n')
        if len(lines) < 3:  # At minimum: header, content, footer
            return False, "SSH key appears to be malformed (too few lines)"

        return True, ""

    def _setup_ssh_key(self, ssh_private_key: str, ssh_passphrase: Optional[str] = None) -> Optional[str]:
        """
        Setup SSH key for git operations.

        Args:
            ssh_private_key: The private SSH key content
            ssh_passphrase: Optional passphrase for the SSH key (reserved for future use)

        Returns:
            Path to the temporary SSH key file, or None if setup failed
        """
        # Note: ssh_passphrase parameter is reserved for future encrypted key support
        try:
            # Validate SSH key format first
            is_valid, error_msg = self._validate_ssh_key_format(ssh_private_key)
            if not is_valid:
                logger.error(f"SSH key validation failed: {error_msg}")
                return None

            # Create a temporary file for the SSH key
            ssh_key_fd, ssh_key_path = tempfile.mkstemp(prefix='git_ssh_key_', suffix='')

            # Clean and normalize the SSH key content
            cleaned_key = ssh_private_key.strip()
            if not cleaned_key.endswith('\n'):
                cleaned_key += '\n'

            # Write the SSH key to the temporary file
            with os.fdopen(ssh_key_fd, 'w') as f:
                f.write(cleaned_key)

            # Set proper permissions for the SSH key file (600)
            os.chmod(ssh_key_path, stat.S_IRUSR | stat.S_IWUSR)

            # Log key info for debugging (without exposing the actual key)
            lines = cleaned_key.split('\n')
            logger.debug(f"SSH key setup completed: {ssh_key_path}")
            logger.debug(f"SSH key format: {lines[0] if lines else 'unknown'}")
            logger.debug(f"SSH key lines: {len([l for l in lines if l.strip()])}")

            return ssh_key_path

        except Exception as e:
            logger.error(f"Failed to setup SSH key: {e}")
            return None

    def _cleanup_ssh_key(self, ssh_key_path: str) -> None:
        """Clean up temporary SSH key file."""
        try:
            if os.path.exists(ssh_key_path):
                os.unlink(ssh_key_path)
                logger.debug(f"SSH key cleaned up: {ssh_key_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup SSH key {ssh_key_path}: {e}")

    def test_ssh_key_format(self, ssh_private_key: str) -> Dict[str, Any]:
        """
        Test SSH key format without performing actual git operations.

        Args:
            ssh_private_key: The private SSH key content to test

        Returns:
            Dictionary with validation results and suggestions
        """
        result = {
            "valid": False,
            "error": None,
            "suggestions": [],
            "key_info": {}
        }

        try:
            # Validate format
            is_valid, error_msg = self._validate_ssh_key_format(ssh_private_key)
            result["valid"] = is_valid
            result["error"] = error_msg

            if ssh_private_key:
                key_content = ssh_private_key.strip()
                lines = key_content.split('\n')

                result["key_info"] = {
                    "total_lines": len(lines),
                    "non_empty_lines": len([l for l in lines if l.strip()]),
                    "starts_with": lines[0][:50] + "..." if lines and len(lines[0]) > 50 else lines[0] if lines else "",
                    "ends_with": lines[-1][:50] + "..." if lines and len(lines[-1]) > 50 else lines[-1] if lines else "",
                    "estimated_length": len(key_content)
                }

                # Add suggestions based on common issues
                if not is_valid:
                    if not any(header in key_content for header in ["-----BEGIN", "-----END"]):
                        result["suggestions"].append("Key appears to be missing PEM headers/footers")
                    if len(lines) < 3:
                        result["suggestions"].append("Key appears to be too short or malformed")
                    if "\\n" in ssh_private_key:
                        result["suggestions"].append("Key may contain escaped newlines (\\n) instead of actual line breaks")

        except Exception as e:
            result["error"] = f"Error testing SSH key: {str(e)}"

        return result

    def _create_ssh_wrapper(self, ssh_key_path: str) -> Optional[str]:
        """
        Create a temporary SSH wrapper script for git operations.

        Args:
            ssh_key_path: Path to the SSH private key file

        Returns:
            Path to the SSH wrapper script, or None if creation failed
        """
        try:
            # Create a temporary script file
            script_fd, script_path = tempfile.mkstemp(prefix='git_ssh_wrapper_', suffix='.sh')

            # Write the SSH wrapper script
            ssh_wrapper_content = f'''#!/bin/bash
ssh -i "{ssh_key_path}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR "$@"
'''

            with os.fdopen(script_fd, 'w') as f:
                f.write(ssh_wrapper_content)

            # Make the script executable
            os.chmod(script_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IXUSR)

            logger.debug(f"SSH wrapper script created: {script_path}")
            return script_path

        except Exception as e:
            logger.error(f"Failed to create SSH wrapper script: {e}")
            return None
    
    def clone_repository(self, git_url: str, project_name: str, ssh_private_key: Optional[str] = None, ssh_passphrase: Optional[str] = None) -> Dict[str, Any]:
        """
        Clone a Git repository to the project directory.

        Args:
            git_url: URL of the Git repository to clone
            project_name: Name of the project (used for directory naming)
            ssh_private_key: Optional SSH private key for authentication
            ssh_passphrase: Optional passphrase for the SSH key

        Returns:
            Dictionary with clone result information
        """
        ssh_key_path = None
        ssh_wrapper_path = None
        original_git_ssh_command = None

        try:
            project_path = self.get_project_path(project_name)
            repo_path = self.get_repo_path(project_name)

            # Clean up existing directory if it exists
            if repo_path.exists():
                shutil.rmtree(repo_path)

            # Ensure project directory exists
            project_path.mkdir(exist_ok=True)

            logger.debug(f"Cloning repository {git_url} to {repo_path}")

            # Setup SSH authentication if needed
            if self._is_ssh_url(git_url) and ssh_private_key:
                logger.debug("Setting up SSH authentication for git clone")

                # Setup SSH key
                ssh_key_path = self._setup_ssh_key(ssh_private_key, ssh_passphrase)
                if not ssh_key_path:
                    raise RuntimeError("Failed to setup SSH key")

                # Create SSH wrapper script
                ssh_wrapper_path = self._create_ssh_wrapper(ssh_key_path)
                if not ssh_wrapper_path:
                    raise RuntimeError("Failed to create SSH wrapper script")

                # Set GIT_SSH_COMMAND environment variable
                original_git_ssh_command = os.environ.get('GIT_SSH_COMMAND')
                os.environ['GIT_SSH_COMMAND'] = ssh_wrapper_path

                logger.debug("SSH authentication configured for git operations")

            # Clone the repository
            repo = Repo.clone_from(git_url, repo_path)
            
            # Get repository information
            repo_info = self._get_repository_info(repo)
            
            # Check if repository is empty
            is_empty = self._is_empty_repository(repo_path)
            
            result = {
                "success": True,
                "project_path": str(project_path),
                "repo_path": str(repo_path),
                "is_empty": is_empty,
                "repo_info": repo_info,
                "message": f"Successfully cloned repository to {repo_path}"
            }
            
            logger.debug(f"Repository cloned successfully. Empty: {is_empty}")
            return result

        except GitCommandError as e:
            error_msg = f"Git command failed: {str(e)}"
            logger.error(error_msg)

            # Check for specific SSH-related errors
            error_str = str(e).lower()
            if "invalid format" in error_str:
                error_msg += "\n\nSSH Key Format Issue: The provided SSH private key appears to be in an invalid format. Please ensure:"
                error_msg += "\n- The key starts with '-----BEGIN [KEY_TYPE] PRIVATE KEY-----'"
                error_msg += "\n- The key ends with '-----END [KEY_TYPE] PRIVATE KEY-----'"
                error_msg += "\n- The key content is properly formatted with line breaks"
                error_msg += "\n- The key is not encrypted with a passphrase (not currently supported)"
            elif "permission denied" in error_str:
                error_msg += "\n\nSSH Authentication Issue: The SSH key was rejected by the server. Please verify:"
                error_msg += "\n- The SSH key is correctly associated with your GitHub/GitLab account"
                error_msg += "\n- The key has the necessary permissions for the repository"
                error_msg += "\n- The key format is correct (OpenSSH or PEM format)"

            return {
                "success": False,
                "error": error_msg,
                "error_type": "git_error"
            }
        except Exception as e:
            error_msg = f"Failed to clone repository: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_type": "general_error"
            }
        finally:
            # Cleanup SSH resources
            if ssh_key_path:
                self._cleanup_ssh_key(ssh_key_path)
            if ssh_wrapper_path:
                try:
                    os.unlink(ssh_wrapper_path)
                    logger.debug(f"SSH wrapper script cleaned up: {ssh_wrapper_path}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup SSH wrapper script: {e}")

            # Restore original GIT_SSH_COMMAND if it was modified
            if original_git_ssh_command is not None:
                os.environ['GIT_SSH_COMMAND'] = original_git_ssh_command
            elif 'GIT_SSH_COMMAND' in os.environ:
                del os.environ['GIT_SSH_COMMAND']
    
    def _get_repository_info(self, repo: Repo) -> Dict[str, Any]:
        """Extract information from the cloned repository."""
        try:
            return {
                "remote_url": repo.remotes.origin.url if repo.remotes else None,
                "branch": repo.active_branch.name if repo.active_branch else None,
                "commit_count": len(list(repo.iter_commits())),
                "last_commit": {
                    "hash": repo.head.commit.hexsha[:8],
                    "message": repo.head.commit.message.strip(),
                    "author": str(repo.head.commit.author),
                    "date": repo.head.commit.committed_datetime.isoformat()
                } if repo.head.commit else None
            }
        except Exception as e:
            logger.warning(f"Could not extract repository info: {e}")
            return {}
    
    def _is_empty_repository(self, repo_path: Path) -> bool:
        """
        Check if a repository is considered empty (only contains non-code files).
        
        Args:
            repo_path: Path to the repository
            
        Returns:
            True if repository is empty, False otherwise
        """
        try:
            # Get all files in the repository (excluding .git directory)
            all_files = []
            for root, dirs, files in os.walk(repo_path):
                # Skip .git directory
                if '.git' in dirs:
                    dirs.remove('.git')
                
                for file in files:
                    rel_path = os.path.relpath(os.path.join(root, file), repo_path)
                    all_files.append(rel_path)
            
            if not all_files:
                return True
            
            # Check if all files are non-code files
            for file_path in all_files:
                file_name = os.path.basename(file_path)
                if file_name not in self.non_code_files:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking if repository is empty: {e}")
            return False
    
    def get_repository_files(self, project_name: str, exclude_patterns: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get a list of all files in the repository with their metadata.
        
        Args:
            project_name: Name of the project
            exclude_patterns: List of patterns to exclude (e.g., ['*.pyc', '__pycache__'])
            
        Returns:
            List of file information dictionaries
        """
        repo_path = self.get_repo_path(project_name)
        
        if not repo_path.exists():
            return []
        
        exclude_patterns = exclude_patterns or [
            '*.pyc', '__pycache__', '.git', 'node_modules', '.env', '*.log'
        ]
        
        files = []
        try:
            for root, dirs, file_names in os.walk(repo_path):
                # Filter out excluded directories
                dirs[:] = [d for d in dirs if not any(
                    self._matches_pattern(d, pattern) for pattern in exclude_patterns
                )]
                
                for file_name in file_names:
                    if any(self._matches_pattern(file_name, pattern) for pattern in exclude_patterns):
                        continue
                    
                    file_path = Path(root) / file_name
                    rel_path = file_path.relative_to(repo_path)
                    
                    try:
                        stat = file_path.stat()
                        files.append({
                            "path": str(rel_path),
                            "full_path": str(file_path),
                            "size": stat.st_size,
                            "modified": stat.st_mtime,
                            "is_text": self._is_text_file(file_path)
                        })
                    except Exception as e:
                        logger.warning(f"Could not get stats for {file_path}: {e}")
                        
        except Exception as e:
            logger.error(f"Error listing repository files: {e}")
        
        return files
    
    def _matches_pattern(self, name: str, pattern: str) -> bool:
        """Check if a name matches a pattern (simple wildcard support)."""
        import fnmatch
        return fnmatch.fnmatch(name, pattern)
    
    def _is_text_file(self, file_path: Path) -> bool:
        """Check if a file is likely a text file."""
        text_extensions = {
            '.py', '.js', '.ts', '.java', '.cs', '.cpp', '.c', '.h',
            '.html', '.css', '.scss', '.less', '.xml', '.json', '.yaml', '.yml',
            '.md', '.txt', '.rst', '.ini', '.cfg', '.conf', '.properties',
            '.sql', '.sh', '.bat', '.ps1', '.dockerfile', '.gitignore'
        }
        
        return file_path.suffix.lower() in text_extensions
    
    def read_file_content(self, project_name: str, file_path: str) -> Optional[str]:
        """
        Read the content of a file in the repository.
        
        Args:
            project_name: Name of the project
            file_path: Relative path to the file within the repository
            
        Returns:
            File content as string, or None if file cannot be read
        """
        repo_path = self.get_repo_path(project_name)
        full_path = repo_path / file_path
        
        try:
            if not full_path.exists():
                return None
            
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(full_path, 'r', encoding='latin-1') as f:
                    return f.read()
            except Exception as e:
                logger.warning(f"Could not read file {file_path}: {e}")
                return None
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return None
    
    def _clean_markdown_code_blocks(self, content: str) -> str:
        """
        Remove markdown code block delimiters from content.

        Args:
            content: Raw content that may contain markdown code blocks

        Returns:
            Cleaned content without markdown delimiters
        """
        if not content:
            return content

        # Remove leading and trailing whitespace
        content = content.strip()

        # Check if content starts with ``` and ends with ```
        if content.startswith('```') and content.endswith('```'):
            lines = content.split('\n')

            # Remove first line if it's just ``` or ```language
            if lines[0].startswith('```'):
                lines = lines[1:]

            # Remove last line if it's just ```
            if lines and lines[-1].strip() == '```':
                lines = lines[:-1]

            # Join back and strip any remaining whitespace
            content = '\n'.join(lines).strip()

        return content

    def write_file_content(self, project_name: str, file_path: str, content: str) -> bool:
        """
        Write content to a file in the repository.

        Args:
            project_name: Name of the project
            file_path: Relative path to the file within the repository
            content: Content to write

        Returns:
            True if successful, False otherwise
        """
        repo_path = self.get_repo_path(project_name)
        full_path = repo_path / file_path

        try:
            # Clean markdown code blocks from content
            cleaned_content = self._clean_markdown_code_blocks(content)

            # Create directory if it doesn't exist
            full_path.parent.mkdir(parents=True, exist_ok=True)

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)

            logger.debug(f"Successfully wrote file: {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            return False
    
    def create_generated_directory(self, project_name: str, requirement_name: str) -> Path:
        """
        Create a directory for generated code for a specific requirement.
        
        Args:
            project_name: Name of the project
            requirement_name: Name of the requirement
            
        Returns:
            Path to the created directory
        """
        generated_path = self.get_generated_path(project_name, requirement_name)
        generated_path.mkdir(parents=True, exist_ok=True)
        return generated_path
    
    def cleanup_project(self, project_name: str) -> bool:
        """
        Clean up a project directory.
        
        Args:
            project_name: Name of the project to clean up
            
        Returns:
            True if successful, False otherwise
        """
        try:
            project_path = self.get_project_path(project_name)
            if project_path.exists():
                shutil.rmtree(project_path)
                logger.debug(f"Cleaned up project directory: {project_path}")
            return True
        except Exception as e:
            logger.error(f"Error cleaning up project {project_name}: {e}")
            return False

# Global instance
git_service = GitRepositoryService()
