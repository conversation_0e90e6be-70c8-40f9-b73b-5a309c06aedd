import json
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from pathlib import Path
import logging
from app.services.llm_service import llm_service
from app.services.git_service import git_service
from app.models.project import AutomationFramework, ProgrammingLanguage
from app.schemas.requirement import TestCase

logger = logging.getLogger(__name__)

class CodeGenerationService:
    """Service for generating automation test code using LLM."""

    def __init__(self):
        # Common code generation instructions to prevent formatting issues
        self.CODE_GENERATION_INSTRUCTIONS = """
CRITICAL CODE FORMATTING REQUIREMENTS:
- Generate ONLY clean, working code without any markdown formatting
- DO NOT include ```javascript, ```python, ```java, or any other language markers
- DO NOT add extra blank lines between code sections unless they are meaningful separators
- DO NOT include explanatory text or comments outside the code
- Output only the raw code that can be directly saved to a file
- Ensure proper indentation and syntax

WORLD-CLASS CODE QUALITY STANDARDS:

1. CONSTANTS & CONFIGURATION:
- Define all constants (URLs, timeouts, selectors, messages) in separate utility/constants files
- Use environment-specific configuration files for different test environments
- Never hardcode values directly in test methods
- Create reusable constant files that can be imported by multiple test files

2. ERROR HANDLING & RESILIENCE:
- Implement comprehensive try-catch blocks around all driver operations
- Add meaningful error messages with context about what operation failed
- Use explicit waits instead of implicit waits or sleep statements
- Implement retry mechanisms for flaky operations
- Add proper logging at debug, info, and error levels

3. CODE REUSABILITY & MODULARITY:
- Create utility classes for common operations (date handling, string manipulation, etc.)
- Implement helper methods for repetitive actions (login, navigation, data setup)
- Use inheritance properly with base classes containing common functionality
- Create data providers/factories for test data generation
- Implement proper dependency injection patterns

4. PERFORMANCE & OPTIMIZATION:
- Use efficient locator strategies (prefer ID > CSS > XPath)
- Implement proper resource cleanup in teardown methods
- Use parallel execution capabilities where appropriate
- Optimize wait times and polling intervals
- Implement smart element caching where beneficial

5. MAINTAINABILITY & READABILITY:
- Use descriptive method and variable names that explain intent
- Follow consistent naming conventions throughout the project
- Add meaningful comments for complex business logic
- Implement proper code organization with logical file structure
- Use design patterns appropriately (Factory, Builder, Strategy)

6. SECURITY & BEST PRACTICES:
- Never hardcode sensitive data (passwords, API keys, tokens)
- Use secure credential management approaches
- Implement proper data masking in logs
- Follow principle of least privilege in test setup
- Validate inputs and sanitize test data

7. TESTING BEST PRACTICES:
- Each test method should be independent and atomic
- Implement proper test data cleanup after each test
- Use meaningful assertions with descriptive failure messages
- Follow AAA pattern (Arrange, Act, Assert) in test methods
- Implement proper test categorization and tagging
"""

        self.framework_templates = {
            AutomationFramework.SELENIUM: {
                ProgrammingLanguage.PYTHON: "selenium_python",
                ProgrammingLanguage.JAVA: "selenium_java",
                ProgrammingLanguage.JAVASCRIPT: "selenium_javascript",
                ProgrammingLanguage.CSHARP: "selenium_csharp"
            },
            AutomationFramework.PLAYWRIGHT: {
                ProgrammingLanguage.PYTHON: "playwright_python",
                ProgrammingLanguage.JAVASCRIPT: "playwright_javascript",
                ProgrammingLanguage.JAVA: "playwright_java",
                ProgrammingLanguage.CSHARP: "playwright_csharp"
            }
        }
    
    async def generate_code_for_empty_repo(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        automation_framework: AutomationFramework,
        programming_language: ProgrammingLanguage,
        project_context: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate a complete automation project from scratch for an empty repository.
        
        Args:
            project_name: Name of the project
            requirement_name: Name of the requirement
            test_cases: List of test cases to implement
            page_elements: Page elements from crawling
            automation_framework: Selected automation framework
            programming_language: Selected programming language
            project_context: Additional project context
            
        Yields:
            Dictionary with generation progress and results
        """
        try:
            yield {"type": "status", "message": "Starting code generation for empty repository..."}
            
            # Create project structure
            yield {"type": "status", "message": "Creating project structure..."}
            project_structure = await self._create_project_structure(
                project_name, automation_framework, programming_language
            )
            
            yield {"type": "structure", "data": project_structure}
            
            # Generate base project files with streaming
            async for progress in self._generate_base_project_files_streaming(
                project_name, automation_framework, programming_language, project_context
            ):
                yield progress
            
            # Generate test files for the requirement with streaming
            yield {"type": "status", "message": f"Generating test files for requirement: {requirement_name}..."}
            async for progress in self._generate_test_files_for_requirement_streaming(
                project_name, requirement_name, test_cases, page_elements,
                automation_framework, programming_language
            ):
                yield progress
            
            # Generate configuration files
            yield {"type": "status", "message": "Generating configuration files..."}
            config_files = await self._generate_config_files(
                project_name, automation_framework, programming_language
            )
            
            for file_info in config_files:
                yield {"type": "file", "data": file_info}

            yield {"type": "complete", "message": "All files generated successfully"}

            # Generate AI explanation of the generated code
            yield {"type": "status", "message": "Generating code explanation..."}
            async for progress in self._generate_code_explanation_streaming(
                project_name, requirement_name, test_cases, automation_framework, programming_language
            ):
                yield progress
            
        except Exception as e:
            logger.error(f"Error in code generation: {e}")
            yield {"type": "error", "message": f"Code generation failed: {str(e)}"}
    
    async def generate_code_for_existing_repo(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        automation_framework: AutomationFramework,
        programming_language: ProgrammingLanguage,
        existing_code_context: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate automation test code for an existing repository.

        Args:
            project_name: Name of the project
            requirement_name: Name of the requirement
            test_cases: List of test cases to implement
            page_elements: Page elements from crawling
            automation_framework: Selected automation framework
            programming_language: Selected programming language
            existing_code_context: Context from existing codebase

        Yields:
            Dictionary with generation progress and results
        """
        try:
            yield {"type": "status", "message": "Analyzing existing codebase..."}

            # Analyze existing code structure
            code_analysis = await self._analyze_existing_code(project_name, automation_framework, programming_language)
            yield {"type": "analysis", "data": code_analysis}

            # Generate test files for the requirement with streaming
            yield {"type": "status", "message": f"Generating test files for requirement: {requirement_name}..."}
            async for progress in self._generate_test_files_for_existing_repo_streaming(
                project_name, requirement_name, test_cases, page_elements,
                automation_framework, programming_language, existing_code_context, code_analysis
            ):
                yield progress

            # Update configuration files if needed
            yield {"type": "status", "message": "Updating configuration files..."}
            updated_files = await self._update_config_files_for_existing_repo(
                project_name, automation_framework, programming_language, code_analysis
            )

            for file_info in updated_files:
                yield {"type": "file", "data": file_info}

            yield {"type": "complete", "message": "Test files generated and integrated successfully"}

            # Generate AI explanation of the generated code (same as empty repo)
            yield {"type": "status", "message": "Generating code explanation..."}
            async for progress in self._generate_code_explanation_streaming(
                project_name, requirement_name, test_cases, automation_framework, programming_language
            ):
                yield progress

        except Exception as e:
            logger.error(f"Error in code generation for existing repo: {e}")
            yield {"type": "error", "message": f"Code generation failed: {str(e)}"}
    
    async def _create_project_structure(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Dict[str, Any]:
        """Create the basic project structure."""
        structure = {
            "directories": [],
            "description": f"Project structure for {framework.value} with {language.value}"
        }
        
        repo_path = git_service.get_repo_path(project_name)
        
        if language == ProgrammingLanguage.PYTHON:
            dirs = ["tests", "pages", "utils", "config", "data"]
        elif language == ProgrammingLanguage.JAVASCRIPT:
            dirs = ["tests", "pages", "utils", "config", "data"]
        elif language == ProgrammingLanguage.JAVA:
            dirs = ["src/test/java", "src/main/java", "src/test/resources"]
        elif language == ProgrammingLanguage.CSHARP:
            dirs = ["Tests", "Pages", "Utils", "Config"]
        else:
            dirs = ["tests", "pages", "utils", "config"]
        
        for dir_name in dirs:
            dir_path = repo_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            structure["directories"].append(dir_name)
        
        return structure
    
    async def _generate_base_project_files(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        project_context: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Generate base project files like setup, requirements, etc."""
        files = []

        # Generate requirements/dependencies file
        deps_file = await self._generate_dependencies_file(framework, language)
        if deps_file:
            files.append(deps_file)

        # Generate base test class/utilities
        base_test = await self._generate_base_test_class(framework, language, project_context)
        if base_test:
            files.append(base_test)

        # Generate page object base class
        page_base = await self._generate_page_object_base(framework, language)
        if page_base:
            files.append(page_base)

        # Generate configuration file
        config_file = await self._generate_base_config(framework, language)
        if config_file:
            files.append(config_file)

        return files

    async def _generate_base_project_files_streaming(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        project_context: Optional[str] = None
    ):
        """Generate base project files with streaming for immediate feedback."""

        # Generate requirements/dependencies file with streaming
        yield {"type": "status", "message": "Generating dependencies file..."}
        async for progress in self._generate_dependencies_file_streaming(framework, language):
            yield progress

        # Generate base test class/utilities with streaming
        yield {"type": "status", "message": "Generating base test class..."}
        async for progress in self._generate_base_test_class_streaming(framework, language, project_context):
            yield progress

        # Generate page object base class with streaming
        yield {"type": "status", "message": "Generating page object base class..."}
        async for progress in self._generate_page_object_base_streaming(framework, language):
            yield progress

        # Generate configuration file with streaming
        yield {"type": "status", "message": "Generating base configuration..."}
        async for progress in self._generate_base_config_streaming(framework, language):
            yield progress
    
    async def _generate_dependencies_file(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Optional[Dict[str, Any]]:
        """Generate dependencies file (requirements.txt, package.json, etc.)."""
        
        system_prompt = f"""You are a senior automation engineer with 15+ years of experience. Generate a production-ready dependencies file for a {framework.value} automation project using {language.value}.

        CRITICAL REQUIREMENTS:
        - Generate ONLY valid, working code without syntax errors
        - Use stable, well-tested LTS versions of all dependencies
        - Include ALL necessary dependencies for enterprise-grade automation
        - Follow industry best practices for dependency management and security
        - Ensure compatibility between all dependencies with proper version pinning

        ENTERPRISE FEATURES TO INCLUDE:
        - {framework.value} automation framework (latest stable LTS version)
        - Advanced testing framework with parallel execution support
        - Comprehensive test reporting and dashboard generation (HTML, JSON, XML reports)
        - Professional logging and debugging tools with multiple log levels
        - Data-driven testing support (CSV, JSON, Excel, database readers)
        - Screenshot and video recording capabilities for test failures
        - Cross-browser and cross-platform testing support
        - API testing capabilities alongside UI testing
        - Performance monitoring and metrics collection
        - CI/CD integration tools and test result publishing
        - Security-focused dependencies for credential management
        - Code quality tools (linting, formatting, static analysis)
        - Browser drivers and utilities with auto-update capabilities
        - Configuration management for multiple environments
        - Utility libraries for date/time, string manipulation, file operations
        - Retry mechanisms and flaky test handling
        - Test data generation and management tools

        IMPORTANT: Output ONLY the file content without any explanations, comments, or markdown formatting.
        The output must be ready to use as-is in a real enterprise project."""
        
        if language == ProgrammingLanguage.PYTHON:
            prompt = "Generate a requirements.txt file for a Python Selenium/Playwright automation project."
            filename = "requirements.txt"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = "Generate a package.json file for a JavaScript/Node.js automation project."
            filename = "package.json"
        elif language == ProgrammingLanguage.JAVA:
            prompt = "Generate a pom.xml file for a Java Maven automation project."
            filename = "pom.xml"
        elif language == ProgrammingLanguage.CSHARP:
            prompt = "Generate a .csproj file for a C# automation project."
            filename = "AutomationTests.csproj"
        else:
            return None
        
        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            return {
                "path": filename,
                "content": sanitized_content,
                "type": "dependencies",
                "description": f"Dependencies file for {language.value} project"
            }

        return None

    async def _generate_file_with_streaming(
        self,
        prompt: str,
        system_prompt: str,
        filename: str,
        file_type: str,
        description: str,
        is_first_file: bool = False
    ):
        """Generate a file with streaming content."""
        content_buffer = ""
        first_chunk = True

        # Yield initial file info
        yield {
            "type": "file_start",
            "data": {
                "path": filename,
                "type": file_type,
                "description": description
            }
        }



        # Stream content generation
        async for chunk in llm_service._make_streaming_request(prompt, system_prompt):
            content_buffer += chunk

            # For the first chunk of the first file, use clear_and_start action
            action = None
            if is_first_file and first_chunk:
                action = "clear_and_start"
                first_chunk = False

            yield {
                "type": "code_chunk",
                "data": {
                    "path": filename,
                    "chunk": chunk,
                    "content": content_buffer,
                    "action": action
                }
            }



        # Sanitize and yield final file info
        if content_buffer:
            # Sanitize the generated code to remove markdown markers and extra lines
            sanitized_content = self._sanitize_generated_code(content_buffer)

            yield {
                "type": "file_complete",
                "data": {
                    "path": filename,
                    "content": sanitized_content,
                    "type": file_type,
                    "description": description
                }
            }
    
    async def _generate_base_test_class(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        project_context: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate base test class with setup and teardown."""
        
        context_section = f"Project context: {project_context}" if project_context else ""
        
        system_prompt = f"""You are a senior automation architect with 15+ years of experience. Generate a world-class base test class for a {framework.value} automation project using {language.value}.

        {self.CODE_GENERATION_INSTRUCTIONS}

        ENTERPRISE-GRADE BASE CLASS REQUIREMENTS:
        - Generate ONLY valid, production-ready code without syntax errors
        - Include comprehensive imports and dependencies
        - Follow SOLID principles and enterprise design patterns
        - Implement proper dependency injection and configuration management
        - Use advanced exception handling with custom exception types

        WORLD-CLASS FEATURES TO IMPLEMENT:
        - Multi-browser support with dynamic browser selection from config
        - Environment-specific configuration loading (dev, staging, prod)
        - Advanced logging with structured logging (JSON format) and log rotation
        - Screenshot and video recording on failures with timestamp and test context
        - Comprehensive wait utilities (explicit waits, custom wait conditions)
        - Element interaction helpers with retry logic and error recovery
        - Test data management with data providers and factories
        - Performance monitoring and test execution metrics collection
        - Parallel execution support with thread-safe operations
        - Custom assertions with detailed failure reporting
        - Test categorization and tagging support
        - Database connection utilities for test data setup/cleanup
        - API testing utilities for hybrid UI/API test scenarios
        - Security utilities for credential management and data masking
        - Cross-platform compatibility (Windows, Mac, Linux)
        - CI/CD integration hooks and test result reporting
        - Memory and resource leak prevention
        - Flaky test detection and automatic retry mechanisms
        - Test execution context tracking and debugging utilities

        {context_section}

        Follow {language.value} enterprise coding standards and {framework.value} advanced patterns.
        Implement comprehensive error handling with meaningful, actionable error messages.

        IMPORTANT: Output ONLY the code without any explanations, comments, or markdown formatting.
        The code must be enterprise-ready and suitable for large-scale automation projects."""
        
        if language == ProgrammingLanguage.PYTHON:
            prompt = f"Generate a BaseTest class in Python for {framework.value} automation."
            filename = "tests/base_test.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"Generate a BaseTest class in JavaScript for {framework.value} automation."
            filename = "tests/baseTest.js"
        elif language == ProgrammingLanguage.JAVA:
            prompt = f"Generate a BaseTest class in Java for {framework.value} automation."
            filename = "src/test/java/BaseTest.java"
        elif language == ProgrammingLanguage.CSHARP:
            prompt = f"Generate a BaseTest class in C# for {framework.value} automation."
            filename = "Tests/BaseTest.cs"
        else:
            return None
        
        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            return {
                "path": filename,
                "content": sanitized_content,
                "type": "base_class",
                "description": f"Base test class for {framework.value} automation"
            }
        
        return None

    async def _generate_page_object_base(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Optional[Dict[str, Any]]:
        """Generate base page object class."""

        system_prompt = f"""You are a senior automation architect with 15+ years of experience. Generate a world-class base page object class for a {framework.value} automation project using {language.value}.

        {self.CODE_GENERATION_INSTRUCTIONS}

        ENTERPRISE-GRADE PAGE OBJECT REQUIREMENTS:
        - Generate ONLY valid, production-ready code without syntax errors
        - Include comprehensive imports and advanced dependencies
        - Follow advanced page object model patterns and SOLID principles
        - Implement enterprise-level error handling and recovery mechanisms
        - Use structured logging with contextual information

        WORLD-CLASS PAGE OBJECT FEATURES:
        - Advanced element location strategies with fallback mechanisms
        - Intelligent wait conditions with custom predicates and timeout handling
        - Element interaction helpers with automatic retry and error recovery
        - Smart screenshot capture with element highlighting and context
        - Comprehensive page validation with business rule checking
        - Advanced navigation helpers with URL validation and redirect handling
        - Form interaction utilities with data validation and error detection
        - JavaScript execution helpers with result validation and error handling
        - Performance monitoring for page load times and element interactions
        - Cross-browser compatibility utilities with browser-specific optimizations
        - Mobile and responsive design testing capabilities
        - Accessibility testing integration (WCAG compliance checking)
        - Visual regression testing utilities with image comparison
        - API integration for hybrid testing scenarios
        - Database interaction utilities for test data verification
        - File upload/download handling with validation
        - Cookie and session management utilities
        - Security testing helpers (XSS, CSRF protection validation)
        - Internationalization support for multi-language testing
        - Advanced debugging utilities with element inspection and DOM analysis
        - Memory leak detection and resource cleanup
        - Parallel execution support with thread-safe operations
        - Test data masking and security compliance features

        Follow {language.value} enterprise coding standards and {framework.value} advanced patterns.
        Implement comprehensive error handling with actionable error messages and recovery suggestions.

        IMPORTANT: Output ONLY the code without any explanations, comments, or markdown formatting.
        The code must be enterprise-ready and suitable for large-scale automation frameworks."""

        if language == ProgrammingLanguage.PYTHON:
            prompt = f"Generate a BasePage class in Python for {framework.value} automation using page object model."
            filename = "pages/base_page.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"Generate a BasePage class in JavaScript for {framework.value} automation using page object model."
            filename = "pages/basePage.js"
        elif language == ProgrammingLanguage.JAVA:
            prompt = f"Generate a BasePage class in Java for {framework.value} automation using page object model."
            filename = "src/main/java/BasePage.java"
        elif language == ProgrammingLanguage.CSHARP:
            prompt = f"Generate a BasePage class in C# for {framework.value} automation using page object model."
            filename = "Pages/BasePage.cs"
        else:
            return None

        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            return {
                "path": filename,
                "content": sanitized_content,
                "type": "page_base",
                "description": f"Base page object class for {framework.value} automation"
            }

        return None

    async def _generate_base_config(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Optional[Dict[str, Any]]:
        """Generate base configuration file."""

        system_prompt = f"""You are a senior automation architect with 15+ years of experience. Generate a world-class configuration file for a {framework.value} automation project using {language.value}.

        {self.CODE_GENERATION_INSTRUCTIONS}

        ENTERPRISE-GRADE CONFIGURATION REQUIREMENTS:
        - Generate ONLY valid, production-ready configuration without syntax errors
        - Use realistic, enterprise-level configuration values
        - Follow industry best practices and security standards
        - Ensure configuration supports multiple environments and deployment scenarios
        - Include comprehensive settings for all aspects of enterprise automation

        WORLD-CLASS CONFIGURATION FEATURES:
        - Multi-browser support with version-specific configurations (Chrome, Firefox, Safari, Edge, mobile browsers)
        - Environment-specific settings (dev, test, staging, prod, DR) with secure credential management
        - Advanced timeout configurations (page load, element wait, script execution, network timeouts)
        - Comprehensive test data management (file paths, database connections, API endpoints)
        - Enterprise reporting settings (HTML, XML, JSON, Allure, custom dashboards)
        - Advanced logging configuration (structured logging, log rotation, remote logging, ELK integration)
        - Screenshot and video recording with quality settings and storage management
        - Parallel execution settings with resource management and load balancing
        - Intelligent retry and recovery configurations with exponential backoff
        - Database connection pooling and transaction management settings
        - API endpoint configurations with authentication, rate limiting, and circuit breakers
        - Security settings (credential encryption, SSL/TLS configuration, proxy settings)
        - Performance monitoring and metrics collection (response times, resource usage)
        - CI/CD integration settings (Jenkins, GitLab, Azure DevOps, GitHub Actions)
        - Cloud testing platform configurations (BrowserStack, Sauce Labs, AWS Device Farm)
        - Accessibility testing configurations (WCAG compliance levels, screen readers)
        - Visual regression testing settings (image comparison thresholds, baseline management)
        - Mobile testing configurations (device capabilities, app paths, simulators)
        - Cross-platform settings (Windows, Mac, Linux specific configurations)
        - Internationalization settings (locales, time zones, currency formats)
        - Test categorization and tagging configurations
        - Memory and resource management settings
        - Custom constants and application-specific settings organized in logical sections

        Use appropriate format for {language.value} with proper structure and validation.
        Include comprehensive comments explaining each configuration section and option.
        Organize settings in logical groups with clear naming conventions.

        IMPORTANT: Output ONLY the configuration content without any explanations or markdown formatting.
        The configuration must be enterprise-ready and suitable for large-scale automation projects."""

        if language == ProgrammingLanguage.PYTHON:
            prompt = f"Generate a config.py file for Python {framework.value} automation project."
            filename = "config/config.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"Generate a config.json file for JavaScript {framework.value} automation project."
            filename = "config/config.json"
        elif language == ProgrammingLanguage.JAVA:
            prompt = f"Generate a config.properties file for Java {framework.value} automation project."
            filename = "src/test/resources/config.properties"
        elif language == ProgrammingLanguage.CSHARP:
            prompt = f"Generate an appsettings.json file for C# {framework.value} automation project."
            filename = "Config/appsettings.json"
        else:
            return None

        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            return {
                "path": filename,
                "content": sanitized_content,
                "type": "config",
                "description": f"Configuration file for {framework.value} automation"
            }

        return None

    # Streaming versions of base file generation methods
    async def _generate_dependencies_file_streaming(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate dependencies file with streaming."""

        system_prompt = f"""You are an expert automation engineer with 10+ years of experience. Generate a comprehensive dependencies file for a {framework.value} automation project using {language.value}.

        CRITICAL REQUIREMENTS:
        - Generate ONLY valid, working code without syntax errors
        - Include all necessary dependencies for {framework.value} with {language.value}
        - Use stable, well-tested versions
        - Include testing frameworks, reporting tools, and utilities
        - Follow best practices for dependency management
        - Output only the file content without explanations or markdown formatting"""

        if language == ProgrammingLanguage.PYTHON:
            prompt = f"""Generate a comprehensive requirements.txt file for a {framework.value} automation project.

            Include dependencies for:
            - {framework.value} WebDriver
            - Testing framework (pytest/unittest)
            - Reporting and logging
            - Configuration management
            - Data handling utilities
            - Browser management
            - Assertion libraries

            Generate clean, working requirements.txt content:"""
            filename = "requirements.txt"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"""Generate a comprehensive package.json file for a {framework.value} automation project.

            Include dependencies for:
            - {framework.value} WebDriver
            - Testing framework (Jest/Mocha)
            - Reporting and logging
            - Configuration management
            - Browser management
            - Assertion libraries

            Generate clean, working package.json content:"""
            filename = "package.json"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "dependencies", f"Dependencies file for {framework.value} project", is_first_file=True
        ):
            yield progress

    async def _generate_base_test_class_streaming(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        project_context: Optional[str] = None
    ):
        """Generate base test class with streaming."""

        context_section = f"Project context: {project_context}" if project_context else ""

        system_prompt = f"""You are an expert automation engineer with 10+ years of experience. Generate a robust base test class for a {framework.value} automation project using {language.value}.

        CRITICAL REQUIREMENTS:
        - Generate ONLY valid, working code without syntax errors
        - Include proper setup and teardown methods
        - Implement WebDriver initialization and cleanup
        - Include utility methods for common operations
        - Add proper error handling and logging
        - Follow testing best practices for {language.value}
        - Include proper imports and dependencies
        - Output only the code without explanations or markdown formatting"""

        if language == ProgrammingLanguage.PYTHON:
            prompt = f"""Generate a comprehensive base test class for {framework.value} automation.

            {context_section}

            Include:
            - WebDriver setup and teardown
            - Browser configuration
            - Common utility methods
            - Error handling and logging
            - Screenshot capture on failure
            - Wait utilities
            - Test data management

            Generate clean, working Python code:"""
            filename = "base/base_test.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"""Generate a comprehensive base test class for {framework.value} automation.

            {context_section}

            Include:
            - WebDriver setup and teardown
            - Browser configuration
            - Common utility methods
            - Error handling and logging
            - Screenshot capture on failure
            - Wait utilities

            Generate clean, working JavaScript code:"""
            filename = "base/BaseTest.js"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "base_test", f"Base test class for {framework.value} project"
        ):
            yield progress

    async def _generate_page_object_base_streaming(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate base page object class with streaming."""

        system_prompt = f"""You are an expert automation engineer with 10+ years of experience. Generate a comprehensive base page object class for a {framework.value} automation project using {language.value}.

        CRITICAL REQUIREMENTS:
        - Generate ONLY valid, working code without syntax errors
        - Include common page operations and utilities
        - Implement proper wait strategies
        - Add element interaction methods
        - Include error handling and logging
        - Follow page object model best practices
        - Include proper imports and dependencies
        - Output only the code without explanations or markdown formatting"""

        if language == ProgrammingLanguage.PYTHON:
            prompt = f"""Generate a comprehensive base page object class for {framework.value} automation.

            Include:
            - WebDriver initialization
            - Common element interaction methods
            - Wait utilities (explicit waits)
            - Error handling and logging
            - Screenshot utilities
            - Navigation methods
            - Element validation methods

            Generate clean, working Python code:"""
            filename = "base/base_page.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"""Generate a comprehensive base page object class for {framework.value} automation.

            Include:
            - WebDriver initialization
            - Common element interaction methods
            - Wait utilities
            - Error handling and logging
            - Navigation methods
            - Element validation methods

            Generate clean, working JavaScript code:"""
            filename = "base/BasePage.js"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "base_page", f"Base page object class for {framework.value} project"
        ):
            yield progress

    async def _generate_base_config_streaming(
        self,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate base configuration file with streaming."""

        system_prompt = f"""You are an expert automation engineer with 10+ years of experience. Generate a comprehensive configuration file for a {framework.value} automation project using {language.value}.

        CRITICAL REQUIREMENTS:
        - Generate ONLY valid, working configuration without syntax errors
        - Include browser configurations
        - Add environment settings
        - Include timeout configurations
        - Add URL configurations
        - Include test data paths
        - Follow configuration best practices
        - Output only the configuration content without explanations or markdown formatting"""

        if language == ProgrammingLanguage.PYTHON:
            prompt = f"""Generate a comprehensive config.py file for {framework.value} automation.

            Include:
            - Browser settings (Chrome, Firefox, etc.)
            - Environment configurations (dev, staging, prod)
            - Timeout settings
            - URL configurations
            - Test data paths
            - Logging configuration
            - Screenshot settings

            Generate clean, working Python configuration:"""
            filename = "config/config.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            prompt = f"""Generate a comprehensive config.js file for {framework.value} automation.

            Include:
            - Browser settings
            - Environment configurations
            - Timeout settings
            - URL configurations
            - Test data paths
            - Logging configuration

            Generate clean, working JavaScript configuration:"""
            filename = "config/config.js"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "config", f"Configuration file for {framework.value} automation"
        ):
            yield progress

    async def _generate_code_explanation_streaming(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate AI explanation of the generated code with streaming."""

        test_cases_summary = f"{len(test_cases)} test cases" if test_cases else "test cases"

        system_prompt = """You are an expert automation engineer. Generate a brief, technical explanation of the generated automation test code.

        CRITICAL REQUIREMENTS:
        - Write in a clear, technical, and concise tone
        - Focus only on technical aspects and implementation details
        - NO congratulatory messages or small talk
        - NO marketing language or confidence statements
        - Be specific about the technologies and frameworks used
        - Explain the project structure and key components
        - Mention technical patterns and practices implemented
        - Keep it informative and strictly technical
        - Maximum 200 words

        FORMATTING REQUIREMENTS:
        - Use emojis like ✅, 🚀, 🎉, 🎯, 🔧 to highlight key points
        - Use **text** for emphasis instead of headers
        - Use numbered lists (1. item, 2. item) instead of bullets or dashes
        - Use single newlines within sections
        - Use double newlines between different sections/paragraphs
        - NO heading markers (#, ##, etc.)
        - Keep formatting clean and readable"""

        prompt = f"""Generate a brief technical explanation for the automation test code generated for:

        Project: {project_name}
        Requirement: {requirement_name}
        Framework: {framework.value}
        Language: {language.value}
        Test Cases: {test_cases_summary}

        Cover these technical aspects using the specified formatting:
        1. Project structure and file organization
        2. Key components generated (test classes, page objects, base classes)
        3. Framework-specific implementation details
        4. Design patterns used (Page Object Model, etc.)
        5. Configuration and dependency management

        Example format:
        🎯 **Generated Automation Framework**

        1. **Project Structure**: Created modular test architecture with separate directories for tests, page objects, and utilities
        2. **Key Components**: Generated base test class, page object models, and configuration files
        3. **Framework Integration**: Implemented {framework.value} with proper driver management and test lifecycle hooks

        🔧 **Technical Implementation**

        1. **Design Patterns**: Applied Page Object Model for maintainable UI interactions
        2. **Configuration**: Set up environment-specific configuration management

        Keep it technical, concise, and under 200 words. No congratulations or marketing language.

        Generate technical explanation:"""

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, "explanation.md", "explanation", "AI-generated explanation of the code"
        ):
            # Modify the progress to indicate this is an explanation
            if progress.get("type") == "code_chunk":
                progress["type"] = "explanation_chunk"
                chunk_content = progress["data"].get("chunk", "")
                progress["data"]["explanation_chunk"] = chunk_content
            elif progress.get("type") == "file_complete":
                progress["type"] = "explanation_complete"
                full_content = progress["data"].get("content", "")
                progress["data"]["explanation"] = full_content

            yield progress

    # Code review method removed for simplified workflow

    async def _generate_test_files_for_requirement(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> List[Dict[str, Any]]:
        """Generate test files for a specific requirement."""
        files = []

        # Generate page object for the requirement
        page_object = await self._generate_page_object_for_requirement(
            requirement_name, page_elements, framework, language
        )
        if page_object:
            files.append(page_object)

        # Generate test class for the requirement
        test_class = await self._generate_test_class_for_requirement(
            requirement_name, test_cases, page_elements, framework, language
        )
        if test_class:
            files.append(test_class)

        return files

    async def _generate_test_files_for_requirement_streaming(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate test files for a specific requirement with streaming."""

        # Generate page object for the requirement with streaming
        yield {"type": "status", "message": f"Generating page object for {requirement_name}..."}
        async for progress in self._generate_page_object_for_requirement_streaming(
            requirement_name, page_elements, framework, language
        ):
            yield progress

        # Generate test class for the requirement with streaming
        yield {"type": "status", "message": f"Generating test class for {requirement_name}..."}
        async for progress in self._generate_test_class_for_requirement_streaming(
            requirement_name, test_cases, page_elements, framework, language
        ):
            yield progress

    async def _generate_page_object_for_requirement(
        self,
        requirement_name: str,
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Optional[Dict[str, Any]]:
        """Generate page object class for a specific requirement."""

        # Filter relevant page elements
        relevant_elements = self._filter_relevant_elements(page_elements)
        elements_json = json.dumps(relevant_elements, indent=2)

        system_prompt = f"""You are an expert automation engineer. Generate a page object class for a specific requirement using {framework.value} with {language.value}.

        The page object should:
        - Extend the base page class
        - Include locators for all relevant elements
        - Provide methods for interacting with page elements
        - Follow page object model best practices
        - Include proper error handling and logging

        Use the provided page elements to create appropriate locators and methods.
        Output only the code without any explanations or markdown formatting."""

        prompt = f"""Generate a page object class for requirement: {requirement_name}

        Page elements available:
        {elements_json}

        Create a comprehensive page object class with:
        1. Locators for all interactive elements
        2. Methods for common page operations
        3. Validation methods
        4. Proper inheritance from base page class"""

        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            class_name = self._sanitize_class_name(requirement_name)

            if language == ProgrammingLanguage.PYTHON:
                filename = f"pages/{class_name.lower()}_page.py"
            elif language == ProgrammingLanguage.JAVASCRIPT:
                filename = f"pages/{class_name}Page.js"
            elif language == ProgrammingLanguage.JAVA:
                filename = f"src/main/java/{class_name}Page.java"
            elif language == ProgrammingLanguage.CSHARP:
                filename = f"Pages/{class_name}Page.cs"
            else:
                return None

            return {
                "path": filename,
                "content": sanitized_content,
                "type": "page_object",
                "description": f"Page object for {requirement_name}"
            }

        return None

    async def _generate_page_object_for_requirement_streaming(
        self,
        requirement_name: str,
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate page object class for a specific requirement with streaming."""

        # Filter relevant page elements
        relevant_elements = self._filter_relevant_elements(page_elements)
        elements_json = json.dumps(relevant_elements, indent=2)

        system_prompt = f"""You are an expert automation engineer. Generate a page object class for a specific requirement using {framework.value} with {language.value}.

        The page object should:
        - Extend the base page class
        - Include locators for all relevant elements
        - Provide methods for interacting with page elements
        - Follow page object model best practices
        - Include proper error handling and logging
        - Generate clean, working code without syntax errors
        - Include proper imports and dependencies
        - Follow coding best practices for {language.value}

        Use the provided page elements to create appropriate locators and methods.
        Output only the code without any explanations or markdown formatting."""

        prompt = f"""Generate a page object class for requirement: {requirement_name}

        Page elements available:
        {elements_json}

        Create a comprehensive page object class with:
        1. Locators for all interactive elements
        2. Methods for common page operations
        3. Validation methods
        4. Proper inheritance from base page class
        5. Error handling and logging
        6. Clean, working code that runs without errors"""

        class_name = self._sanitize_class_name(requirement_name)

        if language == ProgrammingLanguage.PYTHON:
            filename = f"pages/{class_name.lower()}_page.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            filename = f"pages/{class_name}Page.js"
        elif language == ProgrammingLanguage.JAVA:
            filename = f"src/main/java/{class_name}Page.java"
        elif language == ProgrammingLanguage.CSHARP:
            filename = f"Pages/{class_name}Page.cs"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "page_object", f"Page object for {requirement_name}"
        ):
            yield progress

    async def _generate_test_class_for_requirement(
        self,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Optional[Dict[str, Any]]:
        """Generate test class for a specific requirement."""

        test_cases_text = self._format_test_cases_for_prompt(test_cases)
        relevant_elements = self._filter_relevant_elements(page_elements)
        elements_json = json.dumps(relevant_elements, indent=2)

        system_prompt = f"""You are a senior automation architect with 15+ years of experience. Generate a world-class test class for a specific requirement using {framework.value} with {language.value}.

        {self.CODE_GENERATION_INSTRUCTIONS}

        ENTERPRISE-GRADE TEST CLASS REQUIREMENTS:
        - Generate ONLY valid, production-ready code without syntax errors
        - Extend the base test class with proper inheritance
        - Implement all provided test cases as independent, atomic test methods
        - Use the corresponding page object class with proper dependency injection
        - Include comprehensive assertions with meaningful failure messages
        - Follow advanced testing best practices and design patterns

        WORLD-CLASS TEST FEATURES TO IMPLEMENT:
        - Create separate constants/utilities file for reusable values (URLs, timeouts, messages, test data)
        - Implement data-driven testing with external data sources (CSV, JSON, database)
        - Add comprehensive test categorization and tagging for test organization
        - Include performance assertions and monitoring (page load times, response times)
        - Implement proper test data setup and cleanup with database/API integration
        - Add screenshot capture on failures with test context and timestamps
        - Include cross-browser compatibility testing capabilities
        - Implement retry mechanisms for flaky test scenarios
        - Add comprehensive logging with structured test execution context
        - Include API validation alongside UI testing for hybrid scenarios
        - Implement security testing validations (XSS, CSRF, authentication)
        - Add accessibility testing checks (WCAG compliance, screen reader compatibility)
        - Include visual regression testing capabilities with baseline comparisons
        - Implement mobile responsiveness testing for different screen sizes
        - Add internationalization testing support for multiple locales
        - Include performance profiling and memory leak detection
        - Implement custom assertions for business rule validation
        - Add test execution metrics and reporting integration
        - Include parallel execution support with thread-safe operations
        - Implement test dependency management and execution ordering

        CONSTANTS AND UTILITIES ORGANIZATION:
        - Create separate constants file with all hardcoded values (URLs, selectors, messages, timeouts)
        - Implement utility classes for common operations (date handling, string manipulation, file operations)
        - Create test data factories for generating realistic test data
        - Implement helper methods for repetitive actions (login, navigation, form filling)
        - Add validation utilities for common checks (email format, phone numbers, etc.)

        Use the provided test cases and page elements to create comprehensive, enterprise-ready tests.
        Output only the code without any explanations or markdown formatting."""

        prompt = f"""Generate a test class for requirement: {requirement_name}

        Test cases to implement:
        {test_cases_text}

        Page elements available:
        {elements_json}

        Create a comprehensive test class with:
        1. Test methods for each test case
        2. Proper setup and teardown
        3. Data-driven testing where applicable
        4. Assertions and validations
        5. Error handling and reporting"""

        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            class_name = self._sanitize_class_name(requirement_name)

            if language == ProgrammingLanguage.PYTHON:
                filename = f"tests/test_{class_name.lower()}.py"
            elif language == ProgrammingLanguage.JAVASCRIPT:
                filename = f"tests/{class_name}Test.js"
            elif language == ProgrammingLanguage.JAVA:
                filename = f"src/test/java/{class_name}Test.java"
            elif language == ProgrammingLanguage.CSHARP:
                filename = f"Tests/{class_name}Test.cs"
            else:
                return None

            return {
                "path": filename,
                "content": sanitized_content,
                "type": "test_class",
                "description": f"Test class for {requirement_name}"
            }

        return None

    async def _generate_test_class_for_requirement_streaming(
        self,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ):
        """Generate test class for a specific requirement with streaming."""

        test_cases_text = self._format_test_cases_for_prompt(test_cases)
        relevant_elements = self._filter_relevant_elements(page_elements)
        elements_json = json.dumps(relevant_elements, indent=2)

        system_prompt = f"""You are an expert automation engineer. Generate a test class for a specific requirement using {framework.value} with {language.value}.

        The test class should:
        - Extend the base test class
        - Implement all provided test cases as separate test methods
        - Use the corresponding page object class
        - Include proper assertions and validations
        - Follow testing best practices
        - Include proper error handling and logging
        - Generate clean, working code without syntax errors
        - Include proper imports and dependencies
        - Follow coding best practices for {language.value}
        - Use appropriate test framework conventions

        Use the provided test cases and page elements to create comprehensive tests.
        Output only the code without any explanations or markdown formatting."""

        prompt = f"""Generate a test class for requirement: {requirement_name}

        Test cases to implement:
        {test_cases_text}

        Page elements available:
        {elements_json}

        Create a comprehensive test class with:
        1. Test methods for each test case
        2. Proper setup and teardown
        3. Data-driven testing where applicable
        4. Assertions and validations
        5. Error handling and reporting
        6. Clean, working code that runs without errors"""

        class_name = self._sanitize_class_name(requirement_name)

        if language == ProgrammingLanguage.PYTHON:
            filename = f"tests/test_{class_name.lower()}.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            filename = f"tests/{class_name}Test.js"
        elif language == ProgrammingLanguage.JAVA:
            filename = f"src/test/java/{class_name}Test.java"
        elif language == ProgrammingLanguage.CSHARP:
            filename = f"Tests/{class_name}Test.cs"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "test_class", f"Test class for {requirement_name}"
        ):
            yield progress

    async def _generate_config_files(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> List[Dict[str, Any]]:
        """Generate additional configuration files."""
        files = []

        # Generate README file
        readme = await self._generate_readme_file(project_name, framework, language)
        if readme:
            files.append(readme)

        # Generate .gitignore file
        gitignore = await self._generate_gitignore_file(language)
        if gitignore:
            files.append(gitignore)

        return files

    async def _generate_readme_file(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Optional[Dict[str, Any]]:
        """Generate README.md file for the project."""

        system_prompt = f"""You are an expert technical writer. Generate a comprehensive README.md file for a {framework.value} automation project using {language.value}.

        The README should include:
        - Project description
        - Prerequisites and setup instructions
        - How to run tests
        - Project structure explanation
        - Configuration details
        - Reporting information
        - Troubleshooting tips

        Use proper markdown formatting and be clear and concise.
        Output only the markdown content without any explanations."""

        prompt = f"""Generate a README.md file for automation project: {project_name}

        Technology stack:
        - Framework: {framework.value}
        - Language: {language.value}

        Include comprehensive setup and usage instructions."""

        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated content (README is markdown, but may contain code blocks)
            sanitized_content = self._sanitize_generated_code(content)
            return {
                "path": "README.md",
                "content": sanitized_content,
                "type": "documentation",
                "description": "Project documentation and setup instructions"
            }

        return None

    async def _generate_gitignore_file(self, language: ProgrammingLanguage) -> Optional[Dict[str, Any]]:
        """Generate .gitignore file for the project."""

        gitignore_content = ""

        if language == ProgrammingLanguage.PYTHON:
            gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# Test reports
reports/
screenshots/
*.xml
*.html

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db"""

        elif language == ProgrammingLanguage.JAVASCRIPT:
            gitignore_content = """# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test reports
reports/
screenshots/
*.xml
*.html

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db"""

        elif language == ProgrammingLanguage.JAVA:
            gitignore_content = """# Java
*.class
*.jar
*.war
*.ear
target/
.mvn/
mvnw
mvnw.cmd

# Test reports
reports/
screenshots/
*.xml
*.html

# IDE
.vscode/
.idea/
*.iml
*.swp
*.swo

# OS
.DS_Store
Thumbs.db"""

        elif language == ProgrammingLanguage.CSHARP:
            gitignore_content = """# C#
bin/
obj/
*.user
*.suo
*.cache
*.dll
*.exe
*.pdb

# Test reports
reports/
screenshots/
*.xml
*.html

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db"""

        if gitignore_content:
            return {
                "path": ".gitignore",
                "content": gitignore_content,
                "type": "config",
                "description": "Git ignore file"
            }

        return None

    def _filter_relevant_elements(self, page_elements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter page elements to include only interactive/relevant ones."""
        if not page_elements or 'elements' not in page_elements:
            return []

        relevant_types = {
            'input', 'button', 'select', 'textarea', 'a', 'form',
            'checkbox', 'radio', 'submit', 'link'
        }

        filtered_elements = []
        for element in page_elements.get('elements', []):
            tag_name = element.get('tag_name', '').lower()
            element_type = element.get('type', '').lower()

            # Include interactive elements
            if (tag_name in relevant_types or
                element_type in relevant_types or
                element.get('clickable', False) or
                element.get('text', '').strip()):

                filtered_elements.append({
                    'tag_name': element.get('tag_name'),
                    'type': element.get('type'),
                    'text': element.get('text', '').strip()[:100],  # Limit text length
                    'css_selector': element.get('css_selector'),
                    'xpath': element.get('xpath'),
                    'clickable': element.get('clickable', False),
                    'visible': element.get('visible', True)
                })

        return filtered_elements[:50]  # Limit to 50 most relevant elements

    def _format_test_cases_for_prompt(self, test_cases: List[TestCase]) -> str:
        """Format test cases for LLM prompt."""
        formatted_cases = []
        for i, test_case in enumerate(test_cases, 1):
            case_text = f"""Test Case {i}: {test_case.title}
Steps: {test_case.steps or 'Not specified'}
Expected Result: {test_case.expected_result or 'Not specified'}
Notes: {test_case.notes or 'None'}"""
            formatted_cases.append(case_text)

        return "\n\n".join(formatted_cases)

    def _format_page_elements_for_prompt(self, page_elements: Dict[str, Any]) -> str:
        """Format page elements for LLM prompt."""
        if not page_elements or not page_elements.get('elements'):
            return "No page elements available."

        relevant_elements = self._filter_relevant_elements(page_elements)
        if not relevant_elements:
            return "No relevant page elements found."

        formatted_elements = []
        for i, element in enumerate(relevant_elements[:20], 1):  # Limit to 20 elements
            element_text = f"{i}. {element.get('tag_name', 'unknown').upper()}"

            if element.get('type'):
                element_text += f" (type: {element.get('type')})"

            if element.get('text'):
                element_text += f" - Text: '{element.get('text')[:50]}'"

            if element.get('css_selector'):
                element_text += f" - CSS: {element.get('css_selector')[:50]}"
            elif element.get('xpath'):
                element_text += f" - XPath: {element.get('xpath')[:50]}"

            if element.get('clickable'):
                element_text += " [Clickable]"

            formatted_elements.append(element_text)

        return "\n".join(formatted_elements)

    def _sanitize_method_name(self, name: str) -> str:
        """Sanitize a name to be used as a method name."""
        import re

        # Remove special characters and replace with underscores
        sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name)

        # Remove multiple consecutive underscores
        sanitized = re.sub(r'_+', '_', sanitized)

        # Remove leading/trailing underscores
        sanitized = sanitized.strip('_')

        # Convert to camelCase for method names
        words = sanitized.split('_')
        if words:
            # First word lowercase, rest title case
            camel_case = words[0].lower() + ''.join(word.capitalize() for word in words[1:])
            return camel_case

        return 'unknownTest'

    def _sanitize_generated_code(self, code_content: str) -> str:
        """
        Sanitize generated code by removing markdown code blocks, extra lines, and formatting.
        """
        if not code_content:
            return ""

        # Remove markdown code block markers
        lines = code_content.split('\n')
        cleaned_lines = []

        for line in lines:
            stripped_line = line.strip()

            # Skip markdown code block markers
            if (stripped_line.startswith('```') and
                any(lang in stripped_line.lower() for lang in ['javascript', 'python', 'java', 'csharp', 'typescript', 'js', 'ts', 'py'])):
                continue
            elif stripped_line == '```':
                continue

            # Keep the line
            cleaned_lines.append(line)

        # Join lines and remove excessive blank lines
        cleaned_code = '\n'.join(cleaned_lines)

        # Remove more than 2 consecutive blank lines
        import re
        cleaned_code = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_code)

        # Remove leading/trailing whitespace
        cleaned_code = cleaned_code.strip()

        return cleaned_code

    def _sanitize_class_name(self, name: str) -> str:
        """Sanitize a name to be used as a class name."""
        import re
        # Remove special characters and replace with underscore
        sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        # Remove leading numbers
        sanitized = re.sub(r'^[0-9]+', '', sanitized)
        # Convert to PascalCase
        words = sanitized.split('_')
        return ''.join(word.capitalize() for word in words if word)

    async def _analyze_existing_code(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage
    ) -> Dict[str, Any]:
        """Analyze existing code structure and patterns."""
        repo_files = git_service.get_repository_files(project_name)

        analysis = {
            "structure": {},
            "patterns": [],
            "dependencies": [],
            "test_files": [],
            "page_objects": [],
            "config_files": []
        }

        for file_info in repo_files:
            file_path = file_info['path']

            if file_info['is_text']:
                if self._is_test_file(file_path, language):
                    analysis["test_files"].append(file_path)
                elif self._is_page_object_file(file_path, language):
                    analysis["page_objects"].append(file_path)
                elif self._is_config_file(file_path, language):
                    analysis["config_files"].append(file_path)
                elif self._is_dependency_file(file_path, language):
                    analysis["dependencies"].append(file_path)

        return analysis

    def _is_test_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a test file."""
        file_path_lower = file_path.lower()

        if language == ProgrammingLanguage.PYTHON:
            return 'test' in file_path_lower and file_path.endswith('.py')
        elif language == ProgrammingLanguage.JAVASCRIPT:
            return 'test' in file_path_lower and (file_path.endswith('.js') or file_path.endswith('.ts'))
        elif language == ProgrammingLanguage.JAVA:
            return 'test' in file_path_lower and file_path.endswith('.java')
        elif language == ProgrammingLanguage.CSHARP:
            return 'test' in file_path_lower and file_path.endswith('.cs')

        return False

    def _is_page_object_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a page object file."""
        file_path_lower = file_path.lower()
        return 'page' in file_path_lower and not 'test' in file_path_lower

    def _is_config_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a configuration file."""
        config_patterns = ['config', 'settings', 'properties', 'json', 'yaml', 'yml']
        return any(pattern in file_path.lower() for pattern in config_patterns)

    def _is_dependency_file(self, file_path: str, language: ProgrammingLanguage) -> bool:
        """Check if a file is a dependency file."""
        dependency_files = {
            ProgrammingLanguage.PYTHON: ['requirements.txt', 'setup.py', 'pyproject.toml'],
            ProgrammingLanguage.JAVASCRIPT: ['package.json', 'package-lock.json', 'yarn.lock'],
            ProgrammingLanguage.JAVA: ['pom.xml', 'build.gradle'],
            ProgrammingLanguage.CSHARP: ['.csproj', '.sln', 'packages.config']
        }

        patterns = dependency_files.get(language, [])
        return any(pattern in file_path for pattern in patterns)

    async def _generate_test_files_for_existing_repo(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        existing_code_context: str,
        code_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate test files for existing repository."""
        files = []

        # Generate page object if needed
        if not code_analysis.get("page_objects"):
            page_object = await self._generate_page_object_for_requirement(
                requirement_name, page_elements, framework, language
            )
            if page_object:
                files.append(page_object)

        # Generate test class that integrates with existing structure
        test_class = await self._generate_integrated_test_class(
            requirement_name, test_cases, page_elements, framework, language,
            existing_code_context, code_analysis
        )
        if test_class:
            files.append(test_class)

        return files

    async def _generate_test_files_for_existing_repo_streaming(
        self,
        project_name: str,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        existing_code_context: str,
        code_analysis: Dict[str, Any]
    ):
        """Generate test files for existing repository with agentic intelligence and streaming."""

        # Get detailed context for incremental generation FIRST
        from app.services.code_embedding_service import code_embedding_service

        detailed_context = await code_embedding_service.get_detailed_code_context_for_incremental_generation(
            project_name=project_name,
            requirement_name=requirement_name,
            new_test_cases=test_cases,
            language=language
        )

        # 🧠 AGENTIC INTELLIGENCE: Skip for new file creation to avoid loops
        generation_strategy = detailed_context.get('generation_strategy', {})
        if generation_strategy.get('strategy') == 'create_new_file':
            logger.info("🚀 Creating new file - skipping comprehensive agentic analysis to avoid loops")
            yield {"type": "status", "message": "🚀 Creating new test file with intelligent naming..."}
            agentic_plan = None
        else:
            logger.info(f"🧠 Starting agentic intelligence for requirement: {requirement_name}")
            yield {"type": "status", "message": "🧠 Analyzing test cases with agentic intelligence..."}

            agentic_plan = None
            try:
                logger.info(f"🧠 Calling create_agentic_generation_plan with {len(test_cases)} test cases")

                # Check if the agentic method exists and is available
                if hasattr(code_embedding_service, 'create_agentic_generation_plan'):
                    agentic_plan = await code_embedding_service.create_agentic_generation_plan(
                        project_name=project_name,
                        requirement_name=requirement_name,
                        requirement_description=f"Test automation for {requirement_name}",
                        test_cases=test_cases,
                        page_elements=page_elements,
                        language=language
                    )

                    yield {
                        "type": "status",
                        "message": f"🎯 Agentic plan created with {agentic_plan.confidence_score:.1%} confidence"
                    }

                    logger.info(f"🧠 Agentic plan: {agentic_plan.generation_strategy} approach with {agentic_plan.confidence_score:.2f} confidence")
                else:
                    logger.warning("🧠 Agentic intelligence method not available, using fallback")

            except Exception as e:
                logger.error(f"❌ Agentic planning failed, using fallback: {e}")
                import traceback
                logger.error(f"❌ Traceback: {traceback.format_exc()}")
                agentic_plan = None

        # Enhance context with agentic insights
        if agentic_plan:
            detailed_context['agentic_plan'] = agentic_plan
            detailed_context['generation_strategy'] = agentic_plan.generation_strategy
            detailed_context['confidence_score'] = agentic_plan.confidence_score
            detailed_context['implementation_steps'] = agentic_plan.implementation_steps

        # Add project name to context for later use
        detailed_context['project_name'] = project_name

        yield {"type": "status", "message": "📊 Analyzing existing test structure with semantic understanding..."}

        # Create JSON-serializable version of detailed context for frontend
        serializable_context = {
            'project_structure': detailed_context.get('project_structure', {}),
            'existing_test_cases': detailed_context.get('existing_test_cases', []),
            'target_test_file': detailed_context.get('target_test_file', {}),
            'test_cases_to_implement': detailed_context.get('test_cases_to_implement', []),
            'all_test_cases': detailed_context.get('all_test_cases', []),
            'next_method_number': detailed_context.get('next_method_number', 1),
            'generation_strategy': detailed_context.get('generation_strategy', {}),
            'implementation_summary': detailed_context.get('implementation_summary', {})
        }

        yield {"type": "detailed_analysis", "data": serializable_context}

        # Generate page object if needed
        if not code_analysis.get("page_objects"):
            yield {"type": "status", "message": f"Generating page object for {requirement_name}..."}
            async for progress in self._generate_page_object_for_requirement_streaming(
                requirement_name, page_elements, framework, language
            ):
                yield progress

        # Generate test cases using detailed context for incremental generation
        yield {"type": "status", "message": f"Adding new test cases for {requirement_name}..."}
        async for progress in self._generate_incremental_test_cases_streaming(
            requirement_name, test_cases, page_elements, framework, language, detailed_context
        ):
            yield progress

    async def _generate_incremental_test_cases_streaming(
        self,
        requirement_name: str,
        new_test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        detailed_context: Dict[str, Any]
    ):
        """Generate test cases incrementally, adding only new ones to existing files."""

        if detailed_context.get('error'):
            # Fallback to original method if detailed context failed
            yield {"type": "status", "message": "Using fallback generation method..."}
            async for progress in self._generate_integrated_test_class_streaming(
                requirement_name, new_test_cases, page_elements, framework, language,
                detailed_context.get('fallback_context', ''), {}
            ):
                yield progress
            return

        generation_strategy = detailed_context.get('generation_strategy', {})
        target_file_info = detailed_context.get('target_test_file', {})
        existing_test_cases = detailed_context.get('existing_test_cases', [])

        # Format existing test cases for context
        existing_test_cases_text = self._format_existing_test_cases_for_prompt(existing_test_cases)
        new_test_cases_text = self._format_test_cases_for_prompt(new_test_cases)

        if generation_strategy.get('strategy') == 'create_new_file':
            # Create a completely new test file
            yield {"type": "status", "message": f"Creating new test file for {requirement_name}..."}
            async for progress in self._generate_new_test_file_streaming(
                requirement_name, new_test_cases, page_elements, framework, language, detailed_context
            ):
                yield progress

        elif generation_strategy.get('strategy') in ['append_to_existing_file', 'append_to_most_relevant_file']:
            # Add test cases to existing file by providing full file context
            target_file_path = target_file_info.get('file_path', '')
            existing_methods = target_file_info.get('existing_methods', [])
            full_file_content = target_file_info.get('file_content_preview', '')

            # Get the complete file content for proper context
            from app.services.git_service import git_service
            try:
                # Extract project name from the detailed context or use a fallback
                project_name_for_git = detailed_context.get('project_name', requirement_name.replace(' ', '_'))
                complete_file_content = git_service.read_file_content(
                    project_name_for_git, target_file_path
                )
                if complete_file_content:
                    full_file_content = complete_file_content
            except Exception as e:
                logger.warning(f"Could not read complete file content for {target_file_path}: {e}")

            yield {"type": "status", "message": f"Adding test cases to {target_file_path}..."}

            # Get unimplemented test cases with proper ID mapping
            unimplemented_test_cases = detailed_context.get('test_cases_to_implement', [])
            next_method_number = detailed_context.get('next_method_number', 1)

            if not unimplemented_test_cases:
                yield {"type": "status", "message": f"All test cases for '{requirement_name}' are already implemented!"}

                # Get match information for better user feedback
                match_reason = detailed_context.get('target_test_file', {}).get('match_reason', 'analysis')
                confidence = detailed_context.get('target_test_file', {}).get('confidence', 0.0)

                # Send a message to the code panel to inform the user
                no_code_message = f"""// ✅ All Test Cases Already Implemented for "{requirement_name}"!
//
// 🎯 Analysis Results:
// - Requirement: {requirement_name}
// - Target file: {target_file_path}
// - Match method: {match_reason}
// - Confidence: {confidence:.1%}
// - Total test cases for this requirement: {len(existing_test_cases)}
// - New test cases requested: {len(test_cases)}
//
// 📋 All {len(test_cases)} test cases for "{requirement_name}" are already
// implemented in the existing codebase. The system used semantic analysis
// to identify that the existing test cases cover the same functionality.
//
// 🚀 Next Steps:
// 1. Review the existing test implementation in {target_file_path}
// 2. Run the existing tests to verify functionality
// 3. Download the current codebase if needed
//
// 💡 If you believe this analysis is incorrect, try:
// - Using more specific requirement names
// - Adding more detailed test case descriptions
// - Creating a new requirement for different functionality
//
// 🎉 Your test automation is comprehensive and up to date!
"""

                yield {
                    "type": "code_chunk",
                    "data": {
                        "chunk": no_code_message,
                        "path": target_file_path,
                        "action": "clear_and_start",
                        "content": no_code_message
                    }
                }
                return

            # Create enhanced system prompt for incremental generation with full file context
            system_prompt = f"""You are a senior automation engineer. Your task is to add NEW test methods to an EXISTING test file.

{self.CODE_GENERATION_INSTRUCTIONS}

CRITICAL INSTRUCTIONS:
1. You are modifying an existing file: {target_file_path}
2. PRESERVE ALL existing code exactly as it is
3. ADD the new test methods to the existing class at the appropriate location
4. Follow the existing code style, imports, and patterns exactly
5. Use sequential method numbering starting from test{next_method_number}_
6. Each method should correspond to a specific test case ID

EXISTING TEST METHODS TO PRESERVE:
{chr(10).join(f"- {method}" for method in existing_methods)}

IMPLEMENTATION RULES:
- Method naming: Use sequential numbering starting from test{next_method_number}_MethodName()
- Each method should correspond to a specific test case ID
- Preserve all existing imports, class definitions, and methods
- Add new methods inside the existing test class
- Follow the exact same structure and patterns as existing methods

Your output should be the COMPLETE file with existing code preserved and new methods added.
"""

            # Create detailed prompt with test case ID mapping
            test_cases_with_ids = []
            for i, tc in enumerate(unimplemented_test_cases):
                method_number = next_method_number + i
                test_cases_with_ids.append(f"""
Test Case ID: {tc.get('custom_id', f'TC-{method_number:03d}')}
Method Name: test{method_number}_{self._sanitize_method_name(tc.get('title', 'UnknownTest'))}
Title: {tc.get('title', 'Unknown Test')}
Steps: {tc.get('steps', 'Not specified')}
Expected Result: {tc.get('expected_result', 'Not specified')}
""")

            prompt = f"""Add NEW test methods to the existing test file for requirement: {requirement_name}

TARGET FILE: {target_file_path}
FRAMEWORK: {framework.value}
LANGUAGE: {language.value}

COMPLETE EXISTING FILE CONTENT:
{full_file_content}

TEST CASES TO IMPLEMENT (with proper ID mapping):
{chr(10).join(test_cases_with_ids)}

PAGE ELEMENTS AVAILABLE:
{self._format_page_elements_for_prompt(page_elements)}

INSTRUCTIONS:
1. Output the COMPLETE file with all existing code preserved
2. Add the new test methods inside the existing test class
3. Use the specified method names and test case IDs
4. Follow the exact same coding patterns as existing methods
5. Ensure proper indentation and formatting
6. Do not modify any existing methods or imports

Generate the complete updated file content:"""

            # Generate the complete file with new methods appended
            async for progress in self._generate_file_with_streaming(
                prompt, system_prompt, target_file_path, "complete_test_file",
                f"Updated test file with new methods for {requirement_name}"
            ):
                # Modify progress to indicate this is file replacement with appending
                if progress.get("type") == "code_chunk":
                    progress["data"]["is_incremental"] = True
                    progress["data"]["target_file"] = target_file_path
                    progress["data"]["existing_methods"] = existing_methods
                    progress["data"]["append_mode"] = True
                elif progress.get("type") == "file_complete":
                    progress["data"]["is_incremental"] = True
                    progress["data"]["target_file"] = target_file_path
                    progress["data"]["generation_type"] = "complete_file_with_new_methods"
                    progress["data"]["append_mode"] = True

                yield progress

    def _format_existing_test_cases_for_prompt(self, existing_test_cases: List[Dict[str, Any]]) -> str:
        """Format existing test cases for LLM prompt context."""
        if not existing_test_cases:
            return "No existing test cases found."

        formatted_cases = []
        for i, test_case in enumerate(existing_test_cases, 1):
            case_text = f"{i}. Method: {test_case.get('method_name', 'Unknown')}"
            case_text += f"\n   File: {test_case.get('file_path', 'Unknown')}"
            case_text += f"\n   Title: {test_case.get('title', 'Unknown')}"

            steps = test_case.get('estimated_steps', [])
            if steps:
                case_text += f"\n   Steps: {'; '.join(steps[:3])}"  # Limit to first 3 steps

            formatted_cases.append(case_text)

        return "\n\n".join(formatted_cases)

    async def _generate_new_test_file_streaming(
        self,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        detailed_context: Dict[str, Any]
    ):
        """Generate a completely new test file when no existing files are suitable."""

        test_cases_text = self._format_test_cases_for_prompt(test_cases)
        page_elements_text = self._format_page_elements_for_prompt(page_elements)

        # Use existing project structure context
        project_structure = detailed_context.get('project_structure', {})
        config_files = detailed_context.get('config_files', [])

        system_prompt = f"""You are a senior automation engineer. Create a new test file for a requirement in an existing project.

IMPORTANT: This is for an EXISTING project with the following structure:
- Test files: {project_structure.get('test_files_count', 0)}
- Page objects: {project_structure.get('page_object_files_count', 0)}
- Config files: {project_structure.get('config_files_count', 0)}

Follow the existing project patterns and conventions.
Use the same imports, base classes, and testing patterns as other files in the project.

The test file should:
1. Follow existing naming conventions
2. Use the same base classes and imports as other test files
3. Implement all provided test cases as separate test methods
4. Include proper setup and teardown methods
5. Follow the existing code style and patterns

Output only the complete test file code without any explanations or markdown formatting."""

        filename = f"test_{requirement_name.lower().replace(' ', '_')}.py"

        prompt = f"""Create a new test file for requirement: {requirement_name}

FRAMEWORK: {framework.value}
LANGUAGE: {language.value}
FILENAME: {filename}

EXISTING PROJECT CONFIG FILES CONTEXT:
{chr(10).join([f"File: {cf.get('path', '')}" for cf in config_files[:2]])}

TEST CASES TO IMPLEMENT:
{test_cases_text}

PAGE ELEMENTS AVAILABLE:
{page_elements_text}

Generate a complete test file that integrates well with the existing project structure."""

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "new_test_file",
            f"New test file for {requirement_name}"
        ):
            yield progress

    async def _generate_integrated_test_class(
        self,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        existing_code_context: str,
        code_analysis: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Generate test class that integrates with existing codebase."""

        test_cases_text = self._format_test_cases_for_prompt(test_cases)
        relevant_elements = self._filter_relevant_elements(page_elements)
        elements_json = json.dumps(relevant_elements, indent=2)

        system_prompt = f"""You are an expert automation engineer. Generate a test class that integrates with an existing {framework.value} automation project using {language.value}.

        The test class should:
        - Follow the existing project structure and patterns
        - Use existing base classes and utilities
        - Implement all provided test cases as separate test methods
        - Follow the existing naming conventions
        - Include proper assertions and validations
        - Follow testing best practices from the existing codebase

        Use the provided context about existing code to ensure consistency.
        Output only the code without any explanations or markdown formatting."""

        prompt = f"""Generate a test class for requirement: {requirement_name}

        Existing code context:
        {existing_code_context[:2000]}  # Limit context size

        Code analysis:
        - Existing test files: {code_analysis.get('test_files', [])}
        - Existing page objects: {code_analysis.get('page_objects', [])}
        - Config files: {code_analysis.get('config_files', [])}

        Test cases to implement:
        {test_cases_text}

        Page elements available:
        {elements_json}

        Create a test class that follows existing patterns and integrates seamlessly."""

        content = await llm_service._make_request(prompt, system_prompt)
        if content:
            # Sanitize the generated code
            sanitized_content = self._sanitize_generated_code(content)
            class_name = self._sanitize_class_name(requirement_name)

            if language == ProgrammingLanguage.PYTHON:
                filename = f"tests/test_{class_name.lower()}.py"
            elif language == ProgrammingLanguage.JAVASCRIPT:
                filename = f"tests/{class_name}Test.js"
            elif language == ProgrammingLanguage.JAVA:
                filename = f"src/test/java/{class_name}Test.java"
            elif language == ProgrammingLanguage.CSHARP:
                filename = f"Tests/{class_name}Test.cs"
            else:
                return None

            return {
                "path": filename,
                "content": sanitized_content,
                "type": "test_class",
                "description": f"Integrated test class for {requirement_name}"
            }

        return None

    async def _generate_integrated_test_class_streaming(
        self,
        requirement_name: str,
        test_cases: List[TestCase],
        page_elements: Dict[str, Any],
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        existing_code_context: str,
        code_analysis: Dict[str, Any]
    ):
        """Generate test class that integrates with existing codebase with streaming."""

        test_cases_text = self._format_test_cases_for_prompt(test_cases)
        relevant_elements = self._filter_relevant_elements(page_elements)
        elements_json = json.dumps(relevant_elements, indent=2)

        system_prompt = f"""You are an expert automation engineer. Generate a test class that integrates with an existing {framework.value} automation project using {language.value}.

        The test class should:
        - Follow the existing project structure and patterns
        - Use existing base classes and utilities
        - Implement all provided test cases as separate test methods
        - Follow the existing naming conventions
        - Include proper assertions and validations
        - Follow testing best practices from the existing codebase

        Use the provided context about existing code to ensure consistency.
        Output only the code without any explanations or markdown formatting."""

        prompt = f"""Generate a test class for requirement: {requirement_name}

        Existing code context:
        {existing_code_context[:2000]}  # Limit context size

        Code analysis:
        - Existing test files: {code_analysis.get('test_files', [])}
        - Existing page objects: {code_analysis.get('page_objects', [])}
        - Config files: {code_analysis.get('config_files', [])}

        Test cases to implement:
        {test_cases_text}

        Page elements available:
        {elements_json}

        Create a test class that follows existing patterns and integrates seamlessly."""

        class_name = self._sanitize_class_name(requirement_name)

        if language == ProgrammingLanguage.PYTHON:
            filename = f"tests/test_{class_name.lower()}.py"
        elif language == ProgrammingLanguage.JAVASCRIPT:
            filename = f"tests/{class_name}Test.js"
        elif language == ProgrammingLanguage.JAVA:
            filename = f"src/test/java/{class_name}Test.java"
        elif language == ProgrammingLanguage.CSHARP:
            filename = f"Tests/{class_name}Test.cs"
        else:
            return

        async for progress in self._generate_file_with_streaming(
            prompt, system_prompt, filename, "test_class", f"Integrated test class for {requirement_name}"
        ):
            yield progress

    async def _update_config_files_for_existing_repo(
        self,
        project_name: str,
        framework: AutomationFramework,
        language: ProgrammingLanguage,
        code_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Update configuration files for existing repository if needed."""
        files = []

        # Check if we need to update any configuration files
        # This is a placeholder for future enhancements

        return files

    # Code review suggestions method removed for simplified workflow

    # Helper method for code review removed for simplified workflow

# Global instance
code_generation_service = CodeGenerationService()
