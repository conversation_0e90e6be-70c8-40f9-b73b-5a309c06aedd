import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from app.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending emails."""
    
    def __init__(self):
        self.smtp_server = getattr(settings, 'SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = getattr(settings, 'SMTP_PORT', 587)
        self.smtp_username = getattr(settings, 'SMTP_USERNAME', '')
        self.smtp_password = getattr(settings, 'SMTP_PASSWORD', '')
        self.from_email = getattr(settings, 'FROM_EMAIL', self.smtp_username)
        self.from_name = getattr(settings, 'FROM_NAME', 'IntelliTest')
    
    def _create_smtp_connection(self):
        """Create and return SMTP connection."""
        if not self.smtp_username or not self.smtp_password:
            raise ValueError("SMTP credentials not configured")
        
        server = smtplib.SMTP(self.smtp_server, self.smtp_port)
        server.starttls()
        server.login(self.smtp_username, self.smtp_password)
        return server
    
    def send_email(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None):
        """Send an email."""
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email
            
            # Add text version if provided
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)
            
            # Add HTML version
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            with self._create_smtp_connection() as server:
                server.send_message(msg)

            logger.debug(f"Email sent successfully to {to_email}")

        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            raise

def send_password_reset_email(email: str, name: str, reset_token: str):
    """Send password reset email."""
    email_service = EmailService()
    
    # Create reset URL (you'll need to update this with your frontend URL)
    frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
    reset_url = f"{frontend_url}/reset-password?token={reset_token}"
    
    subject = "Reset your IntelliTest password"
    
    # HTML email template
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
            body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }}
            .container {{ max-width: 600px; margin: 0 auto; background-color: white; }}
            .header {{ background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); padding: 40px 20px; text-align: center; }}
            .header h1 {{ color: white; margin: 0; font-size: 24px; font-weight: 600; }}
            .content {{ padding: 40px 20px; }}
            .content h2 {{ color: #1f2937; margin: 0 0 20px 0; font-size: 20px; }}
            .content p {{ color: #6b7280; line-height: 1.6; margin: 0 0 20px 0; }}
            .button {{ display: inline-block; background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; margin: 20px 0; }}
            .button:hover {{ background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%); }}
            .footer {{ background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }}
            .warning {{ background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 20px 0; }}
            .warning p {{ color: #92400e; margin: 0; font-size: 14px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 IntelliTest</h1>
            </div>
            <div class="content">
                <h2>Reset your password</h2>
                <p>Hi {name},</p>
                <p>We received a request to reset your password for your IntelliTest account. Click the button below to create a new password:</p>
                
                <a href="{reset_url}" class="button" style="color: #fff;">Reset Password</a>
                
                <div class="warning">
                    <p><strong>⚠️ Important:</strong> This link will expire in 1 hour for security reasons.</p>
                </div>
                
                <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>
                
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #8b5cf6;">{reset_url}</p>
            </div>
            <div class="footer">
                <p>© 2025 IntelliTest. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Text version for email clients that don't support HTML
    text_content = f"""
    Reset your IntelliTest password
    
    Hi {name},
    
    We received a request to reset your password for your IntelliTest account.
    
    Click this link to reset your password: {reset_url}
    
    This link will expire in 1 hour for security reasons.
    
    If you didn't request a password reset, you can safely ignore this email.
    
    © 2025 IntelliTest. All rights reserved.
    """
    
    email_service.send_email(email, subject, html_content, text_content)
