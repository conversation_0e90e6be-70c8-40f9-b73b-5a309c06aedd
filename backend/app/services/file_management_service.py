import os
import shutil
import zipfile
import tempfile
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging
import aiofiles
from app.services.git_service import git_service

logger = logging.getLogger(__name__)

class FileManagementService:
    """Service for managing files and creating zip archives for code generation."""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "intellitest_temp"
        self.temp_dir.mkdir(exist_ok=True)
    
    async def save_generated_files(
        self,
        project_name: str,
        generated_files: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Save generated files to the repository.
        
        Args:
            project_name: Name of the project
            generated_files: List of generated file information
            
        Returns:
            Dictionary with save results
        """
        try:
            saved_files = []
            failed_files = []
            
            for file_info in generated_files:
                try:
                    file_path = file_info.get('path')
                    content = file_info.get('content')
                    
                    if not file_path or not content:
                        failed_files.append({
                            "path": file_path or "unknown",
                            "error": "Missing file path or content"
                        })
                        continue
                    
                    # Save file to repository
                    success = git_service.write_file_content(project_name, file_path, content)
                    
                    if success:
                        saved_files.append({
                            "path": file_path,
                            "type": file_info.get('type', 'unknown'),
                            "description": file_info.get('description', ''),
                            "size": len(content)
                        })
                    else:
                        failed_files.append({
                            "path": file_path,
                            "error": "Failed to write file"
                        })
                        
                except Exception as e:
                    failed_files.append({
                        "path": file_info.get('path', 'unknown'),
                        "error": str(e)
                    })
            
            result = {
                "success": len(failed_files) == 0,
                "saved_files": saved_files,
                "failed_files": failed_files,
                "total_files": len(generated_files)
            }
            
            logger.info(f"Saved {len(saved_files)} files, {len(failed_files)} failed for project {project_name}")
            return result
            
        except Exception as e:
            error_msg = f"Error saving generated files: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "saved_files": [],
                "failed_files": [],
                "total_files": len(generated_files)
            }
    
    async def create_project_zip(
        self,
        project_name: str,
        include_git: bool = False
    ) -> Optional[str]:
        """
        Create a zip file of the project repository.
        
        Args:
            project_name: Name of the project
            include_git: Whether to include .git directory
            
        Returns:
            Path to the created zip file, or None if failed
        """
        try:
            repo_path = git_service.get_repo_path(project_name)
            
            if not repo_path.exists():
                logger.error(f"Repository path does not exist: {repo_path}")
                return None
            
            # Create temporary zip file
            zip_filename = f"{project_name}_generated_code.zip"
            zip_path = self.temp_dir / zip_filename
            
            # Remove existing zip if it exists
            if zip_path.exists():
                zip_path.unlink()
            
            # Create zip file
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(repo_path):
                    # Skip .git directory unless explicitly included
                    if not include_git and '.git' in dirs:
                        dirs.remove('.git')
                    
                    # Skip other unwanted directories
                    dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.github']
                    
                    for file in files:
                        # Skip unwanted files
                        if self._should_skip_file(file):
                            continue
                        
                        file_path = Path(root) / file
                        arcname = file_path.relative_to(repo_path)
                        
                        try:
                            zipf.write(file_path, arcname)
                        except Exception as e:
                            logger.warning(f"Could not add file {file_path} to zip: {e}")
            
            logger.info(f"Created zip file: {zip_path}")
            return str(zip_path)
            
        except Exception as e:
            logger.error(f"Error creating project zip: {e}")
            return None
    
    def _should_skip_file(self, filename: str) -> bool:
        """Check if a file should be skipped when creating zip."""
        skip_patterns = [
            '.DS_Store', 'Thumbs.db', '*.tmp', '*.log',
            '*.pyc', '*.pyo', '*.pyd', '__pycache__',
            '.pytest_cache', '.coverage', '*.egg-info'
        ]
        
        filename_lower = filename.lower()
        
        for pattern in skip_patterns:
            if pattern.startswith('*'):
                if filename_lower.endswith(pattern[1:]):
                    return True
            elif pattern in filename_lower:
                return True
        
        return False
    
    async def get_project_structure(self, project_name: str) -> Dict[str, Any]:
        """
        Get the structure of a project repository.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Dictionary representing the project structure
        """
        try:
            repo_path = git_service.get_repo_path(project_name)
            
            if not repo_path.exists():
                return {"error": "Repository path does not exist"}
            
            structure = self._build_directory_tree(repo_path, repo_path)
            
            return {
                "success": True,
                "project_name": project_name,
                "structure": structure
            }
            
        except Exception as e:
            logger.error(f"Error getting project structure: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_directory_tree(self, path: Path, root_path: Path, max_depth: int = 5, current_depth: int = 0) -> Dict[str, Any]:
        """Build a directory tree structure."""
        
        if current_depth >= max_depth:
            return {"type": "directory", "name": path.name, "truncated": True}
        
        try:
            if path.is_file():
                return {
                    "type": "file",
                    "name": path.name,
                    "size": path.stat().st_size,
                    "relative_path": str(path.relative_to(root_path))
                }
            
            elif path.is_dir():
                children = []
                
                # Skip certain directories
                if path.name.startswith('.') and path.name not in ['.github']:
                    return {
                        "type": "directory",
                        "name": path.name,
                        "hidden": True
                    }
                
                try:
                    for child in sorted(path.iterdir()):
                        if child.name.startswith('.') and child.name not in ['.github', '.gitignore']:
                            continue
                        
                        child_structure = self._build_directory_tree(
                            child, root_path, max_depth, current_depth + 1
                        )
                        children.append(child_structure)
                
                except PermissionError:
                    return {
                        "type": "directory",
                        "name": path.name,
                        "error": "Permission denied"
                    }
                
                return {
                    "type": "directory",
                    "name": path.name,
                    "children": children,
                    "relative_path": str(path.relative_to(root_path)) if path != root_path else ""
                }
        
        except Exception as e:
            return {
                "type": "unknown",
                "name": path.name,
                "error": str(e)
            }
    
    async def cleanup_temp_files(self, older_than_hours: int = 24) -> Dict[str, Any]:
        """
        Clean up temporary files older than specified hours.
        
        Args:
            older_than_hours: Remove files older than this many hours
            
        Returns:
            Dictionary with cleanup results
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (older_than_hours * 3600)
            
            removed_files = []
            failed_removals = []
            
            if self.temp_dir.exists():
                for file_path in self.temp_dir.iterdir():
                    try:
                        if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                            file_path.unlink()
                            removed_files.append(str(file_path))
                    except Exception as e:
                        failed_removals.append({
                            "file": str(file_path),
                            "error": str(e)
                        })
            
            result = {
                "success": True,
                "removed_files": len(removed_files),
                "failed_removals": len(failed_removals),
                "details": {
                    "removed": removed_files,
                    "failed": failed_removals
                }
            }
            
            logger.info(f"Cleanup completed: {len(removed_files)} files removed, {len(failed_removals)} failed")
            return result
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return {
                "success": False,
                "error": str(e),
                "removed_files": 0,
                "failed_removals": 0
            }
    
    async def get_file_content(self, project_name: str, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get the content of a specific file in the repository.
        
        Args:
            project_name: Name of the project
            file_path: Relative path to the file
            
        Returns:
            Dictionary with file content and metadata
        """
        try:
            content = git_service.read_file_content(project_name, file_path)
            
            if content is None:
                return None
            
            repo_path = git_service.get_repo_path(project_name)
            full_path = repo_path / file_path
            
            if full_path.exists():
                stat = full_path.stat()
                
                return {
                    "path": file_path,
                    "content": content,
                    "size": len(content),
                    "file_size": stat.st_size,
                    "modified": stat.st_mtime,
                    "lines": len(content.split('\n'))
                }
            
            return {
                "path": file_path,
                "content": content,
                "size": len(content),
                "lines": len(content.split('\n'))
            }
            
        except Exception as e:
            logger.error(f"Error getting file content: {e}")
            return None
    
    async def validate_generated_files(self, generated_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate generated files before saving.
        
        Args:
            generated_files: List of generated file information
            
        Returns:
            Dictionary with validation results
        """
        try:
            valid_files = []
            invalid_files = []
            
            for file_info in generated_files:
                validation_result = self._validate_single_file(file_info)
                
                if validation_result["valid"]:
                    valid_files.append(file_info)
                else:
                    invalid_files.append({
                        "file_info": file_info,
                        "errors": validation_result["errors"]
                    })
            
            return {
                "success": len(invalid_files) == 0,
                "valid_files": len(valid_files),
                "invalid_files": len(invalid_files),
                "total_files": len(generated_files),
                "details": {
                    "valid": valid_files,
                    "invalid": invalid_files
                }
            }
            
        except Exception as e:
            logger.error(f"Error validating generated files: {e}")
            return {
                "success": False,
                "error": str(e),
                "valid_files": 0,
                "invalid_files": 0,
                "total_files": len(generated_files)
            }
    
    def _validate_single_file(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a single generated file."""
        errors = []
        
        # Check required fields
        if not file_info.get('path'):
            errors.append("Missing file path")
        
        if not file_info.get('content'):
            errors.append("Missing file content")
        
        # Check file path validity
        path = file_info.get('path', '')
        if path:
            if '..' in path or path.startswith('/'):
                errors.append("Invalid file path (security risk)")
            
            if len(path) > 255:
                errors.append("File path too long")
        
        # Check content size
        content = file_info.get('content', '')
        if len(content) > 10 * 1024 * 1024:  # 10MB limit
            errors.append("File content too large (>10MB)")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def get_temp_file_path(self, filename: str) -> str:
        """Get a temporary file path."""
        return str(self.temp_dir / filename)
    
    async def delete_temp_file(self, file_path: str) -> bool:
        """Delete a temporary file."""
        try:
            path = Path(file_path)
            if path.exists() and path.parent == self.temp_dir:
                path.unlink()
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting temp file {file_path}: {e}")
            return False

# Global instance
file_management_service = FileManagementService()
