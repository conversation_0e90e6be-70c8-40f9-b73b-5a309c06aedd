import httpx
import json
import logging
from typing import List, Dict, Any, Optional, Union, AsyncGenerator
from app.core.config import settings
from app.schemas.requirement import TestCaseCreate

logger = logging.getLogger(__name__)

class OllamaService:
    def __init__(self):
        self.base_url = settings.OLLAMA_BASE_URL
        self.model = settings.OLLAMA_MODEL
    
    async def _make_request(self, prompt: str, system_prompt: str = None) -> Optional[str]:
        """Make a request to Ollama API."""
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                payload = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False
                }

                if system_prompt:
                    payload["system"] = system_prompt

                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=payload
                )
                response.raise_for_status()

                result = response.json()
                return result.get("response", "").strip()

        except httpx.RequestError as e:
            print(f"Request error: {e}")
            return None
        except httpx.HTTPStatusError as e:
            print(f"HTTP error: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None

    async def _make_streaming_request(self, prompt: str, system_prompt: str = None):
        """Make a streaming request to Ollama API."""
        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                payload = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": True
                }

                if system_prompt:
                    payload["system"] = system_prompt

                async with client.stream(
                    "POST",
                    f"{self.base_url}/api/generate",
                    json=payload
                ) as response:
                    response.raise_for_status()

                    async for line in response.aiter_lines():
                        if line.strip():
                            try:
                                data = json.loads(line)
                                if "response" in data:
                                    yield data["response"]
                                if data.get("done", False):
                                    break
                            except json.JSONDecodeError:
                                continue

        except httpx.RequestError as e:
            print(f"Streaming request error: {e}")
            return
        except httpx.HTTPStatusError as e:
            print(f"Streaming HTTP error: {e}")
            return
        except Exception as e:
            print(f"Streaming unexpected error: {e}")
            return
    
    async def refine_requirement(self, requirement_text: str, context: str = "", rag_context: str = "", conflict_context: str = "") -> Optional[str]:
        """Refine a requirement using LLM."""
        system_prompt = """You are a senior business analyst and expert requirements engineer. Your task is to refine and enhance software requirements for clarity, precision, and completeness.

        Follow these guidelines strictly:
        - Transform vague or ambiguous statements into specific, actionable, and testable requirements
        - Use clear, concise, and professional language
        - Eliminate all forms of ambiguity, redundancy, or subjectivity
        - Include measurable or binary acceptance criteria whenever applicable
        - Identify and incorporate relevant edge cases and error scenarios
        - Maintain a consistent format and structure for all refined requirements
        - Do not include explanatory notes, justifications, or any leading/trailing sentences outside the requirement itself
        - IMPORTANT: If there are potentially conflicting requirements provided, identify them and add a "Special Attention - Conflicting Requirements" section
        - CRITICAL: Do not include unrealistic accuracy percentages or performance claims (like "90% accuracy", "99% uptime") unless they are explicitly mentioned in the original requirement and are technically feasible
        - Focus on functional behavior and user experience rather than arbitrary performance metrics
        - When specifying validation criteria, use realistic and testable conditions (e.g., "system validates email format" instead of "system validates email with 95% accuracy")

        Your output should be a clean list of well-defined software requirements ready for implementation or validation. Do not generate Markdown or rich text formatting. Output should be plain text only. Use hyphens (-) for bullet points and ensure clear line breaks between sections.

        If conflicts are detected, add a section at the end with:
        "Special Attention - Conflicting Requirements:
        - [Describe the conflict and reference the conflicting requirement name in brackets]"
        """
        
        context_section = f"Additional Context: {context}" if context else ""
        rag_section = f"Similar Requirements for Reference:\n{rag_context}" if rag_context else ""
        conflict_section = f"Potentially Conflicting Requirements:\n{conflict_context}" if conflict_context else ""

        prompt = f"""Please refine the following software requirement:

        Original Requirement:
        {requirement_text}

        {context_section}

        {rag_section}

        {conflict_section}

        Please provide a refined version that is:
        1. Clear and unambiguous
        2. Specific and measurable with realistic criteria
        3. Complete with acceptance criteria that can be practically tested
        4. Free from arbitrary accuracy percentages or unrealistic performance claims
        5. Focused on functional behavior and user experience
        6. If conflicts exist, include a "Special Attention - Conflicting Requirements" section

        IMPORTANT: Avoid including specific accuracy percentages (like "90% accuracy") unless they are explicitly mentioned in the original requirement and are technically justified. Instead, focus on clear functional requirements and binary pass/fail criteria.

        Refined Requirement:"""

        return await self._make_request(prompt, system_prompt)
    
    async def generate_test_cases(self, requirement_text: str, context: str = "", rag_context: str = "", existing_test_cases: List[Any] = None) -> List[TestCaseCreate]:
        """Generate test cases for a requirement using LLM."""
        system_prompt = """You are an expert Senior QA engineer and test case designer. Your task is to generate high-quality, comprehensive test cases based on provided software requirements.

        Guidelines:
        - Create both positive and negative test cases
        - Edge cases: Include extreme or limit values (e.g., minimum, maximum, empty, null, special characters).
        - Boundary conditions: Test just below, on, and just above the acceptable limits.
        - Consider different user scenarios and permissions
        - Make test cases specific and actionable
        - Include clear expected results

        Formatting:
        - Output must be a JSON array.
        - Each test case must include the following fields:
            {
            "id": "TC-001", // Unique identifier (will be auto-generated, don't include in title)
            "title": "Descriptive test case title without any identifiers",
            "testSteps": "Clearly defined steps to perform the test",
            "expectedResult": "What should happen after the steps are executed",
            "notes": "Any assumptions, dependencies, or relevant context"
            }

        IMPORTANT TITLE FORMATTING RULES:
        - DO NOT include any identifiers like "Test Case 1", "TC-001", "Test 1", etc. in the title
        - Use descriptive, action-oriented titles like "Login with valid credentials", "Submit form with empty required fields"
        - Keep titles concise but descriptive (maximum 80 characters)
        - Use sentence case (first word capitalized, rest lowercase unless proper nouns)

        Additional Guidelines:
        - Use clear, professional, and concise language.
        - Ensure each test case is independent and easy to understand.
        - Include enough detail so the test can be executed without further clarification.
        - Avoid redundancy but ensure complete coverage.

        Do not include any markdown formatting, explanations, or surrounding text — only return the JSON array of test cases.
        """
        
        context_section = f"Additional Context: {context}" if context else ""
        rag_section = f"Similar Requirements for Reference:\n{rag_context}" if rag_context else ""

        # Build existing test cases context
        existing_cases_section = ""
        if existing_test_cases and len(existing_test_cases) > 0:
            existing_cases_list = []
            for tc in existing_test_cases:
                case_info = f"- {tc.custom_id}: {tc.title}"
                if tc.steps:
                    case_info += f" (Steps: {tc.steps[:100]}...)" if len(tc.steps) > 100 else f" (Steps: {tc.steps})"
                existing_cases_list.append(case_info)

            existing_cases_section = f"""
IMPORTANT - Existing Test Cases for this Requirement:
{chr(10).join(existing_cases_list)}

DO NOT generate test cases that are similar or duplicate to the existing ones above.
Generate ONLY NEW and UNIQUE test cases that cover different scenarios not already covered.
"""

        prompt = f"""Generate comprehensive test cases for the following requirement:

        Requirement:
        {requirement_text}

        {context_section}

        {rag_section}

        {existing_cases_section}

        Please generate NEW test cases covering scenarios NOT already covered by existing test cases:
        1. Happy path scenarios (if not covered)
        2. Edge cases (if not covered)
        3. Error conditions (if not covered)
        4. Boundary conditions (if not covered)
        5. Different user roles/permissions (if applicable and not covered)

        Format your response as a JSON array with objects containing:
        - Title: A descriptive title WITHOUT any identifiers (no "Test Case 1", "TC-001", etc.)
        - testSteps: Clearly defined steps to perform the test
        - expectedResult: Expected outcome
        - notes: Any assumptions, dependencies, or relevant context

        TITLE FORMATTING RULES:
        - Use descriptive, action-oriented titles like "Login with valid credentials", "Submit form with missing email"
        - NO identifiers or numbers in titles (no "Test Case 1", "TC-001", "Test 1", etc.)
        - Keep titles concise but descriptive (maximum 80 characters)
        - Use sentence case (first word capitalized, rest lowercase unless proper nouns)

        IMPORTANT: Do NOT include an "ID" field - IDs will be auto-generated.
        IMPORTANT: Only generate test cases that are genuinely different from existing ones.

        JSON Response:"""

        response = await self._make_request(prompt, system_prompt)
        if not response:
            return []
        
        try:
            # Try to extract JSON from the response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1

            if json_start == -1 or json_end == 0:
                print("No valid JSON array found in response")
                return []

            json_str = response[json_start:json_end]

            # Validate that we have a complete JSON structure
            if not json_str.strip():
                print("Empty JSON string extracted")
                return []

            test_cases_data = json.loads(json_str)

            # Ensure we have a list
            if not isinstance(test_cases_data, list):
                print("Response is not a JSON array")
                return []

            # Ensure we have at least one test case
            if len(test_cases_data) == 0:
                print("No test cases found in response")
                return []
                
                test_cases = []
                for i, tc_data in enumerate(test_cases_data):
                    if not isinstance(tc_data, dict):
                        print(f"Test case {i+1} is not a dictionary, skipping")
                        continue

                    # Handle different field name variations from LLM response
                    title = tc_data.get('title', '').strip()

                    # Skip test cases without a proper title
                    if not title or title.lower() in ['generate test case', 'generated test case', 'test case']:
                        print(f"Test case {i+1} has invalid or generic title, skipping")
                        continue

                    # Handle testSteps field (from LLM format)
                    steps = tc_data.get('steps') or tc_data.get('testSteps')
                    if steps is not None:
                        if isinstance(steps, list):
                            steps = '\n'.join(str(step) for step in steps)
                        elif isinstance(steps, dict):
                            # If steps is a dict, it might be malformed JSON
                            print(f"Test case {i+1} has malformed steps (dict), skipping")
                            continue
                        else:
                            steps = str(steps).strip()
                    else:
                        steps = "No specific steps provided"

                    # Skip test cases with obviously malformed or incomplete steps
                    if not steps or len(steps.strip()) < 10:
                        print(f"Test case {i+1} has insufficient steps, skipping")
                        continue

                    # Handle expectedResult field (from LLM format)
                    expected_result = tc_data.get('expected_result') or tc_data.get('expectedResult')
                    if expected_result is not None:
                        if isinstance(expected_result, dict):
                            # If expected_result is a dict, it might be malformed JSON
                            print(f"Test case {i+1} has malformed expected_result (dict), using default")
                            expected_result = "Verify the requirement is met"
                        elif isinstance(expected_result, list):
                            expected_result = '\n'.join(str(item) for item in expected_result)
                        else:
                            expected_result = str(expected_result).strip()
                    else:
                        expected_result = "Verify the requirement is met"

                    # Skip test cases with insufficient expected results
                    if not expected_result or len(expected_result.strip()) < 5:
                        expected_result = "Verify the requirement is met"

                    # Handle notes field
                    notes = tc_data.get('notes', '')
                    if isinstance(notes, list):
                        notes = '\n'.join(str(note) for note in notes)
                    elif isinstance(notes, dict):
                        notes = str(notes)
                    else:
                        notes = str(notes) if notes else ""

                    # Create test case - we've already validated title and steps above
                    test_case = TestCaseCreate(
                        custom_id=None,  # Will be auto-generated
                        title=title,
                        steps=steps,
                        expected_result=expected_result,
                        notes=notes
                    )
                    test_cases.append(test_case)
                    print(f"Successfully parsed test case: {title}")
                
                return test_cases
            
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            print(f"Raw response: {response}")
            # Return empty list to indicate failure - let the API handle the error
            return []
        
        return []

    # ==================== AGENTIC INTELLIGENCE METHODS ====================

    async def analyze_code_semantics(
        self,
        code_content: str,
        file_path: str,
        analysis_type: str = "comprehensive"
    ) -> Dict[str, Any]:
        """Analyze code semantics for agentic understanding."""
        try:
            prompt = f"""Analyze this code file for semantic understanding and intelligent code generation:

FILE: {file_path}
ANALYSIS TYPE: {analysis_type}

CODE:
```
{code_content[:2000]}  # Limit for context
```

Provide comprehensive analysis in JSON format:
{{
    "functional_purpose": "string - what this code does",
    "complexity_level": "string - low/medium/high",
    "key_entities": ["list of main functions/classes/methods"],
    "dependencies": ["list of imports/dependencies"],
    "patterns": ["list of code patterns used"],
    "test_coverage_areas": ["list of areas that need testing"],
    "integration_points": ["list of external integrations"],
    "semantic_tags": ["list of semantic tags for categorization"],
    "improvement_suggestions": ["list of potential improvements"],
    "risk_factors": ["list of potential risks or issues"]
}}"""

            system_prompt = "You are an expert code analyst and software architect specializing in semantic code understanding."

            response = await self._make_request(prompt, system_prompt)

            if response:
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse semantic analysis response for {file_path}")
                    return self._create_fallback_analysis(file_path)

            return self._create_fallback_analysis(file_path)

        except Exception as e:
            logger.error(f"Failed to analyze code semantics for {file_path}: {e}")
            return self._create_fallback_analysis(file_path)

    def _create_fallback_analysis(self, file_path: str) -> Dict[str, Any]:
        """Create fallback analysis when LLM analysis fails."""
        return {
            "functional_purpose": f"Code file: {file_path}",
            "complexity_level": "medium",
            "key_entities": [],
            "dependencies": [],
            "patterns": [],
            "test_coverage_areas": [],
            "integration_points": [],
            "semantic_tags": ["unknown"],
            "improvement_suggestions": [],
            "risk_factors": ["analysis_failed"]
        }

# Global instance
llm_service = OllamaService()
