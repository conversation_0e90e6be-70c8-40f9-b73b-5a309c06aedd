"""
World-class web crawler service for extracting DOM elements with their selectors and text content.
Supports authentication and provides comprehensive element data extraction.
"""

import asyncio
import logging
import random
import time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from urllib.parse import urlparse, urljoin
import json

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from playwright._impl._errors import TimeoutError as PlaywrightTimeoutError

logger = logging.getLogger(__name__)


# Comprehensive User-Agent rotation lists for different browsers and devices
USER_AGENTS = {
    'chrome_windows': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ],
    'chrome_mac': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ],
    'firefox_windows': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
        'Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    ],
    'firefox_mac': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/20100101 Firefox/119.0',
    ],
    'safari_mac': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
    ],
    'edge_windows': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    ],
    'mobile_android': [
        'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 12; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    ],
    'mobile_ios': [
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
    ]
}

# Browser fingerprint data for randomization
BROWSER_FINGERPRINTS = {
    'chrome': {
        'webgl_vendor': ['Google Inc. (NVIDIA)', 'Google Inc. (Intel)', 'Google Inc. (AMD)'],
        'webgl_renderer': ['ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)'],
        'platform': ['Win32', 'MacIntel', 'Linux x86_64'],
        'languages': [['en-US', 'en'], ['en-GB', 'en'], ['en-CA', 'en', 'fr']],
        'timezone': ['America/New_York', 'America/Los_Angeles', 'Europe/London', 'America/Chicago']
    },
    'firefox': {
        'webgl_vendor': ['Mozilla', 'Intel Open Source Technology Center'],
        'webgl_renderer': ['Mesa DRI Intel(R) HD Graphics 620', 'GeForce GTX 1060/PCIe/SSE2'],
        'platform': ['Win32', 'MacIntel', 'Linux x86_64'],
        'languages': [['en-US', 'en'], ['en-GB', 'en']],
        'timezone': ['America/New_York', 'Europe/London', 'America/Los_Angeles']
    }
}


def get_random_user_agent() -> str:
    """Get a random user agent string from the pool"""
    category = random.choice(list(USER_AGENTS.keys()))
    return random.choice(USER_AGENTS[category])


def get_browser_fingerprint(browser_type: str = 'chrome') -> Dict[str, Any]:
    """Generate randomized browser fingerprint data"""
    fingerprint_data = BROWSER_FINGERPRINTS.get(browser_type, BROWSER_FINGERPRINTS['chrome'])

    return {
        'webgl_vendor': random.choice(fingerprint_data['webgl_vendor']),
        'webgl_renderer': random.choice(fingerprint_data['webgl_renderer']),
        'platform': random.choice(fingerprint_data['platform']),
        'languages': random.choice(fingerprint_data['languages']),
        'timezone': random.choice(fingerprint_data['timezone']),
        'screen_width': random.choice([1920, 1366, 1440, 2560, 1536]),
        'screen_height': random.choice([1080, 768, 900, 1440, 864]),
        'color_depth': random.choice([24, 32]),
        'device_memory': random.choice([4, 8, 16]),
        'hardware_concurrency': random.choice([4, 8, 12, 16])
    }


class CrawlerError(Exception):
    """Custom exception for crawler errors"""
    pass


class AuthenticationError(CrawlerError):
    """Exception raised when authentication fails"""
    pass


@dataclass
class AuthCredentials:
    """Authentication credentials for login"""
    username: str
    password: str
    username_selector: str = 'input[type="email"], input[name="username"], input[name="email"], #username, #email'
    password_selector: str = 'input[type="password"], #password'
    submit_selector: str = 'button[type="submit"], input[type="submit"], button:has-text("login"), button:has-text("sign in")'

@dataclass
class CrawlOptions:
    """Options for crawling configuration"""
    skip_hidden: bool = True
    skip_tags: Optional[List[str]] = None
    only_tags: Optional[List[str]] = None
    timeout: int = 30000
    wait_for_network_idle: bool = True
    max_elements: int = 1000

@dataclass
class ElementData:
    """Extracted element data"""
    tag: str
    selector: str
    xpath: str
    text: str
    attributes: Dict[str, str]
    is_interactive: bool

class WebCrawlerService:
    """Advanced web crawler service with authentication support"""
    
    def __init__(self, headless: bool = True, rotate_user_agent: bool = True):
        self.headless = headless
        self.rotate_user_agent = rotate_user_agent
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.current_user_agent = None
        self.current_fingerprint = None

        # Tags to skip by default
        self.default_skip_tags = {
            'script', 'style', 'meta', 'link', 'noscript', 'iframe',
            'img', 'svg', 'picture', 'figure', 'br', 'hr', 'wbr'
        }

        # Interactive element tags
        self.interactive_tags = {
            'a', 'button', 'input', 'select', 'textarea', 'details',
            'summary', 'label', 'option'
        }

    async def __aenter__(self):
        await self._init_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def _init_browser(self):
        """Initialize browser and context with enhanced fingerprinting"""
        try:
            # Get random user agent and fingerprint
            if self.rotate_user_agent:
                self.current_user_agent = get_random_user_agent()
                self.current_fingerprint = get_browser_fingerprint()
            else:
                self.current_user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                self.current_fingerprint = get_browser_fingerprint()

            playwright = await async_playwright().start()

            # Enhanced browser launch arguments for stealth
            launch_args = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor',
                '--disable-web-security',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-client-side-phishing-detection',
                '--disable-sync',
                '--disable-default-apps',
                '--hide-scrollbars',
                '--mute-audio',
                '--no-default-browser-check',
                '--no-pings',
                '--disable-extensions-http-throttling',
                f'--user-agent={self.current_user_agent}'
            ]

            self.browser = await playwright.chromium.launch(
                headless=self.headless,
                args=launch_args
            )

            # Create context with randomized fingerprint
            viewport_width = self.current_fingerprint['screen_width']
            viewport_height = self.current_fingerprint['screen_height']

            self.context = await self.browser.new_context(
                viewport={'width': viewport_width, 'height': viewport_height},
                user_agent=self.current_user_agent,
                locale='-'.join(self.current_fingerprint['languages'][:1]),
                timezone_id=self.current_fingerprint['timezone'],
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': ','.join([f"{lang};q={0.9-i*0.1:.1f}" for i, lang in enumerate(self.current_fingerprint['languages'])]),
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Cache-Control': 'max-age=0'
                }
            )

            # Add comprehensive stealth scripts with randomized fingerprints
            fingerprint_script = f"""
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {{
                    get: () => undefined,
                }});

                // Randomize plugins
                Object.defineProperty(navigator, 'plugins', {{
                    get: () => [1, 2, 3, 4, 5],
                }});

                // Set randomized languages
                Object.defineProperty(navigator, 'languages', {{
                    get: () => {self.current_fingerprint['languages']},
                }});

                // Set randomized platform
                Object.defineProperty(navigator, 'platform', {{
                    get: () => '{self.current_fingerprint['platform']}',
                }});

                // Set randomized hardware concurrency
                Object.defineProperty(navigator, 'hardwareConcurrency', {{
                    get: () => {self.current_fingerprint['hardware_concurrency']},
                }});

                // Set randomized device memory
                Object.defineProperty(navigator, 'deviceMemory', {{
                    get: () => {self.current_fingerprint['device_memory']},
                }});

                // Mock chrome object
                window.chrome = {{
                    runtime: {{}},
                    loadTimes: function() {{ return {{}}; }},
                    csi: function() {{ return {{}}; }},
                }};

                // Override WebGL fingerprinting
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                    if (parameter === 37445) {{
                        return '{self.current_fingerprint['webgl_vendor']}';
                    }}
                    if (parameter === 37446) {{
                        return '{self.current_fingerprint['webgl_renderer']}';
                    }}
                    return getParameter.call(this, parameter);
                }};

                // Override Canvas fingerprinting
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function() {{
                    const context = this.getContext('2d');
                    if (context) {{
                        // Add slight noise to canvas
                        const imageData = context.getImageData(0, 0, this.width, this.height);
                        for (let i = 0; i < imageData.data.length; i += 4) {{
                            imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                        }}
                        context.putImageData(imageData, 0, 0);
                    }}
                    return originalToDataURL.apply(this, arguments);
                }};

                // Override screen properties
                Object.defineProperty(screen, 'width', {{
                    get: () => {self.current_fingerprint['screen_width']},
                }});
                Object.defineProperty(screen, 'height', {{
                    get: () => {self.current_fingerprint['screen_height']},
                }});
                Object.defineProperty(screen, 'colorDepth', {{
                    get: () => {self.current_fingerprint['color_depth']},
                }});

                // Override timezone
                Date.prototype.getTimezoneOffset = function() {{
                    return new Date().getTimezoneOffset();
                }};

                // Remove automation indicators
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """

            await self.context.add_init_script(fingerprint_script)

            logger.info("Browser initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise

    async def _create_page(self) -> Page:
        """Create a new page with optimizations and realistic behavior simulation"""
        if not self.context:
            await self._init_browser()

        page = await self.context.new_page()

        # Enhanced resource blocking with selective loading
        await page.route("**/analytics*", lambda route: route.abort())
        await page.route("**/ads*", lambda route: route.abort())
        await page.route("**/tracking*", lambda route: route.abort())
        await page.route("**/facebook.com/tr*", lambda route: route.abort())
        await page.route("**/google-analytics.com*", lambda route: route.abort())
        await page.route("**/googletagmanager.com*", lambda route: route.abort())
        await page.route("**/doubleclick.net*", lambda route: route.abort())

        # Allow images and fonts for better rendering but with delay simulation
        await page.route("**/*.{png,jpg,jpeg,gif,svg}", lambda route: self._handle_image_request(route))
        await page.route("**/*.{woff,woff2,ttf,otf,eot}", lambda route: self._handle_font_request(route))

        # Add realistic mouse movement and scrolling behavior
        await self._add_realistic_behavior(page)

        return page

    async def _handle_image_request(self, route):
        """Handle image requests with realistic delays"""
        # Randomly decide whether to load images (90% chance)
        if random.random() < 0.9:
            # Add small random delay to simulate network latency
            await asyncio.sleep(random.uniform(0.01, 0.05))
            await route.continue_()
        else:
            await route.abort()

    async def _handle_font_request(self, route):
        """Handle font requests with realistic delays"""
        # Always load fonts but with delay
        await asyncio.sleep(random.uniform(0.005, 0.02))
        await route.continue_()

    async def _add_realistic_behavior(self, page: Page):
        """Add realistic user behavior simulation to the page"""
        # Add mouse movement simulation
        await page.add_init_script("""
            // Simulate realistic mouse movements
            let mouseX = Math.random() * window.innerWidth;
            let mouseY = Math.random() * window.innerHeight;

            function simulateMouseMovement() {
                const targetX = Math.random() * window.innerWidth;
                const targetY = Math.random() * window.innerHeight;

                const steps = 10 + Math.random() * 20;
                const stepX = (targetX - mouseX) / steps;
                const stepY = (targetY - mouseY) / steps;

                let step = 0;
                const moveInterval = setInterval(() => {
                    mouseX += stepX + (Math.random() - 0.5) * 2;
                    mouseY += stepY + (Math.random() - 0.5) * 2;

                    document.dispatchEvent(new MouseEvent('mousemove', {
                        clientX: mouseX,
                        clientY: mouseY,
                        bubbles: true
                    }));

                    step++;
                    if (step >= steps) {
                        clearInterval(moveInterval);
                        // Schedule next movement
                        setTimeout(simulateMouseMovement, 1000 + Math.random() * 3000);
                    }
                }, 50 + Math.random() * 50);
            }

            // Start mouse movement simulation after page load
            setTimeout(simulateMouseMovement, 1000 + Math.random() * 2000);

            // Simulate scroll behavior
            function simulateScrolling() {
                const scrollAmount = Math.random() * 200 - 100;
                window.scrollBy(0, scrollAmount);
                setTimeout(simulateScrolling, 2000 + Math.random() * 5000);
            }

            setTimeout(simulateScrolling, 3000 + Math.random() * 2000);
        """)

    async def _detect_login_page(self, page: Page) -> bool:
        """Detect if current page is a login page"""
        try:
            indicators = await page.evaluate("""
                () => {
                    const hasPasswordField = !!document.querySelector('input[type="password"]');
                    
                    const hasLoginButton = !!Array.from(
                        document.querySelectorAll('button, input[type="submit"]')
                    ).some(el => {
                        const text = (el.textContent || '').toLowerCase();
                        const value = (el.value || '').toLowerCase();
                        return text.includes('login') || text.includes('sign in') || 
                               value.includes('login') || value.includes('sign in');
                    });
                    
                    const hasLoginForm = !!document.querySelector(
                        'form[id*="login"], form[class*="login"], form[id*="signin"], form[class*="signin"]'
                    );
                    
                    const hasLoginHeader = !!Array.from(
                        document.querySelectorAll('h1, h2, h3, .header, .heading')
                    ).some(el => {
                        const text = (el.textContent || '').toLowerCase();
                        return text.includes('login') || text.includes('sign in');
                    });
                    
                    return [hasPasswordField, hasLoginButton, hasLoginForm, hasLoginHeader]
                        .filter(Boolean).length >= 2;
                }
            """)
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error detecting login page: {e}")
            return False

    async def _type_with_human_delays(self, page: Page, selector: str, text: str):
        """Type text with realistic human-like delays between keystrokes"""
        # Clear the field first
        await page.fill(selector, '')

        # Type each character with random delays
        for char in text:
            await page.type(selector, char, delay=random.uniform(50, 150))
            # Occasionally add longer pauses (like thinking)
            if random.random() < 0.1:
                await asyncio.sleep(random.uniform(0.2, 0.5))

    async def _perform_login(self, page: Page, credentials: AuthCredentials) -> bool:
        """Perform login with provided credentials using realistic human-like interactions"""
        try:
            # Wait for login form to be ready
            await page.wait_for_selector(credentials.username_selector, timeout=10000)

            # Simulate realistic interaction delays
            await asyncio.sleep(random.uniform(0.5, 1.5))

            # Click on username field first (more human-like)
            await page.click(credentials.username_selector)
            await asyncio.sleep(random.uniform(0.2, 0.5))

            # Type username with realistic delays between keystrokes
            await self._type_with_human_delays(page, credentials.username_selector, credentials.username)
            await asyncio.sleep(random.uniform(0.3, 0.8))

            # Click on password field
            await page.click(credentials.password_selector)
            await asyncio.sleep(random.uniform(0.2, 0.5))

            # Type password with realistic delays
            await self._type_with_human_delays(page, credentials.password_selector, credentials.password)
            await asyncio.sleep(random.uniform(0.5, 1.2))

            # Get current URL before login
            current_url = page.url

            # Simulate mouse hover over submit button before clicking
            await page.hover(credentials.submit_selector)
            await asyncio.sleep(random.uniform(0.1, 0.3))

            # Click submit button
            await page.click(credentials.submit_selector)

            # Wait for navigation or URL change
            try:
                await page.wait_for_url(lambda url: url != current_url, timeout=15000)
            except PlaywrightTimeoutError:
                # Check if we're still on login page
                if await self._detect_login_page(page):
                    return False
            
            # Additional check: ensure we're not still on login page
            await asyncio.sleep(2)
            return not await self._detect_login_page(page)
            
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False

    def _generate_css_selector(self, element_data: Dict) -> str:
        """Generate CSS selector for an element"""
        selectors = []

        # Priority order: id, data-testid, name, class, tag
        if element_data.get('id'):
            return f"#{element_data['id']}"

        if element_data.get('data-testid'):
            return f"[data-testid='{element_data['data-testid']}']"

        if element_data.get('name'):
            selectors.append(f"[name='{element_data['name']}']")

        if element_data.get('class'):
            classes = element_data['class'].strip().split()
            if classes:
                class_selector = '.' + '.'.join(classes[:2])  # Use first 2 classes
                selectors.append(class_selector)

        # Add tag as base selector
        tag = element_data.get('tag', 'div')
        if selectors:
            return f"{tag}{selectors[0]}"
        else:
            return tag

    def _generate_xpath(self, element_data: Dict) -> str:
        """Generate XPath for an element"""
        if element_data.get('id'):
            return f"//*[@id='{element_data['id']}']"

        if element_data.get('data-testid'):
            return f"//*[@data-testid='{element_data['data-testid']}']"

        # Fallback to basic xpath
        return element_data.get('xpath', f"//{element_data.get('tag', 'div')}")

    def _should_skip_element(self, element_data: Dict, options: CrawlOptions) -> bool:
        """Determine if element should be skipped"""
        tag = element_data.get('tag', '').lower()

        # Skip tags in default skip list
        skip_tags = set(options.skip_tags or []) | self.default_skip_tags
        if tag in skip_tags:
            return True

        # If only_tags specified, skip if not in list
        if options.only_tags and tag not in [t.lower() for t in options.only_tags]:
            return True

        # Skip hidden elements if configured
        if options.skip_hidden and element_data.get('hidden', False):
            return True

        return False

    def _is_element_interactive(self, element_data: Dict) -> bool:
        """Check if element is interactive"""
        tag = element_data.get('tag', '').lower()

        if tag in self.interactive_tags:
            return True

        # Check for interactive roles or attributes
        role = element_data.get('role', '').lower()
        if role in ['button', 'link', 'tab', 'menuitem']:
            return True

        if element_data.get('tabindex') is not None:
            return True

        if element_data.get('onclick'):
            return True

        return False

    def _extract_text_content(self, element_data: Dict) -> str:
        """Extract meaningful text content from element"""
        # Priority order for text extraction
        text_sources = [
            element_data.get('aria-label'),
            element_data.get('title'),
            element_data.get('placeholder'),
            element_data.get('value'),
            element_data.get('text', '').strip()
        ]

        for text in text_sources:
            if text and text.strip():
                # Truncate long text
                text = text.strip()
                if len(text) > 100:
                    text = text[:97] + '...'
                return text

        return ''

    async def crawl(
        self,
        url: str,
        is_login_needed: bool = False,
        auth_credentials: Optional[AuthCredentials] = None,
        options: Optional[CrawlOptions] = None
    ) -> Dict[str, Any]:
        """
        Crawl a webpage and extract element data

        Args:
            url: URL to crawl
            is_login_needed: Whether login is required
            auth_credentials: Login credentials if needed
            options: Crawling options

        Returns:
            Dictionary with extracted elements data
        """
        if options is None:
            options = CrawlOptions()

        page = await self._create_page()

        try:
            # Normalize URL - ensure it has a protocol
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            logger.info(f"Attempting to crawl URL: {url}")

            # Navigate to the page with retry logic
            response = None
            for attempt in range(3):  # Try up to 3 times
                try:
                    response = await page.goto(
                        url,
                        wait_until='networkidle' if options.wait_for_network_idle else 'load',
                        timeout=options.timeout
                    )
                    break
                except Exception as e:
                    logger.warning(f"Navigation attempt {attempt + 1} failed for {url}: {e}")
                    if attempt == 2:  # Last attempt
                        raise
                    await asyncio.sleep(1)  # Wait before retry

            # Check response status
            if response and response.status >= 400:
                logger.error(f"HTTP {response.status} error for {url}")
                raise CrawlerError(f"HTTP {response.status}: {response.status_text}")

            # Handle authentication if needed
            if is_login_needed:
                if not auth_credentials:
                    raise ValueError("Authentication credentials required when is_login_needed=True")

                # Check if we're on a login page
                if await self._detect_login_page(page):
                    login_success = await self._perform_login(page, auth_credentials)
                    if not login_success:
                        raise AuthenticationError("Login failed - invalid credentials or login process failed")

                    # Navigate to original URL after login
                    response = await page.goto(url, wait_until='networkidle' if options.wait_for_network_idle else 'load',
                                  timeout=options.timeout)

                    if response and response.status >= 400:
                        logger.error(f"HTTP {response.status} error after login for {url}")
                        raise CrawlerError(f"HTTP {response.status}: {response.status_text}")

            # Enhanced JavaScript execution and AJAX handling
            await self._ensure_full_page_load(page, options)

            # Extract elements data
            elements_data = await self._extract_elements(page, options)

            logger.info(f"Successfully crawled {url}, extracted {len(elements_data)} elements")

            return {
                'url': url,
                'elements': elements_data,
                'total_elements': len(elements_data),
                'timestamp': asyncio.get_event_loop().time()
            }

        except Exception as e:
            error_msg = f"Crawling failed for {url}: {str(e)}"
            logger.error(error_msg)
            # Re-raise with more context
            if "net::ERR_NAME_NOT_RESOLVED" in str(e):
                raise CrawlerError(f"Domain not found: {url}")
            elif "net::ERR_CONNECTION_REFUSED" in str(e):
                raise CrawlerError(f"Connection refused: {url}")
            elif "net::ERR_TIMED_OUT" in str(e):
                raise CrawlerError(f"Connection timed out: {url}")
            else:
                raise CrawlerError(error_msg)
        finally:
            await page.close()

    async def _ensure_full_page_load(self, page: Page, options: CrawlOptions):
        """Ensure full page load with JavaScript execution and AJAX completion"""
        try:
            # Wait for initial network idle
            await page.wait_for_load_state('networkidle', timeout=options.timeout)

            # Simulate realistic user interactions to trigger lazy loading
            await self._simulate_user_interactions(page)

            # Wait for any additional AJAX requests triggered by interactions
            await asyncio.sleep(random.uniform(1.0, 2.0))

            # Check for and wait for common AJAX indicators
            await self._wait_for_ajax_completion(page)

            # Scroll to trigger any lazy-loaded content
            await self._trigger_lazy_loading(page)

            # Final wait for any remaining network activity
            try:
                await page.wait_for_load_state('networkidle', timeout=5000)
            except PlaywrightTimeoutError:
                # Continue if timeout - some pages have continuous background requests
                pass

        except Exception as e:
            logger.warning(f"Error ensuring full page load: {e}")

    async def _simulate_user_interactions(self, page: Page):
        """Simulate realistic user interactions to trigger dynamic content"""
        try:
            # Random mouse movements
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.3))

            # Random scrolling
            for _ in range(random.randint(1, 3)):
                scroll_amount = random.randint(200, 800)
                await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
                await asyncio.sleep(random.uniform(0.5, 1.0))

        except Exception as e:
            logger.warning(f"Error simulating user interactions: {e}")

    async def _wait_for_ajax_completion(self, page: Page):
        """Wait for common AJAX completion indicators"""
        try:
            # Wait for jQuery if present
            await page.evaluate("""
                new Promise((resolve) => {
                    if (typeof jQuery !== 'undefined') {
                        const checkJQuery = () => {
                            if (jQuery.active === 0) {
                                resolve();
                            } else {
                                setTimeout(checkJQuery, 100);
                            }
                        };
                        checkJQuery();
                    } else {
                        resolve();
                    }
                });
            """)

            # Wait for common loading indicators to disappear
            loading_selectors = [
                '.loading', '.spinner', '.loader', '[data-loading]',
                '.fa-spinner', '.fa-spin', '.loading-overlay'
            ]

            for selector in loading_selectors:
                try:
                    await page.wait_for_selector(selector, state='hidden', timeout=2000)
                except PlaywrightTimeoutError:
                    continue

        except Exception as e:
            logger.warning(f"Error waiting for AJAX completion: {e}")

    async def _trigger_lazy_loading(self, page: Page):
        """Trigger lazy loading by scrolling through the page"""
        try:
            # Get page height
            page_height = await page.evaluate("document.body.scrollHeight")
            viewport_height = await page.evaluate("window.innerHeight")

            # Scroll through the page in chunks
            current_position = 0
            scroll_step = viewport_height // 2

            while current_position < page_height:
                await page.evaluate(f"window.scrollTo(0, {current_position})")
                await asyncio.sleep(random.uniform(0.3, 0.7))
                current_position += scroll_step

                # Update page height in case new content was loaded
                new_height = await page.evaluate("document.body.scrollHeight")
                if new_height > page_height:
                    page_height = new_height

            # Scroll back to top
            await page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(0.5)

        except Exception as e:
            logger.warning(f"Error triggering lazy loading: {e}")

    async def _extract_elements(self, page: Page, options: CrawlOptions) -> List[Dict[str, Any]]:
        """Extract elements from the page"""
        try:
            # Execute JavaScript to extract element data
            elements_raw = await page.evaluate("""
                (options) => {
                    const elements = [];
                    const processedElements = new Set();

                    // Get all elements in DOM order using TreeWalker for better performance
                    const walker = document.createTreeWalker(
                        document.body || document.documentElement,
                        NodeFilter.SHOW_ELEMENT,
                        {
                            acceptNode: function(node) {
                                const tagName = node.tagName.toLowerCase();
                                // Skip script, style, meta, etc.
                                const skipTags = new Set(['script', 'style', 'meta', 'link', 'noscript', 'head', 'title']);
                                return skipTags.has(tagName) ? NodeFilter.FILTER_REJECT : NodeFilter.FILTER_ACCEPT;
                            }
                        }
                    );

                    let element;
                    while (element = walker.nextNode()) {
                        try {
                            const tagName = element.tagName.toLowerCase();

                            // Get element attributes
                            const attributes = {};
                            for (let attr of element.attributes) {
                                attributes[attr.name] = attr.value;
                            }

                            // Check if element is hidden
                            const computedStyle = window.getComputedStyle(element);
                            const isHidden = computedStyle.display === 'none' ||
                                           computedStyle.visibility === 'hidden' ||
                                           computedStyle.opacity === '0' ||
                                           element.hidden ||
                                           element.offsetParent === null;

                            // Skip hidden elements early if option is set
                            if (options.skipHidden && isHidden) continue;

                            // Get text content intelligently
                            let textContent = '';

                            // For input elements, get value or placeholder
                            if (['input', 'textarea', 'select'].includes(tagName)) {
                                textContent = element.value ||
                                            element.getAttribute('placeholder') ||
                                            element.getAttribute('aria-label') ||
                                            element.getAttribute('title') || '';
                            } else {
                                // Get direct text nodes only (not from children)
                                for (let node of element.childNodes) {
                                    if (node.nodeType === Node.TEXT_NODE) {
                                        const text = node.textContent.trim();
                                        if (text) textContent += text + ' ';
                                    }
                                }
                                textContent = textContent.trim();

                                // If no direct text, try semantic attributes
                                if (!textContent) {
                                    textContent = element.getAttribute('aria-label') ||
                                                element.getAttribute('title') ||
                                                element.getAttribute('alt') ||
                                                element.getAttribute('data-label') ||
                                                '';
                                }
                            }

                            // Check if element is interactive
                            const isInteractive = ['a', 'button', 'input', 'select', 'textarea', 'option', 'label'].includes(tagName) ||
                                                 element.hasAttribute('onclick') ||
                                                 element.hasAttribute('href') ||
                                                 element.hasAttribute('role') ||
                                                 element.hasAttribute('tabindex') ||
                                                 (element.getAttribute('role') &&
                                                  ['button', 'link', 'menuitem', 'tab', 'checkbox', 'radio'].includes(element.getAttribute('role')));

                            // Skip non-interactive elements without meaningful text
                            if (!isInteractive && (!textContent || textContent.length < 2)) {
                                // Exception: keep important structural elements
                                const structuralTags = new Set(['div', 'section', 'article', 'main', 'nav', 'header', 'footer', 'aside', 'form']);
                                if (!structuralTags.has(tagName)) continue;
                            }

                            // Generate XPath
                            const getXPath = (element) => {
                                if (element.id) {
                                    return `//*[@id="${element.id}"]`;
                                }

                                let path = '';
                                let current = element;

                                while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
                                    let index = 1;
                                    let sibling = current.previousElementSibling;

                                    while (sibling) {
                                        if (sibling.tagName === current.tagName) {
                                            index++;
                                        }
                                        sibling = sibling.previousElementSibling;
                                    }

                                    const tagName = current.tagName.toLowerCase();
                                    path = `/${tagName}${index > 1 ? `[${index}]` : ''}${path}`;
                                    current = current.parentElement;
                                }

                                return `/html/body${path}`;
                            };

                            const xpath = getXPath(element);

                            // Skip if already processed (duplicate xpath)
                            if (processedElements.has(xpath)) continue;
                            processedElements.add(xpath);

                            // Calculate element priority for sorting
                            let priority = 0;
                            if (isInteractive) priority += 100;
                            if (textContent && textContent.length > 0) priority += 50;
                            if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) priority += 75;
                            if (['button', 'a', 'input'].includes(tagName)) priority += 80;

                            const elementData = {
                                tag: tagName,
                                xpath: xpath,
                                text: textContent,
                                hidden: isHidden,
                                interactive: isInteractive,
                                priority: priority,
                                domOrder: elements.length, // Preserve DOM order
                                ...attributes
                            };

                            elements.push(elementData);

                            // Limit number of elements
                            if (elements.length >= options.maxElements) break;

                        } catch (error) {
                            console.error('Error processing element:', error);
                        }
                    }

                    // Sort by DOM order to maintain structure, but prioritize interactive elements
                    elements.sort((a, b) => {
                        // First sort by priority (interactive elements first)
                        if (a.priority !== b.priority) {
                            return b.priority - a.priority;
                        }
                        // Then by DOM order
                        return a.domOrder - b.domOrder;
                    });

                    return elements;
                }
            """, {
                'maxElements': options.max_elements,
                'skipHidden': options.skip_hidden
            })

            # Process and filter elements
            processed_elements = []

            for element_data in elements_raw:
                if self._should_skip_element(element_data, options):
                    continue

                # Create final element data structure
                processed_element = {
                    'tag': element_data.get('tag', ''),
                    'selector': self._generate_css_selector(element_data),
                    'xpath': element_data.get('xpath', ''),
                    'text': self._extract_text_content(element_data),
                    'attributes': {k: str(v) for k, v in element_data.items()
                                 if k not in ['tag', 'xpath', 'text', 'hidden', 'interactive', 'priority', 'domOrder']},
                    'is_interactive': self._is_element_interactive(element_data)
                }

                processed_elements.append(processed_element)

            logger.info(f"Extracted {len(processed_elements)} elements")
            return processed_elements

        except Exception as e:
            logger.error(f"Error extracting elements: {e}")
            return []

    async def close(self):
        """Close browser and cleanup resources"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
        except Exception as e:
            logger.error(f"Error closing browser: {e}")

    def _should_skip_element(self, element_data: Dict[str, Any], options: CrawlOptions) -> bool:
        """Determine if an element should be skipped based on intelligence rules"""

        # Skip hidden elements if option is set
        if options.skip_hidden and element_data.get('hidden', False):
            return True

        # Skip elements without text and that are not interactive
        text = element_data.get('text', '').strip()
        tag = element_data.get('tag', '').lower()

        # Always include interactive elements even without text
        if self._is_element_interactive(element_data):
            return False

        # Skip non-interactive elements without meaningful text
        if not text or len(text) < 2:
            # Exception: keep structural elements that might be important
            structural_tags = {'div', 'section', 'article', 'main', 'nav', 'header', 'footer', 'aside'}
            if tag not in structural_tags:
                return True

        # Skip elements with only whitespace or common filler text
        filler_texts = {'', ' ', '\n', '\t', '•', '|', '-', '_', '...', 'loading', 'please wait'}
        if text.lower().strip() in filler_texts:
            return True

        # Skip very long text blocks (likely content blocks we don't need for automation)
        if len(text) > 500:
            return True

        return False

    def _is_element_interactive(self, element_data: Dict[str, Any]) -> bool:
        """Check if an element is interactive (clickable, input, etc.)"""
        tag = element_data.get('tag', '').lower()

        # Interactive tags
        interactive_tags = {
            'a', 'button', 'input', 'select', 'textarea', 'option',
            'label', 'form', 'fieldset', 'legend', 'details', 'summary'
        }

        if tag in interactive_tags:
            return True

        # Check for interactive attributes
        interactive_attrs = ['onclick', 'onsubmit', 'href', 'role', 'tabindex']
        for attr in interactive_attrs:
            if attr in element_data:
                return True

        # Check for interactive roles
        role = element_data.get('role', '').lower()
        interactive_roles = {
            'button', 'link', 'menuitem', 'tab', 'checkbox', 'radio',
            'textbox', 'searchbox', 'combobox', 'listbox', 'option',
            'slider', 'spinbutton', 'switch', 'dialog', 'alertdialog'
        }

        if role in interactive_roles:
            return True

        # Check for clickable classes (common patterns)
        class_attr = element_data.get('class', '')
        if class_attr:
            clickable_patterns = ['btn', 'button', 'click', 'link', 'menu', 'nav', 'tab']
            class_lower = class_attr.lower()
            for pattern in clickable_patterns:
                if pattern in class_lower:
                    return True

        return False

    def _generate_css_selector(self, element_data: Dict[str, Any]) -> str:
        """Generate a CSS selector for the element"""
        tag = element_data.get('tag', '')

        # Use ID if available
        if 'id' in element_data and element_data['id']:
            return f"#{element_data['id']}"

        # Use class if available
        if 'class' in element_data and element_data['class']:
            classes = element_data['class'].replace(' ', '.')
            return f"{tag}.{classes}"

        # Use other unique attributes
        for attr in ['name', 'data-testid', 'data-test', 'data-cy']:
            if attr in element_data and element_data[attr]:
                return f"{tag}[{attr}='{element_data[attr]}']"

        # Fall back to xpath-based selector (simplified)
        xpath = element_data.get('xpath', '')
        if xpath:
            # Convert simple xpath to CSS selector
            return xpath.replace('/html/', '').replace('/', ' > ').replace('[1]', ':first-child')

        return tag

    def _extract_text_content(self, element_data: Dict[str, Any]) -> str:
        """Extract and clean text content from element"""
        text = element_data.get('text', '').strip()

        # Clean up whitespace
        text = ' '.join(text.split())

        # Truncate very long text
        if len(text) > 200:
            text = text[:197] + '...'

        return text


# Convenience function for one-off crawling
async def crawl_website(
    url: str,
    is_login_needed: bool = False,
    username: str = None,
    password: str = None,
    options: Optional[CrawlOptions] = None
) -> Dict[str, Any]:
    """
    Convenience function to crawl a website

    Args:
        url: URL to crawl
        is_login_needed: Whether login is required
        username: Username for login
        password: Password for login
        options: Crawling options

    Returns:
        Dictionary with extracted elements data
    """
    auth_credentials = None
    if is_login_needed and username and password:
        auth_credentials = AuthCredentials(username=username, password=password)

    async with WebCrawlerService() as crawler:
        return await crawler.crawl(url, is_login_needed, auth_credentials, options)
