import asyncio
import json
import logging
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class StreamingProgressStore:
    """In-memory store for streaming progress updates."""
    
    def __init__(self):
        self._streams: Dict[str, asyncio.Queue] = {}
        self._session_status: Dict[str, str] = {}
    
    def create_stream(self, session_id: str) -> None:
        """Create a new stream for a session."""
        if session_id not in self._streams:
            self._streams[session_id] = asyncio.Queue()
            self._session_status[session_id] = "active"
            logger.info(f"Created stream for session {session_id}")
    
    async def add_progress(self, session_id: str, progress: Dict[str, Any]) -> None:
        """Add progress update to a session's stream."""
        if session_id in self._streams and self._session_status.get(session_id) == "active":
            try:
                # Add timestamp if not present
                if "timestamp" not in progress:
                    progress["timestamp"] = datetime.now().isoformat()

                await self._streams[session_id].put(progress)

                # Only log important events, not every progress update
                if progress.get("type") in ["error", "complete", "explanation_complete", "review_complete_final", "code_chunk"]:
                    if progress.get("type") == "code_chunk":
                        chunk_preview = progress.get("data", {}).get("chunk", "")[:50] + "..." if progress.get("data", {}).get("chunk") else "no chunk"
                        logger.info(f"Added code_chunk to stream {session_id}: {chunk_preview}")
                    else:
                        logger.info(f"Added progress to stream {session_id}: {progress.get('type', 'unknown')} - {progress.get('message', '')}")

                # Only close stream on critical errors - keep open for interactive chat
                # Don't close on review_complete_final or explanation_complete - let frontend manage closure
                if progress.get("type") in ["error"] or progress.get("status") in ["failed"]:
                    logger.info(f"Stream {session_id} completing with type: {progress.get('type')} status: {progress.get('status')}")
                    await self._streams[session_id].put({"type": "stream_end"})
                    self._session_status[session_id] = "completed"

            except Exception as e:
                logger.error(f"Error adding progress to stream {session_id}: {e}")
        else:
            logger.warning(f"Cannot add progress to stream {session_id}: stream not active or doesn't exist")
    
    async def get_progress_stream(self, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Get progress stream for a session."""
        logger.info(f"GET_PROGRESS_STREAM CALLED for session {session_id}")
        if session_id not in self._streams:
            self.create_stream(session_id)

        queue = self._streams[session_id]
        logger.info(f"Stream status for {session_id}: {self._session_status.get(session_id)}")

        try:
            while self._session_status.get(session_id) == "active":
                try:
                    # Wait for progress with timeout
                    progress = await asyncio.wait_for(queue.get(), timeout=1.0)

                    if progress.get("type") == "stream_end":
                        logger.info(f"STREAM_END received for {session_id}")
                        break

                    yield progress
                    
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield {
                        "type": "keepalive",
                        "timestamp": datetime.now().isoformat()
                    }
                    continue
                    
        except Exception as e:
            logger.error(f"Error in progress stream {session_id}: {e}")
            yield {
                "type": "error",
                "message": f"Stream error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        finally:
            self.cleanup_stream(session_id)
    
    def cleanup_stream(self, session_id: str) -> None:
        """Clean up stream resources for a session."""
        if session_id in self._streams:
            # Ensure the queue is properly closed
            try:
                queue = self._streams[session_id]
                # Put a final message to unblock any waiting consumers
                if not queue.empty():
                    try:
                        while not queue.empty():
                            queue.get_nowait()
                    except asyncio.QueueEmpty:
                        pass
            except Exception as e:
                logger.warning(f"Error cleaning up queue for session {session_id}: {e}")
            finally:
                del self._streams[session_id]

        if session_id in self._session_status:
            del self._session_status[session_id]
        logger.info(f"Cleaned up stream for session {session_id}")

    def get_active_streams(self) -> int:
        """Get number of active streams."""
        return len([s for s in self._session_status.values() if s == "active"])

    def cleanup_all_streams(self) -> None:
        """Clean up all active streams - useful for shutdown."""
        session_ids = list(self._streams.keys())
        for session_id in session_ids:
            self.cleanup_stream(session_id)
        logger.info(f"Cleaned up all {len(session_ids)} active streams")

# Global instance
streaming_store = StreamingProgressStore()
