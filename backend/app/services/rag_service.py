import uuid
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.http import models
from sentence_transformers import SentenceTransformer
from app.core.config import settings

class RAGService:
    def __init__(self):
        self.client = None
        self.encoder = None
        self.collection_name = "requirements"
        self.is_available = False
        self._initialize_services()

    def _initialize_services(self):
        """Initialize Qdrant client and encoder with error handling."""
        try:
            # Initialize Qdrant client
            self.client = QdrantClient(
                host=settings.QDRANT_HOST,
                port=settings.QDRANT_PORT
            )

            # Test connection
            self.client.get_collections()

            # Initialize encoder
            self.encoder = SentenceTransformer('all-MiniLM-L6-v2')

            # Ensure collection exists
            self._ensure_collection_exists()

            self.is_available = True
            print("RAG service initialized successfully")

        except Exception as e:
            print(f"RAG service unavailable: {e}")
            print("RAG features will be disabled until Qdrant is available")
            self.is_available = False
            self.client = None
            self.encoder = None

    def _ensure_collection_exists(self):
        """Ensure the requirements collection exists in Qdrant."""
        if not self.is_available or not self.client:
            return

        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=models.VectorParams(
                        size=384,  # all-MiniLM-L6-v2 embedding size
                        distance=models.Distance.COSINE
                    )
                )
                print(f"Created collection: {self.collection_name}")
        except Exception as e:
            print(f"Error ensuring collection exists: {e}")
            self.is_available = False

    def health_check(self) -> bool:
        """Check if the service is available and healthy."""
        if not self.is_available:
            return False

        try:
            if self.client:
                self.client.get_collections()
                return True
        except Exception as e:
            print(f"RAG service health check failed: {e}")
            self.is_available = False

        return False

    def retry_initialization(self) -> bool:
        """Retry initializing the service if it was previously unavailable."""
        if not self.is_available:
            print("Retrying RAG service initialization...")
            self._initialize_services()
        return self.is_available

    def add_requirement(self, requirement_id: int, project_id: int, text: str, metadata: Dict[str, Any] = None):
        """Add a requirement to the vector database."""
        if not self.is_available or not self.client or not self.encoder:
            print("RAG service unavailable, skipping requirement embedding")
            return

        try:
            # Ensure collection exists before adding
            self._ensure_collection_exists()

            # Generate embedding
            embedding = self.encoder.encode(text).tolist()

            # Prepare payload
            payload = {
                "requirement_id": requirement_id,
                "project_id": project_id,
                "text": text,
                **(metadata or {})
            }

            # Add to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=[
                    models.PointStruct(
                        id=str(uuid.uuid4()),
                        vector=embedding,
                        payload=payload
                    )
                ]
            )
            print(f"Added requirement {requirement_id} to vector database")

        except Exception as e:
            print(f"Error adding requirement to vector database: {e}")
            # Try to recreate collection if it was deleted
            try:
                self._ensure_collection_exists()
                print("Attempted to recreate collection after error")
            except Exception as recreate_error:
                print(f"Failed to recreate collection: {recreate_error}")

    def add_test_case(self, test_case_id: int, requirement_id: int, project_id: int, text: str, metadata: Dict[str, Any] = None):
        """Add a test case to the vector database."""
        if not self.is_available or not self.client or not self.encoder:
            print("RAG service unavailable, skipping test case embedding")
            return

        try:
            # Ensure collection exists before adding
            self._ensure_collection_exists()

            # Generate embedding
            embedding = self.encoder.encode(text).tolist()

            # Prepare payload
            payload = {
                "test_case_id": test_case_id,
                "requirement_id": requirement_id,
                "project_id": project_id,
                "text": text,
                "type": "test_case",
                **(metadata or {})
            }

            # Add to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=[
                    models.PointStruct(
                        id=str(uuid.uuid4()),
                        vector=embedding,
                        payload=payload
                    )
                ]
            )
            print(f"Added test case {test_case_id} to vector database")

        except Exception as e:
            print(f"Error adding test case to vector database: {e}")

    def update_test_case(self, test_case_id: int, text: str, metadata: Dict[str, Any] = None):
        """Update a test case in the vector database."""
        try:
            # First delete the old entry
            self.delete_test_case(test_case_id)

            # Then add the updated entry
            if metadata and "requirement_id" in metadata and "project_id" in metadata:
                self.add_test_case(
                    test_case_id=test_case_id,
                    requirement_id=metadata["requirement_id"],
                    project_id=metadata["project_id"],
                    text=text,
                    metadata=metadata
                )

        except Exception as e:
            print(f"Error updating test case in vector database: {e}")

    def delete_test_case(self, test_case_id: int):
        """Delete a test case from the vector database."""
        try:
            # Search for points with this test_case_id
            search_result = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="test_case_id",
                            match=models.MatchValue(value=test_case_id)
                        )
                    ]
                ),
                limit=100
            )

            # Delete found points
            point_ids = [point.id for point in search_result[0]]
            if point_ids:
                self.client.delete(
                    collection_name=self.collection_name,
                    points_selector=models.PointIdsList(points=point_ids)
                )
                print(f"Deleted test case {test_case_id} from vector database")

        except Exception as e:
            print(f"Error deleting test case from vector database: {e}")

    def update_requirement(self, requirement_id: int, text: str, metadata: Dict[str, Any] = None):
        """Update a requirement in the vector database."""
        try:
            # First, delete existing entries for this requirement
            self.delete_requirement(requirement_id)
            
            # Then add the updated version
            if metadata and "project_id" in metadata:
                self.add_requirement(requirement_id, metadata["project_id"], text, metadata)
            
        except Exception as e:
            print(f"Error updating requirement in vector database: {e}")
    
    def delete_requirement(self, requirement_id: int):
        """Delete a requirement from the vector database."""
        try:
            # Search for points with this requirement_id
            search_result = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="requirement_id",
                            match=models.MatchValue(value=requirement_id)
                        )
                    ]
                ),
                limit=100
            )
            
            # Delete found points
            point_ids = [point.id for point in search_result[0]]
            if point_ids:
                self.client.delete(
                    collection_name=self.collection_name,
                    points_selector=models.PointIdsList(points=point_ids)
                )
                print(f"Deleted requirement {requirement_id} from vector database")
            
        except Exception as e:
            print(f"Error deleting requirement from vector database: {e}")
    
    def search_similar_requirements(
        self,
        query_text: str,
        project_id: int = None,
        limit: int = 5,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Search for similar requirements."""
        if not self.is_available or not self.client or not self.encoder:
            print("RAG service unavailable, returning empty results")
            return []

        try:
            # Ensure collection exists before searching
            self._ensure_collection_exists()

            # Generate embedding for query
            query_embedding = self.encoder.encode(query_text).tolist()

            # Prepare filter
            search_filter = None
            if project_id:
                search_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="project_id",
                            match=models.MatchValue(value=project_id)
                        )
                    ]
                )

            # Search
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            results = []
            for hit in search_result:
                results.append({
                    "requirement_id": hit.payload.get("requirement_id"),
                    "project_id": hit.payload.get("project_id"),
                    "text": hit.payload.get("text"),
                    "name": hit.payload.get("name"),  # Include requirement name
                    "score": hit.score,
                    "metadata": {k: v for k, v in hit.payload.items()
                               if k not in ["requirement_id", "project_id", "text", "name"]}
                })
            
            return results
            
        except Exception as e:
            print(f"Error searching similar requirements: {e}")
            return []
    
    def get_context_for_requirement(self, requirement_text: str, project_id: int, limit: int = 3) -> str:
        """Get context from similar requirements for LLM processing."""
        similar_requirements = self.search_similar_requirements(
            requirement_text,
            project_id=project_id,
            limit=limit
        )

        if not similar_requirements:
            return ""

        context_parts = ["Similar requirements from this project:"]
        for i, req in enumerate(similar_requirements, 1):
            req_name = req.get('name', f"Requirement {req.get('requirement_id', 'Unknown')}")
            context_parts.append(f"{i}. [{req_name}] {req['text']} (similarity: {req['score']:.2f})")

        return "\n".join(context_parts)

    def get_context_with_conflict_detection(self, requirement_text: str, project_id: int, current_requirement_id: int = None, limit: int = 5) -> Dict[str, str]:
        """Get context from similar requirements with conflict detection for LLM processing."""
        similar_requirements = self.search_similar_requirements(
            requirement_text,
            project_id=project_id,
            limit=limit,
            score_threshold=0.6  # Lower threshold to catch potential conflicts
        )

        if not similar_requirements:
            return {"context": "", "conflicts": ""}

        # Filter out the current requirement if provided
        if current_requirement_id:
            similar_requirements = [req for req in similar_requirements if req.get('requirement_id') != current_requirement_id]

        if not similar_requirements:
            return {"context": "", "conflicts": ""}

        # Separate high similarity (potential conflicts) from regular context
        high_similarity_reqs = [req for req in similar_requirements if req['score'] >= 0.8]
        regular_context_reqs = [req for req in similar_requirements if req['score'] < 0.8]

        # Build regular context
        context_parts = []
        if regular_context_reqs:
            context_parts.append("Similar requirements from this project:")
            for i, req in enumerate(regular_context_reqs[:3], 1):
                req_name = req.get('name', f"Requirement {req.get('requirement_id', 'Unknown')}")
                context_parts.append(f"{i}. [{req_name}] {req['text']} (similarity: {req['score']:.2f})")

        # Build conflict context
        conflict_parts = []
        if high_similarity_reqs:
            conflict_parts.append("Potentially conflicting requirements (high similarity):")
            for i, req in enumerate(high_similarity_reqs, 1):
                req_name = req.get('name', f"Requirement {req.get('requirement_id', 'Unknown')}")
                conflict_parts.append(f"{i}. [{req_name}] {req['text']} (similarity: {req['score']:.2f})")

        return {
            "context": "\n".join(context_parts) if context_parts else "",
            "conflicts": "\n".join(conflict_parts) if conflict_parts else ""
        }

# Global instance
rag_service = RAGService()
