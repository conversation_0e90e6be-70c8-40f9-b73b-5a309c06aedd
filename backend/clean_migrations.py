#!/usr/bin/env python3
"""
Clean up existing migrations and create a fresh initial migration

This script:
1. Backs up existing migrations
2. Removes all migration files
3. Creates a new initial migration from current models

Usage:
    python clean_migrations.py
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from alembic.config import Config
from alembic import command

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def backup_migrations():
    """Backup existing migrations."""
    versions_dir = backend_dir / "alembic" / "versions"
    if not versions_dir.exists():
        print("No migrations directory found.")
        return True
    
    # Create backup directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = backend_dir / f"migrations_backup_{timestamp}"
    
    try:
        shutil.copytree(versions_dir, backup_dir)
        print(f"✅ Migrations backed up to: {backup_dir}")
        return True
    except Exception as e:
        print(f"❌ Error backing up migrations: {e}")
        return False

def clean_migrations():
    """Remove all existing migration files."""
    versions_dir = backend_dir / "alembic" / "versions"
    if not versions_dir.exists():
        print("No migrations directory found.")
        return True
    
    try:
        # Remove all .py files except __init__.py
        for file_path in versions_dir.glob("*.py"):
            if file_path.name != "__init__.py":
                file_path.unlink()
                print(f"Removed: {file_path.name}")
        
        # Remove __pycache__ directory
        pycache_dir = versions_dir / "__pycache__"
        if pycache_dir.exists():
            shutil.rmtree(pycache_dir)
            print("Removed: __pycache__ directory")
        
        print("✅ All migration files cleaned up.")
        return True
    except Exception as e:
        print(f"❌ Error cleaning migrations: {e}")
        return False

def create_initial_migration():
    """Create a new initial migration."""
    try:
        alembic_ini = backend_dir / "alembic.ini"
        if not alembic_ini.exists():
            print("❌ Alembic configuration not found. Please run 'alembic init alembic' first.")
            return False
        
        # Create alembic config
        alembic_cfg = Config(str(alembic_ini))
        
        # Create initial migration
        print("Creating initial migration...")
        command.revision(alembic_cfg, autogenerate=True, message="Initial migration")
        print("✅ Initial migration created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating initial migration: {e}")
        return False

def main():
    print("=" * 60)
    print("IntelliTest Migration Cleanup")
    print("=" * 60)
    print()
    
    # Step 1: Backup existing migrations
    print("Step 1: Backing up existing migrations...")
    if not backup_migrations():
        print("❌ Backup failed!")
        sys.exit(1)
    
    # Step 2: Clean up migrations
    print("\nStep 2: Cleaning up existing migrations...")
    if not clean_migrations():
        print("❌ Cleanup failed!")
        sys.exit(1)
    
    # Step 3: Create initial migration
    print("\nStep 3: Creating initial migration...")
    if not create_initial_migration():
        print("❌ Initial migration creation failed!")
        sys.exit(1)
    
    print()
    print("=" * 60)
    print("✅ Migration cleanup completed successfully!")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Review the generated initial migration file")
    print("2. Run 'python init_database.py' to set up a fresh database")
    print("3. For future changes, use 'alembic revision --autogenerate -m \"description\"'")

if __name__ == "__main__":
    main()
