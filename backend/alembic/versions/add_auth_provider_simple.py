"""add auth provider column simple

Revision ID: add_auth_provider_simple
Revises: update_metadata_schema
Create Date: 2025-07-28 04:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_auth_provider_simple'
down_revision = 'update_metadata_schema'
branch_labels = None
depends_on = None


def upgrade():
    # Add auth_provider column to users table with default value
    op.add_column('users', sa.Column('auth_provider', sa.String(), nullable=False, server_default='email'))


def downgrade():
    # Remove auth_provider column
    op.drop_column('users', 'auth_provider')
