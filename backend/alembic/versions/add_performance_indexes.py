"""Add performance indexes

Revision ID: add_performance_indexes
Revises: 
Create Date: 2025-01-26 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_performance_indexes'
down_revision = '5c93c9375862'
depends_on = None


def upgrade():
    """Add performance indexes for common query patterns."""
    
    # 1. ProjectMember indexes for membership queries
    # Composite index for project membership lookups (project_id, user_id)
    op.create_index(
        'idx_project_members_project_user', 
        'project_members', 
        ['project_id', 'user_id']
    )
    
    # Index for user's projects lookup
    op.create_index(
        'idx_project_members_user_id', 
        'project_members', 
        ['user_id']
    )
    
    # Index for role-based queries
    op.create_index(
        'idx_project_members_role', 
        'project_members', 
        ['role']
    )
    
    # 2. Requirement indexes for project-based queries
    # Composite index for project requirements with creation date ordering
    op.create_index(
        'idx_requirements_project_created', 
        'requirements', 
        ['project_id', 'created_at']
    )
    
    # Index for created_by user lookups
    op.create_index(
        'idx_requirements_created_by', 
        'requirements', 
        ['created_by']
    )
    
    # 3. TestCase indexes for requirement-based queries
    # Index for requirement test cases
    op.create_index(
        'idx_test_cases_requirement_id', 
        'test_cases', 
        ['requirement_id']
    )
    
    # Composite index for requirement test cases with creation date
    op.create_index(
        'idx_test_cases_requirement_created', 
        'test_cases', 
        ['requirement_id', 'created_at']
    )
    
    # 4. RequirementTag indexes for tag-based queries
    # Index for requirement tags lookup
    op.create_index(
        'idx_requirement_tags_requirement_id', 
        'requirement_tags', 
        ['requirement_id']
    )
    
    # Index for tag requirements lookup
    op.create_index(
        'idx_requirement_tags_tag_id', 
        'requirement_tags', 
        ['tag_id']
    )
    
    # 5. ProjectVariable indexes for project-based queries
    # Composite index for project variables lookup
    op.create_index(
        'idx_project_variables_project_key', 
        'project_variables', 
        ['project_id', 'key']
    )
    
    # 6. CodeGenerationSession indexes for session-based queries
    # Index for project code generation sessions
    op.create_index(
        'idx_code_generation_sessions_project_id', 
        'code_generation_sessions', 
        ['project_id']
    )
    
    # Index for user code generation sessions
    op.create_index(
        'idx_code_generation_sessions_user_id', 
        'code_generation_sessions', 
        ['user_id']
    )
    
    # Index for requirement code generation sessions
    op.create_index(
        'idx_code_generation_sessions_requirement_id', 
        'code_generation_sessions', 
        ['requirement_id']
    )
    
    # Index for status-based queries
    op.create_index(
        'idx_code_generation_sessions_status', 
        'code_generation_sessions', 
        ['status']
    )
    
    # Composite index for project sessions with creation date
    op.create_index(
        'idx_code_generation_sessions_project_created', 
        'code_generation_sessions', 
        ['project_id', 'created_at']
    )
    
    # 7. DateTime indexes for common time-based queries
    # Index for projects by creation date
    op.create_index(
        'idx_projects_created_at', 
        'projects', 
        ['created_at']
    )
    
    # Index for requirements by creation date
    op.create_index(
        'idx_requirements_created_at', 
        'requirements', 
        ['created_at']
    )
    
    # Index for test cases by creation date
    op.create_index(
        'idx_test_cases_created_at', 
        'test_cases', 
        ['created_at']
    )
    
    # 8. User activity indexes
    # Index for user creation date
    op.create_index(
        'idx_users_created_at', 
        'users', 
        ['created_at']
    )
    
    # Index for active users
    op.create_index(
        'idx_users_is_active', 
        'users', 
        ['is_active']
    )


def downgrade():
    """Remove performance indexes."""
    
    # Remove all indexes in reverse order
    op.drop_index('idx_users_is_active', table_name='users')
    op.drop_index('idx_users_created_at', table_name='users')
    op.drop_index('idx_test_cases_created_at', table_name='test_cases')
    op.drop_index('idx_requirements_created_at', table_name='requirements')
    op.drop_index('idx_projects_created_at', table_name='projects')
    op.drop_index('idx_code_generation_sessions_project_created', table_name='code_generation_sessions')
    op.drop_index('idx_code_generation_sessions_status', table_name='code_generation_sessions')
    op.drop_index('idx_code_generation_sessions_requirement_id', table_name='code_generation_sessions')
    op.drop_index('idx_code_generation_sessions_user_id', table_name='code_generation_sessions')
    op.drop_index('idx_code_generation_sessions_project_id', table_name='code_generation_sessions')
    op.drop_index('idx_project_variables_project_key', table_name='project_variables')
    op.drop_index('idx_requirement_tags_tag_id', table_name='requirement_tags')
    op.drop_index('idx_requirement_tags_requirement_id', table_name='requirement_tags')
    op.drop_index('idx_test_cases_requirement_created', table_name='test_cases')
    op.drop_index('idx_test_cases_requirement_id', table_name='test_cases')
    op.drop_index('idx_requirements_created_by', table_name='requirements')
    op.drop_index('idx_requirements_project_created', table_name='requirements')
    op.drop_index('idx_project_members_role', table_name='project_members')
    op.drop_index('idx_project_members_user_id', table_name='project_members')
    op.drop_index('idx_project_members_project_user', table_name='project_members')
