"""merge heads

Revision ID: 283ef98c4f94
Revises: add_performance_indexes, add_refresh_tokens
Create Date: 2025-07-27 19:57:34.908290

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '283ef98c4f94'
down_revision: Union[str, None] = ('add_performance_indexes', 'add_refresh_tokens')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
