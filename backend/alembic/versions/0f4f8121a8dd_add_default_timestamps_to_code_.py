"""add_default_timestamps_to_code_generation_sessions

Revision ID: 0f4f8121a8dd
Revises: b4b563ec218d
Create Date: 2025-07-23 02:02:55.033625

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0f4f8121a8dd'
down_revision: Union[str, None] = 'b4b563ec218d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add default value for created_at column in code_generation_sessions
    op.execute("ALTER TABLE code_generation_sessions ALTER COLUMN created_at SET DEFAULT NOW()")

    # Add default value for updated_at column (for updates)
    op.execute("ALTER TABLE code_generation_sessions ALTER COLUMN updated_at SET DEFAULT NOW()")

    # Also add defaults for other timestamp tables that exist and need them
    op.execute("ALTER TABLE generated_files ALTER COLUMN created_at SET DEFAULT NOW()")
    op.execute("ALTER TABLE code_generation_logs ALTER COLUMN timestamp SET DEFAULT NOW()")
    op.execute("ALTER TABLE project_code_metadata ALTER COLUMN created_at SET DEFAULT NOW()")
    op.execute("ALTER TABLE project_code_metadata ALTER COLUMN updated_at SET DEFAULT NOW()")


def downgrade() -> None:
    # Remove default values for timestamp columns
    op.execute("ALTER TABLE code_generation_sessions ALTER COLUMN created_at DROP DEFAULT")
    op.execute("ALTER TABLE code_generation_sessions ALTER COLUMN updated_at DROP DEFAULT")
    op.execute("ALTER TABLE generated_files ALTER COLUMN created_at DROP DEFAULT")
    op.execute("ALTER TABLE code_generation_logs ALTER COLUMN timestamp DROP DEFAULT")
    op.execute("ALTER TABLE project_code_metadata ALTER COLUMN created_at DROP DEFAULT")
    op.execute("ALTER TABLE project_code_metadata ALTER COLUMN updated_at DROP DEFAULT")
