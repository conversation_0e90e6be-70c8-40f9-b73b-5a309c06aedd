# IntelliTest Database Migrations 🗄️

> **Alembic Database Migration Management for IntelliTest**

This directory contains all database migration files for the IntelliTest application using Alembic, SQLAlchemy's database migration tool.

## 📋 Overview

The IntelliTest database uses PostgreSQL with SQLAlchemy ORM and Alembic for version control of database schema changes. All migrations are automatically generated and can be applied incrementally.

## 🏗️ Database Schema

### **Core Tables**
- **users**: User accounts with authentication details
- **projects**: Test automation projects with metadata
- **project_members**: Project membership with roles (Owner/Member)
- **requirements**: Project requirements with AI refinement support
- **test_cases**: Generated and manual test cases
- **tags**: Requirement categorization system
- **requirement_tags**: Many-to-many relationship for requirement tagging
- **project_variables**: Dynamic variables for requirement substitution
- **code_generation_sessions**: AI code generation tracking

### **Performance Optimizations**
- **Comprehensive Indexes**: Added for all common query patterns
- **Composite Indexes**: Multi-column indexes for complex queries
- **Foreign Key Indexes**: Optimized relationship lookups
- **DateTime Indexes**: Time-based query optimization

## 🚀 Migration Commands

### **Apply Migrations**

```bash
# Apply all pending migrations
python3 -m alembic upgrade head

# Apply specific migration
python3 -m alembic upgrade <revision_id>

# Apply one migration at a time
python3 -m alembic upgrade +1
```

### **Check Migration Status**

```bash
# Show current migration version
python3 -m alembic current

# Show migration history
python3 -m alembic history

# Show pending migrations
python3 -m alembic heads
```

### **Create New Migrations**

```bash
# Auto-generate migration from model changes
python3 -m alembic revision --autogenerate -m "Description of changes"

# Create empty migration file
python3 -m alembic revision -m "Description of changes"
```

### **Rollback Migrations**

```bash
# Rollback to previous migration
python3 -m alembic downgrade -1

# Rollback to specific migration
python3 -m alembic downgrade <revision_id>

# Rollback all migrations (DANGEROUS)
python3 -m alembic downgrade base
```

## 📁 Migration Files

### **Key Migrations**
- **Initial Schema**: Base tables and relationships
- **Authentication**: User management and JWT support
- **Project Management**: Projects, members, and permissions
- **Requirements System**: Requirements with AI integration
- **Test Case Management**: Test case generation and storage
- **Performance Indexes**: Comprehensive database optimization
- **Code Generation**: AI code generation session tracking

### **Migration Naming Convention**
- Format: `{revision_id}_{description}.py`
- Example: `add_performance_indexes.py`
- Descriptive names for easy identification

## 🔧 Configuration

### **Alembic Configuration** (`alembic.ini`)
- **Database URL**: Configured via environment variables
- **Migration Directory**: `backend/alembic/versions/`
- **Script Template**: Custom template for consistent migrations

### **Environment Variables**
```bash
# Database connection
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_DB=intellitest
POSTGRES_PORT=5432
```

## 🗄️ Database Management

### **Development Environment**

```bash
# Docker environment
docker exec intellitest_backend_dev python3 -m alembic upgrade head

# Manual environment
cd backend
source venv/bin/activate
python3 -m alembic upgrade head
```

### **Production Environment**

```bash
# Docker production
docker exec intellitest_backend_prod python3 -m alembic upgrade head

# Manual production
cd backend
source venv/bin/activate
python3 -m alembic upgrade head
```

### **Database Backup Before Migrations**

```bash
# Create backup before major migrations
pg_dump -U postgres -h localhost intellitest > backup_$(date +%Y%m%d).sql

# Docker environment backup
docker exec intellitest_postgres_dev pg_dump -U postgres intellitest > backup_$(date +%Y%m%d).sql
```

## 🔍 Troubleshooting

### **Common Issues**

1. **Multiple Heads Error**
   ```bash
   # Merge multiple heads
   python3 -m alembic merge heads -m "Merge migrations"
   ```

2. **Migration Conflicts**
   ```bash
   # Check for conflicts
   python3 -m alembic history --verbose

   # Resolve manually and create merge migration
   python3 -m alembic merge -m "Resolve conflicts"
   ```

3. **Database Connection Issues**
   ```bash
   # Verify database connection
   python3 -c "from app.db.base import engine; print(engine.execute('SELECT 1').scalar())"
   ```

4. **Schema Drift**
   ```bash
   # Compare current schema with migrations
   python3 -m alembic check

   # Generate migration to fix drift
   python3 -m alembic revision --autogenerate -m "Fix schema drift"
   ```

## 📊 Performance Considerations

### **Index Strategy**
- **Primary Keys**: Automatic B-tree indexes
- **Foreign Keys**: Explicit indexes for relationship queries
- **Composite Indexes**: Multi-column indexes for complex queries
- **Partial Indexes**: Conditional indexes for specific use cases

### **Query Optimization**
- **Connection Pooling**: SQLAlchemy connection pool configuration
- **Query Analysis**: Use EXPLAIN ANALYZE for slow queries
- **Index Usage**: Monitor index usage with pg_stat_user_indexes

## 🔒 Security Considerations

### **Migration Security**
- **Review All Migrations**: Manual review before applying
- **Backup Before Changes**: Always backup before major migrations
- **Test in Staging**: Apply migrations in staging environment first
- **Rollback Plan**: Always have a rollback strategy

### **Data Protection**
- **Sensitive Data**: Handle sensitive data migrations carefully
- **Encryption**: Ensure encrypted fields remain encrypted
- **Access Control**: Limit migration execution to authorized users

---