# IntelliTest Database Setup Guide

This guide explains how to set up the IntelliTest database from scratch or manage existing installations.

## Prerequisites

1. **PostgreSQL** installed and running
2. **Python 3.11+** with required dependencies
3. **Environment variables** configured (see `.env.example`)

## Quick Start (New Installation)

For first-time setup, run:

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Set up environment variables
cp .env.example .env
# Edit .env with your database credentials

# 3. Initialize database
python init_database.py
```

This will:
- Create the PostgreSQL database
- Create all required tables
- Set up Alembic for future migrations

## Reset Database (Clean Slate)

To completely reset the database:

```bash
python init_database.py --reset
```

This will:
- Drop the existing database
- Create a fresh database
- Recreate all tables
- Reset Alembic version control

## Migration Management

### Clean Up Existing Migrations (One-time)

If you have an existing installation with many migration files:

```bash
# Back up and clean existing migrations
python clean_migrations.py

# Initialize fresh database
python init_database.py --reset
```

### Future Schema Changes

For ongoing development:

```bash
# 1. Make changes to models in app/models/
# 2. Generate migration
alembic revision --autogenerate -m "Description of changes"

# 3. Review the generated migration file
# 4. Apply migration
alembic upgrade head
```

## Database Schema

The current schema includes these tables:

### Core Tables
- `users` - User accounts and authentication
- `projects` - Test automation projects
- `project_members` - Project membership and roles
- `requirements` - Project requirements
- `test_cases` - Generated and manual test cases

### Tagging System
- `tags` - Requirement tags
- `requirement_tags` - Many-to-many relationship for requirement tagging

### Variables and Configuration
- `project_variables` - Dynamic variables for requirement substitution

### Code Generation
- `code_generation_sessions` - AI code generation tracking
- `generated_files` - Generated code files
- `code_generation_logs` - Generation progress and error logs

### Authentication
- `refresh_tokens` - JWT refresh token management

## Troubleshooting

### Database Connection Issues

1. **Check PostgreSQL is running:**
   ```bash
   # On macOS with Homebrew
   brew services start postgresql
   
   # On Ubuntu/Debian
   sudo systemctl start postgresql
   ```

2. **Verify credentials in `.env`:**
   ```env
   POSTGRES_SERVER=localhost
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=your_password
   POSTGRES_DB=intellitest
   POSTGRES_PORT=5432
   ```

3. **Test connection manually:**
   ```bash
   psql -h localhost -U postgres -d intellitest
   ```

### Migration Issues

1. **Alembic version conflicts:**
   ```bash
   # Check current version
   alembic current
   
   # Reset to head
   alembic stamp head
   ```

2. **Manual table creation conflicts:**
   ```bash
   # If tables were created manually, stamp the database
   alembic stamp head
   ```

### Permission Issues

1. **PostgreSQL user permissions:**
   ```sql
   -- Connect as superuser and grant permissions
   GRANT ALL PRIVILEGES ON DATABASE intellitest TO postgres;
   GRANT ALL ON SCHEMA public TO postgres;
   ```

## Environment Configuration

Required environment variables:

```env
# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=intellitest
POSTGRES_PORT=5432

# JWT Configuration
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=30

# External Services
QDRANT_HOST=localhost
QDRANT_PORT=6333
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2:latest

# Email Configuration (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=IntelliTest

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback
```

## Development vs Production

### Development Setup
- Use `init_database.py` for quick setup
- SQLite can be used for local development (modify DATABASE_URL)
- Enable debug logging

### Production Setup
- Use proper PostgreSQL instance
- Set strong SECRET_KEY
- Configure proper CORS origins
- Set up SSL/TLS
- Use environment-specific configuration

## Backup and Restore

### Backup
```bash
pg_dump -h localhost -U postgres intellitest > backup.sql
```

### Restore
```bash
psql -h localhost -U postgres intellitest < backup.sql
```

## Support

If you encounter issues:

1. Check the logs for detailed error messages
2. Verify all prerequisites are installed
3. Ensure environment variables are correctly set
4. Check PostgreSQL service status
5. Review the troubleshooting section above
