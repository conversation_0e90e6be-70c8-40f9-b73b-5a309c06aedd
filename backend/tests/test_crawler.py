#!/usr/bin/env python3
"""
Test script for web crawler functionality
"""
import asyncio
import sys
import logging
from app.services.web_crawler_service import WebCrawlerService, CrawlOptions

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_crawler():
    """Test the web crawler with different websites"""
    
    test_urls = [
        "https://www.google.com",
        "https://www.goodfirms.co/company/vention",
        "https://httpbin.org/html",  # Simple test page
        "https://example.com"  # Basic test page
    ]
    
    options = CrawlOptions(
        max_elements=100,  # Increased limit to test intelligence
        timeout=30000,
        skip_hidden=True,
        wait_for_network_idle=False
    )
    
    for url in test_urls:
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing URL: {url}")
        logger.info(f"{'='*60}")
        
        try:
            async with WebCrawlerService() as crawler:
                result = await crawler.crawl(
                    url=url,
                    is_login_needed=False,
                    options=options
                )
                
                logger.info(f"✅ SUCCESS: {url}")
                logger.info(f"   Total elements: {result['total_elements']}")
                logger.info(f"   URL: {result['url']}")
                
                # Show first few elements as sample with more details
                if result['elements']:
                    logger.info("   Sample elements:")
                    for i, element in enumerate(result['elements'][:5]):
                        interactive = "✓" if element.get('is_interactive', False) else "✗"
                        text = element.get('text', 'No text')[:50]
                        logger.info(f"     {i+1}. {element['tag']} [{interactive}] - {text}...")

                    # Count interactive vs non-interactive
                    interactive_count = sum(1 for el in result['elements'] if el.get('is_interactive', False))
                    logger.info(f"   Interactive elements: {interactive_count}/{result['total_elements']}")
                
        except Exception as e:
            logger.error(f"❌ FAILED: {url}")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(test_crawler())
