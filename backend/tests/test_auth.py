import pytest
from fastapi.testclient import Test<PERSON>lient
from app.crud.user import create_user
from app.schemas.user import UserCreate

def test_register_user(client: TestClient):
    """Test user registration."""
    response = client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_register_duplicate_email(client: TestClient, db_session):
    """Test registration with duplicate email."""
    # Create a user first
    user_create = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    create_user(db_session, user_create)
    
    # Try to register with same email
    response = client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User 2"
        }
    )
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]

def test_login_user(client: TestClient, db_session):
    """Test user login."""
    # Create a user first
    user_create = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    create_user(db_session, user_create)
    
    # Login
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_login_invalid_credentials(client: TestClient):
    """Test login with invalid credentials."""
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
    )
    assert response.status_code == 401
    assert "Incorrect email or password" in response.json()["detail"]

def test_get_current_user(client: TestClient, db_session):
    """Test getting current user info."""
    # Create and login user
    user_create = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    user = create_user(db_session, user_create)
    
    # Login to get token
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
    )
    token = login_response.json()["access_token"]

    # Get current user
    response = client.get(
        "/api/v1/auth/me",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["full_name"] == "Test User"

def test_get_current_user_invalid_token(client: TestClient):
    """Test getting current user with invalid token."""
    response = client.get(
        "/api/v1/auth/me",
        headers={"Authorization": "Bearer invalid_token"}
    )
    assert response.status_code == 401
