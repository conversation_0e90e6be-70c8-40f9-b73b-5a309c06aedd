import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.project import Project, AutomationFramework, ProgrammingLanguage
from app.models.requirement import Requirement
from app.models.test_case import TestCase
from app.models.code_generation import CodeGenerationSession, CodeGenerationStatus, CodeGenerationType
from app.services.git_service import GitRepositoryService
from app.services.code_generation_service import CodeGenerationService
from app.services.code_embedding_service import CodeEmbeddingService
from app.services.file_management_service import FileManagementService
from app.crud.code_generation import create_code_generation_session
from tests.conftest import create_test_user_project_and_login

class TestGitRepositoryService:
    """Test cases for Git Repository Service."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.git_service = GitRepositoryService(str(self.temp_dir))
    
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_get_project_path(self):
        """Test getting project path."""
        project_name = "test_project"
        expected_path = self.temp_dir / project_name
        
        result = self.git_service.get_project_path(project_name)
        assert result == expected_path
    
    def test_get_repo_path(self):
        """Test getting repository path."""
        project_name = "test_project"
        expected_path = self.temp_dir / project_name / "repo"
        
        result = self.git_service.get_repo_path(project_name)
        assert result == expected_path
    
    def test_get_generated_path(self):
        """Test getting generated code path."""
        project_name = "test_project"
        requirement_name = "login_feature"
        expected_path = self.temp_dir / project_name / "repo" / "generated" / requirement_name
        
        result = self.git_service.get_generated_path(project_name, requirement_name)
        assert result == expected_path
    
    def test_is_empty_repository_with_empty_dir(self):
        """Test empty repository detection with empty directory."""
        project_path = self.temp_dir / "empty_project" / "repo"
        project_path.mkdir(parents=True)
        
        result = self.git_service._is_empty_repository(project_path)
        assert result is True
    
    def test_is_empty_repository_with_readme_only(self):
        """Test empty repository detection with only README."""
        project_path = self.temp_dir / "readme_project" / "repo"
        project_path.mkdir(parents=True)
        
        readme_file = project_path / "README.md"
        readme_file.write_text("# Test Project")
        
        result = self.git_service._is_empty_repository(project_path)
        assert result is True
    
    def test_is_empty_repository_with_code_files(self):
        """Test empty repository detection with code files."""
        project_path = self.temp_dir / "code_project" / "repo"
        project_path.mkdir(parents=True)
        
        code_file = project_path / "main.py"
        code_file.write_text("print('Hello World')")
        
        result = self.git_service._is_empty_repository(project_path)
        assert result is False
    
    def test_write_and_read_file_content(self):
        """Test writing and reading file content."""
        project_name = "test_project"
        file_path = "test_file.py"
        content = "def test_function():\n    pass"
        
        # Create project directory
        self.git_service.get_repo_path(project_name).mkdir(parents=True)
        
        # Write file
        result = self.git_service.write_file_content(project_name, file_path, content)
        assert result is True
        
        # Read file
        read_content = self.git_service.read_file_content(project_name, file_path)
        assert read_content == content
    
    def test_get_repository_files(self):
        """Test getting repository files list."""
        project_name = "test_project"
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Create test files
        (repo_path / "main.py").write_text("print('main')")
        (repo_path / "test_main.py").write_text("def test_main(): pass")
        (repo_path / "README.md").write_text("# Project")
        
        files = self.git_service.get_repository_files(project_name)
        
        assert len(files) == 3
        file_paths = [f['path'] for f in files]
        assert 'main.py' in file_paths
        assert 'test_main.py' in file_paths
        assert 'README.md' in file_paths
    
    @patch('git.Repo.clone_from')
    @patch('git.Repo')
    async def test_clone_repository_success(self, mock_repo_class, mock_clone_from):
        """Test successful repository cloning."""
        # Mock repository
        mock_repo = Mock()
        mock_repo.remotes = []
        mock_repo.active_branch = None
        mock_repo.head.commit = None
        mock_clone_from.return_value = mock_repo
        
        git_url = "https://github.com/test/repo.git"
        project_name = "test_project"
        
        result = await self.git_service.clone_repository(git_url, project_name)
        
        assert result["success"] is True
        assert "repo_path" in result
        assert "is_empty" in result
        mock_clone_from.assert_called_once()
    
    @patch('git.Repo.clone_from')
    async def test_clone_repository_failure(self, mock_clone_from):
        """Test repository cloning failure."""
        mock_clone_from.side_effect = Exception("Clone failed")
        
        git_url = "https://github.com/test/invalid-repo.git"
        project_name = "test_project"
        
        result = await self.git_service.clone_repository(git_url, project_name)
        
        assert result["success"] is False
        assert "error" in result

class TestCodeGenerationService:
    """Test cases for Code Generation Service."""
    
    def setup_method(self):
        """Set up test environment."""
        self.code_gen_service = CodeGenerationService()
    
    def test_sanitize_class_name(self):
        """Test class name sanitization."""
        test_cases = [
            ("User Login", "UserLogin"),
            ("user-login", "UserLogin"),
            ("user_login", "UserLogin"),
            ("123user login", "UserLogin"),
            ("user@login#test", "UserLoginTest"),
            ("", ""),
        ]
        
        for input_name, expected in test_cases:
            result = self.code_gen_service._sanitize_class_name(input_name)
            assert result == expected
    
    def test_filter_relevant_elements(self):
        """Test filtering of relevant page elements."""
        page_elements = {
            "elements": [
                {
                    "tag_name": "input",
                    "type": "text",
                    "text": "Username",
                    "css_selector": "#username",
                    "xpath": "//input[@id='username']",
                    "clickable": False,
                    "visible": True
                },
                {
                    "tag_name": "button",
                    "type": "submit",
                    "text": "Login",
                    "css_selector": "#login-btn",
                    "xpath": "//button[@id='login-btn']",
                    "clickable": True,
                    "visible": True
                },
                {
                    "tag_name": "div",
                    "type": "",
                    "text": "Some random text",
                    "css_selector": ".content",
                    "xpath": "//div[@class='content']",
                    "clickable": False,
                    "visible": True
                }
            ]
        }
        
        filtered = self.code_gen_service._filter_relevant_elements(page_elements)
        
        assert len(filtered) == 3  # All elements should be included (input, button, div with text)
        
        # Check that input element is included
        input_element = next((el for el in filtered if el['tag_name'] == 'input'), None)
        assert input_element is not None
        assert input_element['css_selector'] == '#username'
        
        # Check that button element is included
        button_element = next((el for el in filtered if el['tag_name'] == 'button'), None)
        assert button_element is not None
        assert button_element['clickable'] is True
    
    def test_format_test_cases_for_prompt(self):
        """Test formatting test cases for LLM prompt."""
        test_cases = [
            Mock(
                title="Test Login",
                steps="1. Enter username\n2. Enter password\n3. Click login",
                expected_result="User should be logged in",
                notes="Test with valid credentials"
            ),
            Mock(
                title="Test Invalid Login",
                steps="1. Enter invalid username\n2. Click login",
                expected_result="Error message should be displayed",
                notes=None
            )
        ]
        
        formatted = self.code_gen_service._format_test_cases_for_prompt(test_cases)
        
        assert "Test Case 1: Test Login" in formatted
        assert "Test Case 2: Test Invalid Login" in formatted
        assert "Enter username" in formatted
        assert "Error message should be displayed" in formatted

class TestFileManagementService:
    """Test cases for File Management Service."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.file_service = FileManagementService()
        self.file_service.temp_dir = self.temp_dir
    
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_validate_single_file_valid(self):
        """Test validation of a valid file."""
        file_info = {
            "path": "test.py",
            "content": "print('hello')",
            "type": "test_file"
        }
        
        result = self.file_service._validate_single_file(file_info)
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_single_file_invalid_path(self):
        """Test validation of file with invalid path."""
        file_info = {
            "path": "../../../etc/passwd",
            "content": "malicious content",
            "type": "test_file"
        }
        
        result = self.file_service._validate_single_file(file_info)
        
        assert result["valid"] is False
        assert any("security risk" in error.lower() for error in result["errors"])
    
    def test_validate_single_file_missing_content(self):
        """Test validation of file with missing content."""
        file_info = {
            "path": "test.py",
            "type": "test_file"
        }
        
        result = self.file_service._validate_single_file(file_info)
        
        assert result["valid"] is False
        assert any("missing" in error.lower() for error in result["errors"])
    
    def test_should_skip_file(self):
        """Test file skipping logic."""
        skip_cases = [
            ".DS_Store",
            "Thumbs.db",
            "test.pyc",
            "__pycache__",
            "app.log"
        ]
        
        keep_cases = [
            "main.py",
            "test_main.py",
            "README.md",
            "config.json"
        ]
        
        for filename in skip_cases:
            assert self.file_service._should_skip_file(filename) is True
        
        for filename in keep_cases:
            assert self.file_service._should_skip_file(filename) is False
    
    async def test_validate_generated_files(self):
        """Test validation of generated files."""
        generated_files = [
            {
                "path": "valid_test.py",
                "content": "def test(): pass",
                "type": "test_file"
            },
            {
                "path": "../invalid.py",
                "content": "malicious",
                "type": "test_file"
            },
            {
                "path": "missing_content.py",
                "type": "test_file"
            }
        ]
        
        result = await self.file_service.validate_generated_files(generated_files)
        
        assert result["success"] is False
        assert result["valid_files"] == 1
        assert result["invalid_files"] == 2
        assert result["total_files"] == 3

class TestCodeGenerationAPI:
    """Test cases for Code Generation API endpoints."""
    
    def test_start_code_generation_unauthorized(self, client: TestClient, db_session: Session):
        """Test starting code generation without authorization."""
        response = client.post("/code-generation/generate", json={
            "requirement_id": 1,
            "project_id": 1,
            "automation_framework": "selenium",
            "programming_language": "python"
        })
        
        assert response.status_code == 401
    
    def test_start_code_generation_invalid_project(self, client: TestClient, db_session: Session):
        """Test starting code generation with invalid project."""
        user, project, token = create_test_user_project_and_login(client, db_session)
        
        response = client.post("/code-generation/generate", 
            json={
                "requirement_id": 1,
                "project_id": 99999,  # Non-existent project
                "automation_framework": "selenium",
                "programming_language": "python"
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 404
        assert "Project not found" in response.json()["detail"]
    
    def test_start_code_generation_no_test_cases(self, client: TestClient, db_session: Session):
        """Test starting code generation with no test cases."""
        user, project, token = create_test_user_project_and_login(client, db_session)
        
        # Create a requirement without test cases
        requirement = Requirement(
            name="Test Requirement",
            description="A test requirement",
            project_id=project.id,
            created_by=user.id,
            slug="test-requirement"
        )
        db_session.add(requirement)
        db_session.commit()
        
        response = client.post("/code-generation/generate",
            json={
                "requirement_id": requirement.id,
                "project_id": project.id,
                "automation_framework": "selenium",
                "programming_language": "python",
                "git_url": "https://github.com/test/repo.git"
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 400
        assert "No test cases found" in response.json()["detail"]
    
    def test_get_session_status_not_found(self, client: TestClient, db_session: Session):
        """Test getting status of non-existent session."""
        user, project, token = create_test_user_project_and_login(client, db_session)
        
        response = client.get("/code-generation/session/non-existent-session-id",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 404
        assert "session not found" in response.json()["detail"].lower()
    
    def test_get_project_sessions_unauthorized(self, client: TestClient, db_session: Session):
        """Test getting project sessions without authorization."""
        response = client.get("/code-generation/sessions/project/1")
        
        assert response.status_code == 401
    
    def test_get_user_sessions(self, client: TestClient, db_session: Session):
        """Test getting user sessions."""
        user, project, token = create_test_user_project_and_login(client, db_session)
        
        response = client.get("/code-generation/sessions/user",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        assert isinstance(response.json(), list)

class TestCodeGenerationCRUD:
    """Test cases for Code Generation CRUD operations."""
    
    def test_create_code_generation_session(self, db_session: Session):
        """Test creating a code generation session."""
        session_data = {
            "project_id": 1,
            "requirement_id": 1,
            "user_id": 1,
            "session_id": "test-session-123",
            "generation_type": CodeGenerationType.EMPTY_REPO,
            "automation_framework": "selenium",
            "programming_language": "python",
            "git_url": "https://github.com/test/repo.git"
        }
        
        session = create_code_generation_session(db_session, session_data)
        
        assert session.id is not None
        assert session.session_id == "test-session-123"
        assert session.status == CodeGenerationStatus.PENDING
        assert session.generation_type == CodeGenerationType.EMPTY_REPO
        assert session.automation_framework == "selenium"
        assert session.programming_language == "python"

@pytest.mark.asyncio
class TestCodeGenerationIntegration:
    """Integration tests for code generation workflow."""
    
    async def test_empty_repo_code_generation_workflow(self):
        """Test complete workflow for empty repository code generation."""
        # This would be a comprehensive integration test
        # that tests the entire flow from API call to file generation
        pass
    
    async def test_existing_repo_code_generation_workflow(self):
        """Test complete workflow for existing repository code generation."""
        # This would test the flow including code embedding and context generation
        pass
