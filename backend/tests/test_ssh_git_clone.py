#!/usr/bin/env python3
"""
Test script for SSH Git cloning functionality.
This script tests the GitRepositoryService with SSH authentication.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.git_service import GitRepositoryService

def test_ssh_url_detection():
    """Test SSH URL detection."""
    print("Testing SSH URL detection...")

    git_service = GitRepositoryService()

    # Test SSH URLs
    ssh_urls = [
        "**************:user/repo.git",
        "ssh://**************/user/repo.git",
        "**************:user/repo.git"
    ]

    # Test HTTPS URLs
    https_urls = [
        "https://github.com/user/repo.git",
        "https://gitlab.com/user/repo.git"
    ]

    for url in ssh_urls:
        if git_service._is_ssh_url(url):
            print(f"✓ Correctly identified SSH URL: {url}")
        else:
            print(f"✗ Failed to identify SSH URL: {url}")

    for url in https_urls:
        if not git_service._is_ssh_url(url):
            print(f"✓ Correctly identified HTTPS URL: {url}")
        else:
            print(f"✗ Incorrectly identified HTTPS URL as SSH: {url}")

def test_ssh_key_setup():
    """Test SSH key setup functionality."""
    print("\nTesting SSH key setup...")

    git_service = GitRepositoryService()

    # Sample SSH private key (dummy key for testing)
    dummy_ssh_key = """-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn
NhAAAAAwEAAQAAAQEA1234567890abcdefghijklmnopqrstuvwxyz
-----END OPENSSH PRIVATE KEY-----"""

    # Test SSH key setup
    ssh_key_path = git_service._setup_ssh_key(dummy_ssh_key)

    if ssh_key_path and os.path.exists(ssh_key_path):
        print(f"✓ SSH key setup successful: {ssh_key_path}")

        # Check file permissions
        stat_info = os.stat(ssh_key_path)
        permissions = oct(stat_info.st_mode)[-3:]
        if permissions == '600':
            print("✓ SSH key has correct permissions (600)")
        else:
            print(f"✗ SSH key has incorrect permissions: {permissions}")

        # Test SSH wrapper creation
        wrapper_path = git_service._create_ssh_wrapper(ssh_key_path)
        if wrapper_path and os.path.exists(wrapper_path):
            print(f"✓ SSH wrapper script created: {wrapper_path}")

            # Check if wrapper is executable
            if os.access(wrapper_path, os.X_OK):
                print("✓ SSH wrapper script is executable")
            else:
                print("✗ SSH wrapper script is not executable")

            # Clean up wrapper
            os.unlink(wrapper_path)
        else:
            print("✗ Failed to create SSH wrapper script")

        # Clean up SSH key
        git_service._cleanup_ssh_key(ssh_key_path)
        if not os.path.exists(ssh_key_path):
            print("✓ SSH key cleaned up successfully")
        else:
            print("✗ Failed to clean up SSH key")
    else:
        print("✗ SSH key setup failed")

def test_git_clone_with_https():
    """Test git cloning with HTTPS (should work without SSH key)."""
    print("\nTesting HTTPS Git clone...")

    # Use a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        git_service = GitRepositoryService(temp_dir)

        # Test with a public repository
        test_repo = "https://github.com/octocat/Hello-World.git"
        project_name = "test_project"

        try:
            result = git_service.clone_repository(test_repo, project_name)

            if result["success"]:
                print(f"✓ HTTPS clone successful: {result['message']}")
                print(f"  Repository is empty: {result['is_empty']}")
                if result.get('repo_info'):
                    print(f"  Branch: {result['repo_info'].get('branch', 'N/A')}")
            else:
                print(f"✗ HTTPS clone failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"✗ HTTPS clone exception: {e}")

def main():
    """Run all tests."""
    print("SSH Git Clone Functionality Test")
    print("=" * 40)

    try:
        test_ssh_url_detection()
        test_ssh_key_setup()
        test_git_clone_with_https()

        print("\n" + "=" * 40)
        print("Test completed!")
        print("\nNote: SSH clone with real repositories requires valid SSH keys.")
        print("The SSH functionality is ready and will work when provided with valid keys.")

    except Exception as e:
        print(f"\nTest failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()