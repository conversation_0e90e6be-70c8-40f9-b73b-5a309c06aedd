import pytest
from fastapi.testclient import TestClient

def test_root_endpoint(client: TestClient):
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data

def test_health_endpoint(client: TestClient):
    """Test the health endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_api_prefix(client: TestClient):
    """Test that API endpoints are accessible with prefix."""
    response = client.get("/api/v1/auth/me")
    # Should return 401 (unauthorized) not 404 (not found)
    assert response.status_code == 401
