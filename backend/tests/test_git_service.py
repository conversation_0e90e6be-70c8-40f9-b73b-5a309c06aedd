import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import git

from app.services.git_service import GitRepositoryService

class TestGitRepositoryServiceDetailed:
    """Detailed test cases for Git Repository Service."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.git_service = GitRepositoryService(str(self.temp_dir))
    
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test service initialization."""
        assert self.git_service.base_path == self.temp_dir
        assert self.temp_dir.exists()
        assert len(self.git_service.non_code_files) > 0
        assert 'README.md' in self.git_service.non_code_files
        assert '.gitignore' in self.git_service.non_code_files
    
    def test_path_methods(self):
        """Test path generation methods."""
        project_name = "test_project"
        requirement_name = "login_feature"
        
        project_path = self.git_service.get_project_path(project_name)
        repo_path = self.git_service.get_repo_path(project_name)
        generated_path = self.git_service.get_generated_path(project_name, requirement_name)
        
        assert project_path == self.temp_dir / project_name
        assert repo_path == self.temp_dir / project_name / "repo"
        assert generated_path == self.temp_dir / project_name / "repo" / "generated" / requirement_name
    
    def test_create_generated_directory(self):
        """Test creating generated code directory."""
        project_name = "test_project"
        requirement_name = "login_feature"
        
        created_path = self.git_service.create_generated_directory(project_name, requirement_name)
        
        assert created_path.exists()
        assert created_path.is_dir()
        assert created_path == self.git_service.get_generated_path(project_name, requirement_name)
    
    def test_write_file_content_creates_directories(self):
        """Test that writing file content creates necessary directories."""
        project_name = "test_project"
        file_path = "src/main/java/TestClass.java"
        content = "public class TestClass {}"
        
        result = self.git_service.write_file_content(project_name, file_path, content)
        
        assert result is True
        
        full_path = self.git_service.get_repo_path(project_name) / file_path
        assert full_path.exists()
        assert full_path.read_text() == content
        assert full_path.parent.exists()
    
    def test_read_file_content_nonexistent(self):
        """Test reading content of non-existent file."""
        project_name = "test_project"
        file_path = "nonexistent.py"
        
        content = self.git_service.read_file_content(project_name, file_path)
        
        assert content is None
    
    def test_read_file_content_binary_file(self):
        """Test reading content of binary file."""
        project_name = "test_project"
        file_path = "binary_file.bin"
        
        # Create repo directory
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Create binary file
        binary_content = b'\x00\x01\x02\x03\xff\xfe\xfd'
        full_path = repo_path / file_path
        full_path.write_bytes(binary_content)
        
        content = self.git_service.read_file_content(project_name, file_path)
        
        # Should return None or handle gracefully for binary files
        assert content is None or isinstance(content, str)
    
    def test_get_repository_files_with_exclusions(self):
        """Test getting repository files with exclusion patterns."""
        project_name = "test_project"
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Create various types of files
        (repo_path / "main.py").write_text("print('main')")
        (repo_path / "test.pyc").write_text("compiled")
        (repo_path / "__pycache__").mkdir()
        (repo_path / "__pycache__" / "main.cpython-39.pyc").write_text("cached")
        (repo_path / "node_modules").mkdir()
        (repo_path / "node_modules" / "package.json").write_text("{}")
        (repo_path / "app.log").write_text("log content")
        
        files = self.git_service.get_repository_files(project_name)
        
        # Should only include main.py
        assert len(files) == 1
        assert files[0]['path'] == 'main.py'
        assert files[0]['is_text'] is True
    
    def test_is_text_file(self):
        """Test text file detection."""
        test_cases = [
            ("main.py", True),
            ("script.js", True),
            ("component.tsx", True),
            ("Main.java", True),
            ("Program.cs", True),
            ("style.css", True),
            ("config.json", True),
            ("data.yaml", True),
            ("README.md", True),
            ("Dockerfile", True),
            ("image.png", False),
            ("video.mp4", False),
            ("archive.zip", False),
            ("binary.exe", False),
        ]
        
        for filename, expected in test_cases:
            file_path = Path(filename)
            result = self.git_service._is_text_file(file_path)
            assert result == expected, f"Failed for {filename}"
    
    def test_matches_pattern(self):
        """Test pattern matching functionality."""
        test_cases = [
            ("test.pyc", "*.pyc", True),
            ("main.py", "*.pyc", False),
            ("__pycache__", "__pycache__", True),
            ("cache", "__pycache__", False),
            ("node_modules", "node_modules", True),
            ("my_modules", "node_modules", False),
        ]
        
        for name, pattern, expected in test_cases:
            result = self.git_service._matches_pattern(name, pattern)
            assert result == expected, f"Failed for {name} with pattern {pattern}"
    
    def test_is_empty_repository_edge_cases(self):
        """Test empty repository detection with edge cases."""
        project_path = self.temp_dir / "edge_case_project" / "repo"
        project_path.mkdir(parents=True)
        
        # Test with hidden files
        (project_path / ".hidden").write_text("hidden content")
        assert self.git_service._is_empty_repository(project_path) is True
        
        # Test with LICENSE file
        (project_path / "LICENSE").write_text("MIT License")
        assert self.git_service._is_empty_repository(project_path) is True
        
        # Test with .gitignore
        (project_path / ".gitignore").write_text("*.pyc\n__pycache__/")
        assert self.git_service._is_empty_repository(project_path) is True
        
        # Add a code file
        (project_path / "main.py").write_text("print('hello')")
        assert self.git_service._is_empty_repository(project_path) is False
    
    def test_cleanup_project(self):
        """Test project cleanup functionality."""
        project_name = "cleanup_test"
        
        # Create project structure
        project_path = self.git_service.get_project_path(project_name)
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Add some files
        (repo_path / "test.py").write_text("test content")
        (repo_path / "subdir").mkdir()
        (repo_path / "subdir" / "nested.py").write_text("nested content")
        
        assert project_path.exists()
        assert repo_path.exists()
        
        # Cleanup
        result = self.git_service.cleanup_project(project_name)
        
        assert result is True
        assert not project_path.exists()
    
    def test_cleanup_nonexistent_project(self):
        """Test cleanup of non-existent project."""
        result = self.git_service.cleanup_project("nonexistent_project")
        assert result is True  # Should succeed even if project doesn't exist
    
    @patch('git.Repo.clone_from')
    async def test_clone_repository_with_repo_info(self, mock_clone_from):
        """Test repository cloning with detailed repository information."""
        # Create mock repository with detailed info
        mock_commit = Mock()
        mock_commit.hexsha = "abcdef1234567890"
        mock_commit.message = "Initial commit"
        mock_commit.author = "Test Author <<EMAIL>>"
        mock_commit.committed_datetime.isoformat.return_value = "2023-01-01T00:00:00"
        
        mock_branch = Mock()
        mock_branch.name = "main"
        
        mock_remote = Mock()
        mock_remote.url = "https://github.com/test/repo.git"
        
        mock_repo = Mock()
        mock_repo.remotes = [mock_remote]
        mock_repo.active_branch = mock_branch
        mock_repo.head.commit = mock_commit
        mock_repo.iter_commits.return_value = [mock_commit, mock_commit]  # 2 commits
        
        mock_clone_from.return_value = mock_repo
        
        git_url = "https://github.com/test/repo.git"
        project_name = "detailed_test"
        
        result = await self.git_service.clone_repository(git_url, project_name)
        
        assert result["success"] is True
        assert result["repo_info"]["remote_url"] == git_url
        assert result["repo_info"]["branch"] == "main"
        assert result["repo_info"]["commit_count"] == 2
        assert "last_commit" in result["repo_info"]
        assert result["repo_info"]["last_commit"]["hash"] == "abcdef12"
    
    @patch('git.Repo.clone_from')
    async def test_clone_repository_git_command_error(self, mock_clone_from):
        """Test repository cloning with Git command error."""
        mock_clone_from.side_effect = git.GitCommandError("clone", "Repository not found")
        
        git_url = "https://github.com/test/nonexistent.git"
        project_name = "error_test"
        
        result = await self.git_service.clone_repository(git_url, project_name)
        
        assert result["success"] is False
        assert result["error_type"] == "git_error"
        assert "Git command failed" in result["error"]
    
    @patch('git.Repo.clone_from')
    async def test_clone_repository_general_error(self, mock_clone_from):
        """Test repository cloning with general error."""
        mock_clone_from.side_effect = Exception("Network error")
        
        git_url = "https://github.com/test/repo.git"
        project_name = "general_error_test"
        
        result = await self.git_service.clone_repository(git_url, project_name)
        
        assert result["success"] is False
        assert result["error_type"] == "general_error"
        assert "Failed to clone repository" in result["error"]
    
    @patch('git.Repo.clone_from')
    async def test_clone_repository_cleans_existing(self, mock_clone_from):
        """Test that cloning cleans up existing repository."""
        mock_repo = Mock()
        mock_repo.remotes = []
        mock_repo.active_branch = None
        mock_repo.head.commit = None
        mock_clone_from.return_value = mock_repo
        
        project_name = "cleanup_existing"
        repo_path = self.git_service.get_repo_path(project_name)
        
        # Create existing repository
        repo_path.mkdir(parents=True)
        (repo_path / "old_file.py").write_text("old content")
        
        assert repo_path.exists()
        assert (repo_path / "old_file.py").exists()
        
        git_url = "https://github.com/test/repo.git"
        result = await self.git_service.clone_repository(git_url, project_name)
        
        assert result["success"] is True
        # Old file should be gone after cleanup
        assert not (repo_path / "old_file.py").exists()
    
    def test_get_repository_files_empty_directory(self):
        """Test getting files from empty repository."""
        project_name = "empty_repo"
        
        files = self.git_service.get_repository_files(project_name)
        
        assert files == []
    
    def test_get_repository_files_with_custom_exclusions(self):
        """Test getting files with custom exclusion patterns."""
        project_name = "custom_exclusions"
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Create test files
        (repo_path / "main.py").write_text("main")
        (repo_path / "test.py").write_text("test")
        (repo_path / "config.json").write_text("{}")
        (repo_path / "temp.tmp").write_text("temp")
        
        # Test with custom exclusions
        files = self.git_service.get_repository_files(project_name, exclude_patterns=['*.json', '*.tmp'])
        
        file_paths = [f['path'] for f in files]
        assert 'main.py' in file_paths
        assert 'test.py' in file_paths
        assert 'config.json' not in file_paths
        assert 'temp.tmp' not in file_paths
