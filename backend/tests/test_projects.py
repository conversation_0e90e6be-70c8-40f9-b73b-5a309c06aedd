import pytest
from fastapi.testclient import Test<PERSON>lient
from app.crud.user import create_user
from app.schemas.user import User<PERSON>reate

def create_test_user_and_login(client: TestClient, db_session):
    """Helper function to create user and get auth token."""
    user_create = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    user = create_user(db_session, user_create)
    
    login_response = client.post(
        "/api/v1/auth/login",
        json={"email": "<EMAIL>", "password": "testpassword123"}
    )
    token = login_response.json()["access_token"]
    return user, token

def test_create_project(client: TestClient, db_session):
    """Test project creation."""
    user, token = create_test_user_and_login(client, db_session)
    
    response = client.post(
        "/api/v1/projects/",
        json={
            "name": "Test Project",
            "description": "A test project",
            "automation_framework": "selenium",
            "programming_language": "python"
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Project"
    assert data["description"] == "A test project"

def test_get_projects(client: TestClient, db_session):
    """Test getting user projects."""
    user, token = create_test_user_and_login(client, db_session)
    
    # Create a project first
    client.post(
        "/api/v1/projects/",
        json={"name": "Test Project", "description": "A test project"},
        headers={"Authorization": f"Bearer {token}"}
    )

    # Get projects
    response = client.get(
        "/api/v1/projects/",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["name"] == "Test Project"

def test_get_project_detail(client: TestClient, db_session):
    """Test getting project details."""
    user, token = create_test_user_and_login(client, db_session)
    
    # Create a project first
    create_response = client.post(
        "/api/v1/projects/",
        json={"name": "Test Project", "description": "A test project"},
        headers={"Authorization": f"Bearer {token}"}
    )
    project_id = create_response.json()["id"]

    # Get project details
    response = client.get(
        f"/api/v1/projects/{project_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Project"

def test_update_project(client: TestClient, db_session):
    """Test updating a project."""
    user, token = create_test_user_and_login(client, db_session)
    
    # Create a project first
    create_response = client.post(
        "/api/v1/projects/",
        json={"name": "Test Project", "description": "A test project"},
        headers={"Authorization": f"Bearer {token}"}
    )
    project_id = create_response.json()["id"]

    # Update project
    response = client.put(
        f"/api/v1/projects/{project_id}",
        json={"name": "Updated Project", "description": "Updated description"},
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Project"
    assert data["description"] == "Updated description"

def test_delete_project(client: TestClient, db_session):
    """Test deleting a project."""
    user, token = create_test_user_and_login(client, db_session)
    
    # Create a project first
    create_response = client.post(
        "/api/v1/projects/",
        json={"name": "Test Project", "description": "A test project"},
        headers={"Authorization": f"Bearer {token}"}
    )
    project_id = create_response.json()["id"]

    # Delete project
    response = client.delete(
        f"/api/v1/projects/{project_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200

    # Verify project is deleted
    get_response = client.get(
        f"/api/v1/projects/{project_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert get_response.status_code == 404

def test_unauthorized_access(client: TestClient):
    """Test unauthorized access to projects."""
    response = client.get("/api/v1/projects/")
    assert response.status_code == 403  # HTTPBearer returns 403 for missing credentials
