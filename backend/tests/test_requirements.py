import pytest
from fastapi.testclient import Test<PERSON>lient
from app.crud.user import create_user
from app.crud.project import create_project
from app.schemas.user import UserCreate
from app.schemas.project import ProjectCreate

def create_test_user_project_and_login(client: TestClient, db_session):
    """Helper function to create user, project and get auth token."""
    user_create = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    user = create_user(db_session, user_create)
    
    project_create = ProjectCreate(
        name="Test Project",
        description="A test project"
    )
    project = create_project(db_session, project_create, user.id)
    
    login_response = client.post(
        "/api/v1/auth/login",
        json={"email": "<EMAIL>", "password": "testpassword123"}
    )
    token = login_response.json()["access_token"]
    return user, project, token

def test_create_requirement(client: TestClient, db_session):
    """Test requirement creation."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    response = client.post(
        "/api/v1/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement description",
            "project_id": project.id,
            "tag_names": ["ui", "critical"]
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Requirement"
    assert data["description"] == "A test requirement description"

def test_get_requirements(client: TestClient, db_session):
    """Test getting requirements for a project."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    # Create a requirement first
    client.post(
        "/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement",
            "project_id": project.id
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Get requirements
    response = client.get(
        f"/requirements/?project_id={project.id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["name"] == "Test Requirement"

def test_get_requirement_detail(client: TestClient, db_session):
    """Test getting requirement details."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    # Create a requirement first
    create_response = client.post(
        "/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement",
            "project_id": project.id
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    requirement_id = create_response.json()["id"]
    
    # Get requirement details
    response = client.get(
        f"/requirements/{requirement_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Requirement"

def test_update_requirement(client: TestClient, db_session):
    """Test updating a requirement."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    # Create a requirement first
    create_response = client.post(
        "/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement",
            "project_id": project.id
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    requirement_id = create_response.json()["id"]
    
    # Update requirement
    response = client.put(
        f"/requirements/{requirement_id}",
        json={
            "name": "Updated Requirement",
            "description": "Updated description"
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Requirement"

def test_delete_requirement(client: TestClient, db_session):
    """Test deleting a requirement."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    # Create a requirement first
    create_response = client.post(
        "/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement",
            "project_id": project.id
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    requirement_id = create_response.json()["id"]
    
    # Delete requirement
    response = client.delete(
        f"/requirements/{requirement_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    
    # Verify requirement is deleted
    get_response = client.get(
        f"/requirements/{requirement_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert get_response.status_code == 404

def test_create_test_case(client: TestClient, db_session):
    """Test creating a test case for a requirement."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    # Create a requirement first
    create_response = client.post(
        "/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement",
            "project_id": project.id
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    requirement_id = create_response.json()["id"]
    
    # Create test case
    response = client.post(
        f"/requirements/{requirement_id}/test-cases",
        json={
            "title": "Test Case 1",
            "description": "Test case description",
            "steps": "1. Step one\n2. Step two",
            "expected_result": "Expected result"
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Test Case 1"
    assert data["description"] == "Test case description"

def test_get_test_cases(client: TestClient, db_session):
    """Test getting test cases for a requirement."""
    user, project, token = create_test_user_project_and_login(client, db_session)
    
    # Create a requirement first
    create_response = client.post(
        "/requirements/",
        json={
            "name": "Test Requirement",
            "description": "A test requirement",
            "project_id": project.id
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    requirement_id = create_response.json()["id"]
    
    # Create test case
    client.post(
        f"/requirements/{requirement_id}/test-cases",
        json={
            "title": "Test Case 1",
            "description": "Test case description"
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Get test cases
    response = client.get(
        f"/requirements/{requirement_id}/test-cases",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["title"] == "Test Case 1"
