"""
Integration tests for the complete code generation workflow.
These tests verify the end-to-end functionality of the code generation feature.
"""

import pytest
import tempfile
import shutil
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.project import Project, AutomationFramework, ProgrammingLanguage
from app.models.requirement import Requirement
from app.models.test_case import TestCase
from app.models.code_generation import CodeGenerationSession, CodeGenerationStatus, CodeGenerationType
from app.services.git_service import GitRepositoryService
from app.services.code_generation_service import CodeGenerationService
from app.services.file_management_service import FileManagementService
from app.crud.code_generation import create_code_generation_session
from tests.conftest import create_test_user_project_and_login

class TestCodeGenerationIntegration:
    """Integration tests for code generation workflow."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.git_service = GitRepositoryService(str(self.temp_dir))
        self.code_gen_service = CodeGenerationService()
        self.file_service = FileManagementService()
        self.file_service.temp_dir = self.temp_dir / "file_temp"
        self.file_service.temp_dir.mkdir(exist_ok=True)
    
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_git_service_integration(self):
        """Test Git service integration with real file operations."""
        project_name = "integration_test"
        
        # Test project structure creation
        project_path = self.git_service.get_project_path(project_name)
        repo_path = self.git_service.get_repo_path(project_name)
        generated_path = self.git_service.get_generated_path(project_name, "login_feature")
        
        assert not project_path.exists()
        assert not repo_path.exists()
        
        # Create generated directory
        created_path = self.git_service.create_generated_directory(project_name, "login_feature")
        assert created_path.exists()
        assert created_path == generated_path
        
        # Test file operations
        test_files = [
            ("main.py", "def main():\n    print('Hello World')"),
            ("test_main.py", "def test_main():\n    assert True"),
            ("README.md", "# Test Project\n\nThis is a test project."),
            ("config/settings.py", "DEBUG = True\nDATABASE_URL = 'sqlite:///test.db'")
        ]
        
        for file_path, content in test_files:
            success = self.git_service.write_file_content(project_name, file_path, content)
            assert success, f"Failed to write {file_path}"
            
            read_content = self.git_service.read_file_content(project_name, file_path)
            assert read_content == content, f"Content mismatch for {file_path}"
        
        # Test repository files listing
        repo_files = self.git_service.get_repository_files(project_name)
        assert len(repo_files) == 4
        
        file_paths = [f['path'] for f in repo_files]
        assert 'main.py' in file_paths
        assert 'test_main.py' in file_paths
        assert 'README.md' in file_paths
        assert 'config/settings.py' in file_paths
        
        # Test empty repository detection
        is_empty = self.git_service._is_empty_repository(repo_path)
        assert not is_empty  # Should not be empty since we have code files
        
        # Test cleanup
        cleanup_success = self.git_service.cleanup_project(project_name)
        assert cleanup_success
        assert not project_path.exists()
    
    async def test_file_management_integration(self):
        """Test file management service integration."""
        project_name = "file_test"
        
        # Create test repository structure
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Create test files
        test_files = [
            ("src/main.py", "def main(): pass"),
            ("tests/test_main.py", "def test_main(): pass"),
            ("README.md", "# Project"),
            (".gitignore", "*.pyc\n__pycache__/"),
            ("requirements.txt", "fastapi==0.104.1")
        ]
        
        for file_path, content in test_files:
            full_path = repo_path / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content)
        
        # Test project structure retrieval
        with patch.object(self.git_service, 'get_repo_path', return_value=repo_path):
            structure = await self.file_service.get_project_structure(project_name)
        
        assert structure["success"] is True
        assert structure["project_name"] == project_name
        assert "structure" in structure
        
        # Test zip creation
        with patch.object(self.git_service, 'get_repo_path', return_value=repo_path):
            zip_path = await self.file_service.create_project_zip(project_name)
        
        assert zip_path is not None
        assert Path(zip_path).exists()
        assert Path(zip_path).suffix == ".zip"
        
        # Test generated files saving
        generated_files = [
            {
                "path": "generated/test_login.py",
                "content": "def test_login(): pass",
                "type": "test_file",
                "description": "Login test file"
            },
            {
                "path": "generated/login_page.py",
                "content": "class LoginPage: pass",
                "type": "page_object",
                "description": "Login page object"
            }
        ]
        
        with patch.object(self.git_service, 'write_file_content', return_value=True) as mock_write:
            result = await self.file_service.save_generated_files(project_name, generated_files)
        
        assert result["success"] is True
        assert result["total_files"] == 2
        assert len(result["saved_files"]) == 2
        assert len(result["failed_files"]) == 0
        assert mock_write.call_count == 2
    
    def test_code_generation_service_integration(self):
        """Test code generation service integration."""
        # Test class name sanitization
        test_cases = [
            ("User Login Feature", "UserLoginFeature"),
            ("test-case-123", "TestCase123"),
            ("@#$%invalid", "Invalid"),
            ("123numbers", "Numbers")
        ]
        
        for input_name, expected in test_cases:
            result = self.code_gen_service._sanitize_class_name(input_name)
            assert result == expected
        
        # Test page elements filtering
        page_elements = {
            "elements": [
                {
                    "tag_name": "input",
                    "type": "email",
                    "text": "Email",
                    "css_selector": "#email",
                    "xpath": "//input[@id='email']",
                    "clickable": False,
                    "visible": True
                },
                {
                    "tag_name": "input",
                    "type": "password",
                    "text": "Password",
                    "css_selector": "#password",
                    "xpath": "//input[@id='password']",
                    "clickable": False,
                    "visible": True
                },
                {
                    "tag_name": "button",
                    "type": "submit",
                    "text": "Login",
                    "css_selector": "#login-btn",
                    "xpath": "//button[@id='login-btn']",
                    "clickable": True,
                    "visible": True
                },
                {
                    "tag_name": "span",
                    "type": "",
                    "text": "",
                    "css_selector": ".hidden",
                    "xpath": "//span[@class='hidden']",
                    "clickable": False,
                    "visible": False
                }
            ]
        }
        
        filtered = self.code_gen_service._filter_relevant_elements(page_elements)
        
        # Should include input and button elements, but may exclude empty span
        assert len(filtered) >= 3
        
        # Check that important elements are included
        element_types = [el['tag_name'] for el in filtered]
        assert 'input' in element_types
        assert 'button' in element_types
        
        # Test test cases formatting
        mock_test_cases = [
            Mock(
                title="Valid Login Test",
                steps="1. Enter valid email\n2. Enter valid password\n3. Click login",
                expected_result="User should be redirected to dashboard",
                notes="Use <EMAIL>"
            ),
            Mock(
                title="Invalid Login Test",
                steps="1. Enter invalid email\n2. Click login",
                expected_result="Error message should appear",
                notes=None
            )
        ]
        
        formatted = self.code_gen_service._format_test_cases_for_prompt(mock_test_cases)
        
        assert "Test Case 1: Valid Login Test" in formatted
        assert "Test Case 2: Invalid Login Test" in formatted
        assert "Enter valid email" in formatted
        assert "Error message should appear" in formatted
        assert "<EMAIL>" in formatted
    
    async def test_file_validation_integration(self):
        """Test file validation integration."""
        # Test valid files
        valid_files = [
            {
                "path": "tests/test_login.py",
                "content": "def test_login():\n    assert True",
                "type": "test_file"
            },
            {
                "path": "pages/login_page.py",
                "content": "class LoginPage:\n    pass",
                "type": "page_object"
            }
        ]
        
        result = await self.file_service.validate_generated_files(valid_files)
        
        assert result["success"] is True
        assert result["valid_files"] == 2
        assert result["invalid_files"] == 0
        
        # Test invalid files
        invalid_files = [
            {
                "path": "../../../etc/passwd",  # Security risk
                "content": "malicious content",
                "type": "malicious"
            },
            {
                "path": "missing_content.py",  # Missing content
                "type": "test_file"
            },
            {
                "path": "x" * 300,  # Path too long
                "content": "content",
                "type": "test_file"
            }
        ]
        
        result = await self.file_service.validate_generated_files(invalid_files)
        
        assert result["success"] is False
        assert result["valid_files"] == 0
        assert result["invalid_files"] == 3
    
    def test_repository_analysis_integration(self):
        """Test repository analysis functionality."""
        project_name = "analysis_test"
        repo_path = self.git_service.get_repo_path(project_name)
        repo_path.mkdir(parents=True)
        
        # Create a realistic project structure
        project_files = [
            # Python files
            ("main.py", "from fastapi import FastAPI\napp = FastAPI()"),
            ("models/user.py", "class User:\n    pass"),
            ("tests/test_user.py", "def test_user():\n    pass"),
            
            # Config files
            ("config.json", '{"debug": true}'),
            ("requirements.txt", "fastapi==0.104.1\npytest==7.4.3"),
            
            # Documentation
            ("README.md", "# Project\nThis is a test project"),
            ("docs/api.md", "# API Documentation"),
            
            # Git files
            (".gitignore", "*.pyc\n__pycache__/"),
            
            # Build artifacts (should be excluded)
            ("__pycache__/main.cpython-39.pyc", "compiled"),
            ("build/dist.tar.gz", "binary"),
        ]
        
        for file_path, content in project_files:
            full_path = repo_path / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content)
        
        # Test file listing with exclusions
        repo_files = self.git_service.get_repository_files(project_name)
        
        # Should exclude __pycache__ and build directories
        file_paths = [f['path'] for f in repo_files]
        assert 'main.py' in file_paths
        assert 'models/user.py' in file_paths
        assert 'tests/test_user.py' in file_paths
        assert 'config.json' in file_paths
        assert 'README.md' in file_paths
        
        # Should exclude build artifacts
        assert '__pycache__/main.cpython-39.pyc' not in file_paths
        assert 'build/dist.tar.gz' not in file_paths
        
        # Test empty repository detection
        is_empty = self.git_service._is_empty_repository(repo_path)
        assert not is_empty  # Has code files
        
        # Test with empty repository
        empty_repo_path = self.git_service.get_repo_path("empty_project")
        empty_repo_path.mkdir(parents=True)
        (empty_repo_path / "README.md").write_text("# Empty Project")
        (empty_repo_path / ".gitignore").write_text("*.log")
        
        is_empty = self.git_service._is_empty_repository(empty_repo_path)
        assert is_empty  # Only has non-code files
    
    async def test_cleanup_integration(self):
        """Test cleanup functionality integration."""
        # Create multiple projects
        projects = ["project1", "project2", "project3"]
        
        for project in projects:
            repo_path = self.git_service.get_repo_path(project)
            repo_path.mkdir(parents=True)
            (repo_path / "main.py").write_text("print('hello')")
        
        # Verify all projects exist
        for project in projects:
            assert self.git_service.get_project_path(project).exists()
        
        # Test individual cleanup
        success = self.git_service.cleanup_project("project1")
        assert success
        assert not self.git_service.get_project_path("project1").exists()
        assert self.git_service.get_project_path("project2").exists()
        assert self.git_service.get_project_path("project3").exists()
        
        # Test cleanup of remaining projects
        for project in ["project2", "project3"]:
            success = self.git_service.cleanup_project(project)
            assert success
            assert not self.git_service.get_project_path(project).exists()
        
        # Test temp file cleanup
        import time
        
        # Create temp files with different ages
        old_file = self.file_service.temp_dir / "old_file.txt"
        new_file = self.file_service.temp_dir / "new_file.txt"
        
        old_file.write_text("old content")
        new_file.write_text("new content")
        
        # Make old file appear old (25 hours ago)
        old_time = time.time() - (25 * 3600)
        old_file.touch(times=(old_time, old_time))
        
        # Cleanup files older than 24 hours
        result = await self.file_service.cleanup_temp_files(older_than_hours=24)
        
        assert result["success"] is True
        assert result["removed_files"] == 1
        assert not old_file.exists()
        assert new_file.exists()

@pytest.mark.asyncio
class TestCodeGenerationWorkflow:
    """Test the complete code generation workflow."""
    
    async def test_mock_empty_repo_workflow(self):
        """Test the complete workflow for empty repository (mocked)."""
        # This test mocks external dependencies to test the workflow logic
        
        with patch('app.services.git_service.git_service') as mock_git_service, \
             patch('app.services.code_generation_service.llm_service') as mock_llm_service, \
             patch('app.services.file_management_service.file_management_service') as mock_file_service:
            
            # Mock git service responses
            mock_git_service.clone_repository.return_value = {
                "success": True,
                "is_empty": True,
                "repo_path": "/tmp/test_repo",
                "repo_info": {}
            }
            
            # Mock LLM service responses
            mock_llm_service._make_request.return_value = "def test_function(): pass"
            
            # Mock file service responses
            mock_file_service.save_generated_files.return_value = {
                "success": True,
                "saved_files": [{"path": "test.py", "type": "test_file"}],
                "failed_files": [],
                "total_files": 1
            }
            mock_file_service.create_project_zip.return_value = "/tmp/project.zip"
            
            # Test the workflow components
            from app.services.code_generation_service import CodeGenerationService
            from app.models.project import AutomationFramework, ProgrammingLanguage
            
            service = CodeGenerationService()
            
            # Mock test cases
            mock_test_cases = [
                Mock(
                    title="Test Login",
                    steps="1. Enter credentials\n2. Click login",
                    expected_result="User logged in",
                    notes="Valid credentials test"
                )
            ]
            
            # Mock page elements
            page_elements = {
                "elements": [
                    {
                        "tag_name": "input",
                        "type": "email",
                        "css_selector": "#email",
                        "xpath": "//input[@id='email']"
                    }
                ]
            }
            
            # Test code generation for empty repo
            generated_files = []
            async for progress in service.generate_code_for_empty_repo(
                project_name="test_project",
                requirement_name="login_feature",
                test_cases=mock_test_cases,
                page_elements=page_elements,
                automation_framework=AutomationFramework.SELENIUM,
                programming_language=ProgrammingLanguage.PYTHON
            ):
                if progress["type"] == "file":
                    generated_files.append(progress["data"])
                elif progress["type"] == "complete":
                    break
                elif progress["type"] == "error":
                    pytest.fail(f"Code generation failed: {progress['message']}")
            
            # Verify that files were generated
            assert len(generated_files) > 0
            
            # Verify that the workflow completed successfully
            assert any(progress["type"] == "complete" for progress in [])

def run_integration_tests():
    """Run integration tests manually."""
    print("Running Code Generation Integration Tests...")
    
    # Test Git Service
    print("\n1. Testing Git Service Integration...")
    test = TestCodeGenerationIntegration()
    test.setup_method()
    try:
        test.test_git_service_integration()
        print("   ✓ Git Service Integration - PASSED")
    except Exception as e:
        print(f"   ✗ Git Service Integration - FAILED: {e}")
    finally:
        test.teardown_method()
    
    # Test File Management
    print("\n2. Testing File Management Integration...")
    test = TestCodeGenerationIntegration()
    test.setup_method()
    try:
        asyncio.run(test.test_file_management_integration())
        print("   ✓ File Management Integration - PASSED")
    except Exception as e:
        print(f"   ✗ File Management Integration - FAILED: {e}")
    finally:
        test.teardown_method()
    
    # Test Code Generation Service
    print("\n3. Testing Code Generation Service Integration...")
    test = TestCodeGenerationIntegration()
    test.setup_method()
    try:
        test.test_code_generation_service_integration()
        print("   ✓ Code Generation Service Integration - PASSED")
    except Exception as e:
        print(f"   ✗ Code Generation Service Integration - FAILED: {e}")
    finally:
        test.teardown_method()
    
    print("\nIntegration tests completed!")

if __name__ == "__main__":
    run_integration_tests()
