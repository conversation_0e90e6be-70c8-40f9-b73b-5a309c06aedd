import pytest
import tempfile
import shutil
import zipfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import time

from app.services.file_management_service import FileManagementService
from app.services.git_service import git_service

class TestFileManagementService:
    """Test cases for File Management Service."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.file_service = FileManagementService()
        self.file_service.temp_dir = self.temp_dir
    
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test service initialization."""
        service = FileManagementService()
        assert service.temp_dir.exists()
    
    @patch.object(git_service, 'write_file_content')
    async def test_save_generated_files_success(self, mock_write):
        """Test successful saving of generated files."""
        mock_write.return_value = True
        
        generated_files = [
            {
                "path": "test1.py",
                "content": "def test1(): pass",
                "type": "test_file",
                "description": "Test file 1"
            },
            {
                "path": "test2.py",
                "content": "def test2(): pass",
                "type": "test_file",
                "description": "Test file 2"
            }
        ]
        
        result = await self.file_service.save_generated_files("test_project", generated_files)
        
        assert result["success"] is True
        assert result["total_files"] == 2
        assert len(result["saved_files"]) == 2
        assert len(result["failed_files"]) == 0
        
        # Check that git_service.write_file_content was called correctly
        assert mock_write.call_count == 2
        mock_write.assert_any_call("test_project", "test1.py", "def test1(): pass")
        mock_write.assert_any_call("test_project", "test2.py", "def test2(): pass")
    
    @patch.object(git_service, 'write_file_content')
    async def test_save_generated_files_partial_failure(self, mock_write):
        """Test saving files with partial failures."""
        # First call succeeds, second fails
        mock_write.side_effect = [True, False]
        
        generated_files = [
            {
                "path": "success.py",
                "content": "def success(): pass",
                "type": "test_file"
            },
            {
                "path": "failure.py",
                "content": "def failure(): pass",
                "type": "test_file"
            }
        ]
        
        result = await self.file_service.save_generated_files("test_project", generated_files)
        
        assert result["success"] is False
        assert result["total_files"] == 2
        assert len(result["saved_files"]) == 1
        assert len(result["failed_files"]) == 1
        assert result["saved_files"][0]["path"] == "success.py"
        assert result["failed_files"][0]["path"] == "failure.py"
    
    async def test_save_generated_files_invalid_data(self):
        """Test saving files with invalid data."""
        generated_files = [
            {
                "path": "valid.py",
                "content": "def valid(): pass",
                "type": "test_file"
            },
            {
                # Missing content
                "path": "invalid1.py",
                "type": "test_file"
            },
            {
                # Missing path
                "content": "def invalid2(): pass",
                "type": "test_file"
            }
        ]
        
        with patch.object(git_service, 'write_file_content', return_value=True):
            result = await self.file_service.save_generated_files("test_project", generated_files)
        
        assert result["success"] is False
        assert result["total_files"] == 3
        assert len(result["saved_files"]) == 1
        assert len(result["failed_files"]) == 2
    
    def test_should_skip_file(self):
        """Test file skipping logic."""
        skip_cases = [
            ".DS_Store",
            "Thumbs.db",
            "test.tmp",
            "app.log",
            "cache.pyc",
            "compiled.pyo",
            "module.pyd",
            "__pycache__",
            ".pytest_cache",
            ".coverage",
            "package.egg-info"
        ]
        
        keep_cases = [
            "main.py",
            "test_main.py",
            "README.md",
            "config.json",
            "style.css",
            "script.js"
        ]
        
        for filename in skip_cases:
            assert self.file_service._should_skip_file(filename) is True, f"Should skip {filename}"
        
        for filename in keep_cases:
            assert self.file_service._should_skip_file(filename) is False, f"Should keep {filename}"
    
    @patch.object(git_service, 'get_repo_path')
    async def test_create_project_zip_success(self, mock_get_repo_path):
        """Test successful creation of project zip file."""
        # Create mock repository structure
        mock_repo_path = self.temp_dir / "mock_repo"
        mock_repo_path.mkdir()
        
        # Create test files
        (mock_repo_path / "main.py").write_text("print('main')")
        (mock_repo_path / "test.py").write_text("def test(): pass")
        (mock_repo_path / "README.md").write_text("# Project")
        
        # Create subdirectory
        subdir = mock_repo_path / "src"
        subdir.mkdir()
        (subdir / "module.py").write_text("class Module: pass")
        
        mock_get_repo_path.return_value = mock_repo_path
        
        zip_path = await self.file_service.create_project_zip("test_project")
        
        assert zip_path is not None
        assert Path(zip_path).exists()
        assert Path(zip_path).suffix == ".zip"
        
        # Verify zip contents
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            file_list = zipf.namelist()
            assert "main.py" in file_list
            assert "test.py" in file_list
            assert "README.md" in file_list
            assert "src/module.py" in file_list
    
    @patch.object(git_service, 'get_repo_path')
    async def test_create_project_zip_nonexistent_repo(self, mock_get_repo_path):
        """Test zip creation with non-existent repository."""
        mock_get_repo_path.return_value = Path("/nonexistent/path")
        
        zip_path = await self.file_service.create_project_zip("test_project")
        
        assert zip_path is None
    
    @patch.object(git_service, 'get_repo_path')
    async def test_create_project_zip_excludes_unwanted_files(self, mock_get_repo_path):
        """Test that zip creation excludes unwanted files."""
        mock_repo_path = self.temp_dir / "mock_repo"
        mock_repo_path.mkdir()
        
        # Create files that should be included
        (mock_repo_path / "main.py").write_text("print('main')")
        (mock_repo_path / "README.md").write_text("# Project")
        
        # Create files that should be excluded
        (mock_repo_path / ".DS_Store").write_text("system file")
        (mock_repo_path / "Thumbs.db").write_text("windows file")
        (mock_repo_path / "temp.tmp").write_text("temp file")
        (mock_repo_path / "app.log").write_text("log file")
        (mock_repo_path / "cache.pyc").write_text("compiled python")
        
        # Create .git directory (should be excluded by default)
        git_dir = mock_repo_path / ".git"
        git_dir.mkdir()
        (git_dir / "config").write_text("git config")
        
        mock_get_repo_path.return_value = mock_repo_path
        
        zip_path = await self.file_service.create_project_zip("test_project", include_git=False)
        
        assert zip_path is not None
        
        # Verify zip contents
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            file_list = zipf.namelist()
            
            # Should include
            assert "main.py" in file_list
            assert "README.md" in file_list
            
            # Should exclude
            assert ".DS_Store" not in file_list
            assert "Thumbs.db" not in file_list
            assert "temp.tmp" not in file_list
            assert "app.log" not in file_list
            assert "cache.pyc" not in file_list
            assert ".git/config" not in file_list
    
    @patch.object(git_service, 'get_repo_path')
    async def test_create_project_zip_include_git(self, mock_get_repo_path):
        """Test zip creation including .git directory."""
        mock_repo_path = self.temp_dir / "mock_repo"
        mock_repo_path.mkdir()
        
        (mock_repo_path / "main.py").write_text("print('main')")
        
        # Create .git directory
        git_dir = mock_repo_path / ".git"
        git_dir.mkdir()
        (git_dir / "config").write_text("git config")
        
        mock_get_repo_path.return_value = mock_repo_path
        
        zip_path = await self.file_service.create_project_zip("test_project", include_git=True)
        
        assert zip_path is not None
        
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            file_list = zipf.namelist()
            assert "main.py" in file_list
            assert ".git/config" in file_list
    
    @patch.object(git_service, 'get_repo_path')
    async def test_get_project_structure(self, mock_get_repo_path):
        """Test getting project structure."""
        mock_repo_path = self.temp_dir / "mock_repo"
        mock_repo_path.mkdir()
        
        # Create test structure
        (mock_repo_path / "main.py").write_text("print('main')")
        (mock_repo_path / "README.md").write_text("# Project")
        
        src_dir = mock_repo_path / "src"
        src_dir.mkdir()
        (src_dir / "module.py").write_text("class Module: pass")
        
        tests_dir = mock_repo_path / "tests"
        tests_dir.mkdir()
        (tests_dir / "test_module.py").write_text("def test_module(): pass")
        
        mock_get_repo_path.return_value = mock_repo_path
        
        result = await self.file_service.get_project_structure("test_project")
        
        assert result["success"] is True
        assert result["project_name"] == "test_project"
        assert "structure" in result
        
        structure = result["structure"]
        assert structure["type"] == "directory"
        assert len(structure["children"]) >= 4  # main.py, README.md, src/, tests/
    
    @patch.object(git_service, 'get_repo_path')
    async def test_get_project_structure_nonexistent(self, mock_get_repo_path):
        """Test getting structure of non-existent project."""
        mock_get_repo_path.return_value = Path("/nonexistent/path")
        
        result = await self.file_service.get_project_structure("test_project")
        
        assert "error" in result
        assert "does not exist" in result["error"]
    
    def test_build_directory_tree_file(self):
        """Test building directory tree for a file."""
        test_file = self.temp_dir / "test.py"
        test_file.write_text("print('test')")
        
        result = self.file_service._build_directory_tree(test_file, self.temp_dir)
        
        assert result["type"] == "file"
        assert result["name"] == "test.py"
        assert result["size"] > 0
        assert result["relative_path"] == "test.py"
    
    def test_build_directory_tree_directory(self):
        """Test building directory tree for a directory."""
        test_dir = self.temp_dir / "test_dir"
        test_dir.mkdir()
        (test_dir / "file1.py").write_text("content1")
        (test_dir / "file2.py").write_text("content2")
        
        result = self.file_service._build_directory_tree(test_dir, self.temp_dir)
        
        assert result["type"] == "directory"
        assert result["name"] == "test_dir"
        assert len(result["children"]) == 2
        assert result["relative_path"] == "test_dir"
    
    def test_build_directory_tree_max_depth(self):
        """Test directory tree building with max depth limit."""
        # Create deep directory structure
        current_dir = self.temp_dir
        for i in range(10):
            current_dir = current_dir / f"level_{i}"
            current_dir.mkdir()
            (current_dir / f"file_{i}.py").write_text(f"content {i}")
        
        result = self.file_service._build_directory_tree(self.temp_dir, self.temp_dir, max_depth=3)
        
        # Should be truncated at max depth
        def check_depth(node, current_depth=0):
            if current_depth >= 3:
                return node.get("truncated", False)
            if node["type"] == "directory" and "children" in node:
                return all(check_depth(child, current_depth + 1) for child in node["children"])
            return True
        
        assert check_depth(result)
    
    async def test_cleanup_temp_files(self):
        """Test cleanup of temporary files."""
        # Create test files with different ages
        old_file = self.temp_dir / "old_file.txt"
        new_file = self.temp_dir / "new_file.txt"
        
        old_file.write_text("old content")
        new_file.write_text("new content")
        
        # Make old file appear old
        old_time = time.time() - (25 * 3600)  # 25 hours ago
        old_file.touch(times=(old_time, old_time))
        
        result = await self.file_service.cleanup_temp_files(older_than_hours=24)
        
        assert result["success"] is True
        assert result["removed_files"] == 1
        assert result["failed_removals"] == 0
        assert not old_file.exists()
        assert new_file.exists()
    
    @patch.object(git_service, 'read_file_content')
    async def test_get_file_content(self, mock_read):
        """Test getting file content."""
        mock_read.return_value = "def test(): pass"
        
        with patch.object(git_service, 'get_repo_path') as mock_get_path:
            mock_repo_path = self.temp_dir / "repo"
            mock_repo_path.mkdir()
            test_file = mock_repo_path / "test.py"
            test_file.write_text("def test(): pass")
            mock_get_path.return_value = mock_repo_path
            
            result = await self.file_service.get_file_content("test_project", "test.py")
        
        assert result is not None
        assert result["path"] == "test.py"
        assert result["content"] == "def test(): pass"
        assert result["size"] > 0
        assert result["lines"] == 1
    
    @patch.object(git_service, 'read_file_content')
    async def test_get_file_content_nonexistent(self, mock_read):
        """Test getting content of non-existent file."""
        mock_read.return_value = None
        
        result = await self.file_service.get_file_content("test_project", "nonexistent.py")
        
        assert result is None
    
    async def test_validate_generated_files(self):
        """Test validation of generated files."""
        files = [
            {
                "path": "valid.py",
                "content": "def valid(): pass",
                "type": "test_file"
            },
            {
                "path": "../invalid.py",  # Security risk
                "content": "malicious",
                "type": "test_file"
            },
            {
                "path": "missing_content.py",  # Missing content
                "type": "test_file"
            },
            {
                "path": "x" * 300,  # Path too long
                "content": "content",
                "type": "test_file"
            }
        ]
        
        result = await self.file_service.validate_generated_files(files)
        
        assert result["success"] is False
        assert result["valid_files"] == 1
        assert result["invalid_files"] == 3
        assert result["total_files"] == 4
    
    def test_validate_single_file_valid(self):
        """Test validation of a valid file."""
        file_info = {
            "path": "test.py",
            "content": "def test(): pass",
            "type": "test_file"
        }
        
        result = self.file_service._validate_single_file(file_info)
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_single_file_security_risk(self):
        """Test validation catches security risks."""
        file_info = {
            "path": "../../../etc/passwd",
            "content": "malicious",
            "type": "test_file"
        }
        
        result = self.file_service._validate_single_file(file_info)
        
        assert result["valid"] is False
        assert any("security risk" in error.lower() for error in result["errors"])
    
    def test_validate_single_file_large_content(self):
        """Test validation of file with large content."""
        large_content = "x" * (11 * 1024 * 1024)  # 11MB
        file_info = {
            "path": "large.py",
            "content": large_content,
            "type": "test_file"
        }
        
        result = self.file_service._validate_single_file(file_info)
        
        assert result["valid"] is False
        assert any("too large" in error.lower() for error in result["errors"])
    
    def test_get_temp_file_path(self):
        """Test getting temporary file path."""
        filename = "test.zip"
        path = self.file_service.get_temp_file_path(filename)
        
        assert path == str(self.temp_dir / filename)
    
    async def test_delete_temp_file(self):
        """Test deleting temporary file."""
        test_file = self.temp_dir / "test.txt"
        test_file.write_text("test content")
        
        assert test_file.exists()
        
        result = await self.file_service.delete_temp_file(str(test_file))
        
        assert result is True
        assert not test_file.exists()
    
    async def test_delete_temp_file_outside_temp_dir(self):
        """Test that files outside temp directory cannot be deleted."""
        outside_file = Path(tempfile.mktemp())
        outside_file.write_text("outside content")
        
        try:
            result = await self.file_service.delete_temp_file(str(outside_file))
            assert result is False
            assert outside_file.exists()  # Should still exist
        finally:
            if outside_file.exists():
                outside_file.unlink()
