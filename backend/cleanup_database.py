#!/usr/bin/env python3
"""
Database cleanup script to remove unused tables and columns

This script helps clean up the database after removing unused models:
1. Drops project_code_metadata table if it exists
2. Drops color column from tags table if it exists

Usage:
    python cleanup_database.py [--dry-run]
    
Options:
    --dry-run: Show what would be done without making changes
"""

import sys
import argparse
from pathlib import Path
from sqlalchemy import create_engine, text, inspect

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings

def check_table_exists(engine, table_name):
    """Check if a table exists in the database."""
    try:
        inspector = inspect(engine)
        return table_name in inspector.get_table_names()
    except Exception as e:
        print(f"Error checking table {table_name}: {e}")
        return False

def check_column_exists(engine, table_name, column_name):
    """Check if a column exists in a table."""
    try:
        inspector = inspect(engine)
        if not check_table_exists(engine, table_name):
            return False
        
        columns = inspector.get_columns(table_name)
        return any(col['name'] == column_name for col in columns)
    except Exception as e:
        print(f"Error checking column {column_name} in {table_name}: {e}")
        return False

def cleanup_database(dry_run=False):
    """Clean up unused tables and columns."""
    try:
        engine = create_engine(settings.DATABASE_URL)
        
        print("=" * 60)
        print("Database Cleanup")
        print("=" * 60)
        print(f"Database URL: {settings.DATABASE_URL}")
        print(f"Mode: {'DRY RUN' if dry_run else 'EXECUTE'}")
        print()
        
        changes_made = False
        
        # Check and drop project_code_metadata table
        if check_table_exists(engine, 'project_code_metadata'):
            print("✓ Found unused table: project_code_metadata")
            if dry_run:
                print("  [DRY RUN] Would drop table: project_code_metadata")
            else:
                with engine.connect() as conn:
                    conn.execute(text("DROP TABLE IF EXISTS project_code_metadata CASCADE"))
                    conn.commit()
                print("  ✅ Dropped table: project_code_metadata")
            changes_made = True
        else:
            print("✓ Table project_code_metadata does not exist (already clean)")
        
        # Check and drop color column from tags table
        if check_column_exists(engine, 'tags', 'color'):
            print("✓ Found unused column: tags.color")
            if dry_run:
                print("  [DRY RUN] Would drop column: tags.color")
            else:
                with engine.connect() as conn:
                    conn.execute(text("ALTER TABLE tags DROP COLUMN IF EXISTS color"))
                    conn.commit()
                print("  ✅ Dropped column: tags.color")
            changes_made = True
        else:
            print("✓ Column tags.color does not exist (already clean)")
        
        print()
        if changes_made:
            if dry_run:
                print("🔍 Dry run completed. Run without --dry-run to apply changes.")
            else:
                print("✅ Database cleanup completed successfully!")
        else:
            print("✅ Database is already clean - no changes needed.")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during database cleanup: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Clean up unused database tables and columns')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be done without making changes')
    args = parser.parse_args()
    
    success = cleanup_database(dry_run=args.dry_run)
    
    if not success:
        sys.exit(1)
    
    if not args.dry_run:
        print()
        print("Next steps:")
        print("1. Restart your backend server")
        print("2. Test login functionality")
        print("3. Verify that all features work correctly")

if __name__ == "__main__":
    main()
