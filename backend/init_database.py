#!/usr/bin/env python3
"""
Complete database initialization script for IntelliTest

This script handles:
1. Database creation
2. Schema initialization using SQLAlchemy models
3. Alembic setup for future migrations

Usage:
    python init_database.py [--reset]
    
Options:
    --reset: Drop existing database and recreate from scratch
"""

import os
import sys
import argparse
from pathlib import Path
from sqlalchemy import create_engine, text, inspect
from alembic.config import Config
from alembic import command

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.db.base import Base, engine
from app.models import (
    user, project, project_member, requirement, test_case, 
    tag, project_variable, code_generation, refresh_token
)

def create_database(reset=False):
    """Create the database if it doesn't exist."""
    # Connect to PostgreSQL server (not to specific database)
    server_url = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/postgres"
    
    try:
        server_engine = create_engine(server_url)
        with server_engine.connect() as conn:
            # Check if database exists
            result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{settings.POSTGRES_DB}'"))
            db_exists = result.fetchone() is not None
            
            if reset and db_exists:
                print(f"Dropping existing database '{settings.POSTGRES_DB}'...")
                # Terminate existing connections
                conn.execute(text(f"""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity
                    WHERE datname = '{settings.POSTGRES_DB}' AND pid <> pg_backend_pid()
                """))
                conn.execute(text("COMMIT"))
                conn.execute(text(f"DROP DATABASE {settings.POSTGRES_DB}"))
                db_exists = False
                print(f"Database '{settings.POSTGRES_DB}' dropped successfully!")
            
            if not db_exists:
                # Create database
                conn.execute(text("COMMIT"))  # End any existing transaction
                conn.execute(text(f"CREATE DATABASE {settings.POSTGRES_DB}"))
                print(f"Database '{settings.POSTGRES_DB}' created successfully!")
            else:
                print(f"Database '{settings.POSTGRES_DB}' already exists.")
                
    except Exception as e:
        print(f"Error creating database: {e}")
        print("Please ensure PostgreSQL is running and credentials are correct.")
        return False
    
    return True

def test_connection():
    """Test connection to the application database."""
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("Database connection successful!")
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False

def create_tables():
    """Create all tables using SQLAlchemy models."""
    try:
        print("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully!")
        return True
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

def check_tables_exist():
    """Check if tables already exist in the database."""
    try:
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        expected_tables = [
            'users', 'projects', 'project_members', 'requirements', 
            'test_cases', 'tags', 'requirement_tags', 'project_variables',
            'code_generation_sessions', 'generated_files', 'code_generation_logs',
            'refresh_tokens'
        ]
        
        missing_tables = [table for table in expected_tables if table not in existing_tables]
        return len(missing_tables) == 0, missing_tables, existing_tables
    except Exception as e:
        print(f"Error checking tables: {e}")
        return False, [], []

def setup_alembic():
    """Initialize Alembic for future migrations."""
    try:
        alembic_dir = backend_dir / "alembic"
        alembic_ini = backend_dir / "alembic.ini"
        
        if not alembic_ini.exists():
            print("Alembic configuration not found. Please run 'alembic init alembic' first.")
            return False
            
        # Create alembic config
        alembic_cfg = Config(str(alembic_ini))
        
        # Check if alembic_version table exists
        inspector = inspect(engine)
        if 'alembic_version' not in inspector.get_table_names():
            print("Setting up Alembic version control...")
            # Stamp the database with the current revision
            command.stamp(alembic_cfg, "head")
            print("Alembic version control initialized!")
        else:
            print("Alembic version control already initialized.")
            
        return True
    except Exception as e:
        print(f"Error setting up Alembic: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Initialize IntelliTest database')
    parser.add_argument('--reset', action='store_true', 
                       help='Drop existing database and recreate from scratch')
    args = parser.parse_args()
    
    print("=" * 60)
    print("IntelliTest Database Initialization")
    print("=" * 60)
    print(f"Database URL: {settings.DATABASE_URL}")
    print()
    
    # Step 1: Create database
    if not create_database(reset=args.reset):
        print("❌ Database creation failed!")
        sys.exit(1)
    
    # Step 2: Test connection
    if not test_connection():
        print("❌ Database connection failed!")
        sys.exit(1)
    
    # Step 3: Check if tables exist
    tables_exist, missing_tables, existing_tables = check_tables_exist()
    
    if tables_exist and not args.reset:
        print("✅ All required tables already exist.")
        print(f"Existing tables: {', '.join(existing_tables)}")
    else:
        if missing_tables:
            print(f"Missing tables: {', '.join(missing_tables)}")
        
        # Step 4: Create tables
        if not create_tables():
            print("❌ Table creation failed!")
            sys.exit(1)
    
    # Step 5: Setup Alembic
    if not setup_alembic():
        print("⚠️  Alembic setup failed, but database is ready to use.")
    
    print()
    print("=" * 60)
    print("✅ Database initialization completed successfully!")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Start the backend server: uvicorn app.main:app --reload")
    print("2. For future schema changes, use: alembic revision --autogenerate -m 'description'")
    print("3. Apply migrations with: alembic upgrade head")

if __name__ == "__main__":
    main()
