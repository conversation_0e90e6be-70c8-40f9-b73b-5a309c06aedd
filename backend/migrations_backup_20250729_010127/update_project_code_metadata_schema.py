"""Update project_code_metadata schema to match current model

Revision ID: update_metadata_schema
Revises: 0f4f8121a8dd
Create Date: 2025-07-26 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'update_metadata_schema'
down_revision: Union[str, None] = '0f4f8121a8dd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # First, rename existing columns to match the new model
    op.alter_column('project_code_metadata', 'repository_url', new_column_name='git_url')
    op.alter_column('project_code_metadata', 'is_empty_repository', new_column_name='is_empty_repo')
    op.alter_column('project_code_metadata', 'last_analyzed_at', new_column_name='last_analysis_at')
    
    # Make git_url nullable (it was required before, but should be optional now)
    op.alter_column('project_code_metadata', 'git_url', nullable=True)
    op.alter_column('project_code_metadata', 'is_empty_repo', nullable=True)
    op.alter_column('project_code_metadata', 'last_analysis_at', nullable=True)
    
    # Remove columns that are no longer needed
    op.drop_column('project_code_metadata', 'detected_frameworks')
    op.drop_column('project_code_metadata', 'detected_languages')
    op.drop_column('project_code_metadata', 'file_structure')
    
    # Add new columns that are missing
    op.add_column('project_code_metadata', sa.Column('last_clone_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('project_code_metadata', sa.Column('clone_status', sa.String(), nullable=True))
    op.add_column('project_code_metadata', sa.Column('clone_error', sa.Text(), nullable=True))
    op.add_column('project_code_metadata', sa.Column('total_files', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('project_code_metadata', sa.Column('code_files_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('project_code_metadata', sa.Column('is_embedded', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('project_code_metadata', sa.Column('embedding_status', sa.String(), nullable=True))
    op.add_column('project_code_metadata', sa.Column('embedded_files_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('project_code_metadata', sa.Column('last_embedding_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('project_code_metadata', sa.Column('embedding_error', sa.Text(), nullable=True))
    op.add_column('project_code_metadata', sa.Column('total_generations', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('project_code_metadata', sa.Column('successful_generations', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('project_code_metadata', sa.Column('last_generation_at', sa.DateTime(timezone=True), nullable=True))
    
    # Add unique constraint on project_id (as specified in the model)
    op.create_unique_constraint('uq_project_code_metadata_project_id', 'project_code_metadata', ['project_id'])


def downgrade() -> None:
    # Remove unique constraint
    op.drop_constraint('uq_project_code_metadata_project_id', 'project_code_metadata', type_='unique')
    
    # Remove new columns
    op.drop_column('project_code_metadata', 'last_generation_at')
    op.drop_column('project_code_metadata', 'successful_generations')
    op.drop_column('project_code_metadata', 'total_generations')
    op.drop_column('project_code_metadata', 'embedding_error')
    op.drop_column('project_code_metadata', 'last_embedding_at')
    op.drop_column('project_code_metadata', 'embedded_files_count')
    op.drop_column('project_code_metadata', 'embedding_status')
    op.drop_column('project_code_metadata', 'is_embedded')
    op.drop_column('project_code_metadata', 'code_files_count')
    op.drop_column('project_code_metadata', 'total_files')
    op.drop_column('project_code_metadata', 'clone_error')
    op.drop_column('project_code_metadata', 'clone_status')
    op.drop_column('project_code_metadata', 'last_clone_at')
    
    # Add back old columns
    op.add_column('project_code_metadata', sa.Column('file_structure', sa.Text(), nullable=True))
    op.add_column('project_code_metadata', sa.Column('detected_languages', sa.Text(), nullable=True))
    op.add_column('project_code_metadata', sa.Column('detected_frameworks', sa.Text(), nullable=True))
    
    # Rename columns back to original names
    op.alter_column('project_code_metadata', 'last_analysis_at', new_column_name='last_analyzed_at')
    op.alter_column('project_code_metadata', 'is_empty_repo', new_column_name='is_empty_repository')
    op.alter_column('project_code_metadata', 'git_url', new_column_name='repository_url')
    
    # Make columns non-nullable again
    op.alter_column('project_code_metadata', 'repository_url', nullable=False)
    op.alter_column('project_code_metadata', 'is_empty_repository', nullable=False)
    op.alter_column('project_code_metadata', 'last_analyzed_at', nullable=False)
