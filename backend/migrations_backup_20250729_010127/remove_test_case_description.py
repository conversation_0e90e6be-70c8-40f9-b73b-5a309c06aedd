"""Remove description field from test_cases

Revision ID: remove_description
Revises: add_custom_id_notes
Create Date: 2025-01-19 16:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'remove_description'
down_revision = 'add_custom_id_notes'
depends_on = None


def upgrade():
    # Remove description column from test_cases table
    op.drop_column('test_cases', 'description')


def downgrade():
    # Add description column back
    op.add_column('test_cases', sa.Column('description', sa.Text(), nullable=False, server_default=''))
