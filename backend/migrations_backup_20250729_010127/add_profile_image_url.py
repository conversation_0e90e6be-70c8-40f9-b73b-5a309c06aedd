"""add profile image url

Revision ID: add_profile_image_url
Revises: add_auth_provider_simple
Create Date: 2025-07-28 04:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_profile_image_url'
down_revision = 'add_auth_provider_simple'
branch_labels = None
depends_on = None


def upgrade():
    # Add profile_image_url column to users table
    op.add_column('users', sa.Column('profile_image_url', sa.String(), nullable=True))


def downgrade():
    # Remove profile_image_url column
    op.drop_column('users', 'profile_image_url')
