"""Add custom_id and notes to test_cases

Revision ID: add_test_case_custom_id_and_notes
Revises: 
Create Date: 2025-01-19 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_custom_id_notes'
down_revision = None  # Update this with the actual previous revision
depends_on = None


def upgrade():
    # Add custom_id column
    op.add_column('test_cases', sa.Column('custom_id', sa.String(), nullable=True))
    
    # Add notes column
    op.add_column('test_cases', sa.Column('notes', sa.Text(), nullable=True))
    
    # Create index on custom_id
    op.create_index(op.f('ix_test_cases_custom_id'), 'test_cases', ['custom_id'], unique=False)
    
    # Update existing test cases with auto-generated custom_ids
    # This is a simple approach - in production you might want more sophisticated logic
    connection = op.get_bind()
    result = connection.execute(sa.text("""
        SELECT id, requirement_id 
        FROM test_cases 
        WHERE custom_id IS NULL 
        ORDER BY requirement_id, id
    """))
    
    current_requirement_id = None
    counter = 1
    
    for row in result:
        if row.requirement_id != current_requirement_id:
            current_requirement_id = row.requirement_id
            counter = 1
        
        custom_id = f"TC-{counter:03d}"
        connection.execute(sa.text("""
            UPDATE test_cases 
            SET custom_id = :custom_id 
            WHERE id = :id
        """), {"custom_id": custom_id, "id": row.id})
        
        counter += 1
    
    # Make custom_id non-nullable after populating existing records
    op.alter_column('test_cases', 'custom_id', nullable=False)


def downgrade():
    # Remove index
    op.drop_index(op.f('ix_test_cases_custom_id'), table_name='test_cases')
    
    # Remove columns
    op.drop_column('test_cases', 'notes')
    op.drop_column('test_cases', 'custom_id')
