"""Remove unused color column from tags table

Revision ID: remove_unused_color_column_from_tags
Revises: 
Create Date: 2025-07-28 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'remove_unused_color_column_from_tags'
down_revision: Union[str, None] = None  # Will be set to latest revision
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Remove the unused color column from tags table
    op.drop_column('tags', 'color')


def downgrade() -> None:
    # Add back the color column if needed
    op.add_column('tags', sa.Column('color', sa.String(), nullable=True))
