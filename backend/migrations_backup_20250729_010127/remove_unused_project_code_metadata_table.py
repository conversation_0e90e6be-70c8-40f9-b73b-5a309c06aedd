"""Remove unused project_code_metadata table

Revision ID: remove_unused_project_code_metadata_table
Revises: 
Create Date: 2025-07-28 12:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'remove_unused_project_code_metadata_table'
down_revision: Union[str, None] = None  # Will be set to latest revision
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop the unused project_code_metadata table
    op.drop_table('project_code_metadata')


def downgrade() -> None:
    # Recreate the table if needed (based on the original schema)
    op.create_table('project_code_metadata',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('git_url', sa.String(), nullable=True),
        sa.Column('last_clone_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('clone_status', sa.String(), nullable=True),
        sa.Column('clone_error', sa.Text(), nullable=True),
        sa.Column('is_empty_repo', sa.Boolean(), nullable=True),
        sa.Column('total_files', sa.Integer(), nullable=True),
        sa.Column('code_files_count', sa.Integer(), nullable=True),
        sa.Column('last_analysis_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_embedded', sa.Boolean(), nullable=True),
        sa.Column('embedding_status', sa.String(), nullable=True),
        sa.Column('embedded_files_count', sa.Integer(), nullable=True),
        sa.Column('last_embedding_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('embedding_error', sa.Text(), nullable=True),
        sa.Column('total_generations', sa.Integer(), nullable=True),
        sa.Column('successful_generations', sa.Integer(), nullable=True),
        sa.Column('last_generation_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('project_id', name='uq_project_code_metadata_project_id')
    )
