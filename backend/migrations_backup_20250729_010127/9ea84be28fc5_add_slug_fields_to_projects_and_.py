"""Add slug fields to projects and requirements

Revision ID: 9ea84be28fc5
Revises: remove_description
Create Date: 2025-07-22 13:28:25.340716

"""
from typing import Sequence, Union
import re

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9ea84be28fc5'
down_revision: Union[str, None] = 'remove_description'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def generate_slug(text: str, max_length: int = 50) -> str:
    """Generate a URL-friendly slug from text."""
    slug = text.lower()
    slug = re.sub(r'[^\w\s-]', '', slug)
    slug = re.sub(r'[-\s]+', '-', slug)
    slug = slug.strip('-')
    if len(slug) > max_length:
        slug = slug[:max_length].rstrip('-')
    if not slug:
        slug = 'item'
    return slug


def upgrade() -> None:
    # Add slug columns as nullable first
    op.add_column('projects', sa.Column('slug', sa.String(), nullable=True))
    op.add_column('requirements', sa.Column('slug', sa.String(), nullable=True))

    # Get database connection
    connection = op.get_bind()

    # Populate project slugs
    projects = connection.execute(sa.text("SELECT id, name FROM projects")).fetchall()
    used_slugs = set()

    for project in projects:
        base_slug = generate_slug(project.name)
        slug = base_slug
        counter = 1

        while slug in used_slugs:
            counter += 1
            slug = f"{base_slug}-{counter}"

        used_slugs.add(slug)
        connection.execute(
            sa.text("UPDATE projects SET slug = :slug WHERE id = :id"),
            {"slug": slug, "id": project.id}
        )

    # Populate requirement slugs (scoped by project)
    requirements = connection.execute(
        sa.text("SELECT id, name, project_id FROM requirements ORDER BY project_id")
    ).fetchall()

    project_slugs = {}  # project_id -> set of used slugs

    for requirement in requirements:
        project_id = requirement.project_id
        if project_id not in project_slugs:
            project_slugs[project_id] = set()

        base_slug = generate_slug(requirement.name)
        slug = base_slug
        counter = 1

        while slug in project_slugs[project_id]:
            counter += 1
            slug = f"{base_slug}-{counter}"

        project_slugs[project_id].add(slug)
        connection.execute(
            sa.text("UPDATE requirements SET slug = :slug WHERE id = :id"),
            {"slug": slug, "id": requirement.id}
        )

    # Make columns non-nullable and add constraints
    op.alter_column('projects', 'slug', nullable=False)
    op.alter_column('requirements', 'slug', nullable=False)

    op.create_index(op.f('ix_projects_slug'), 'projects', ['slug'], unique=True)
    op.create_unique_constraint('_project_requirement_slug_uc', 'requirements', ['project_id', 'slug'])
    op.create_index(op.f('ix_requirements_slug'), 'requirements', ['slug'], unique=False)


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_requirements_slug'), table_name='requirements')
    op.drop_constraint('_project_requirement_slug_uc', 'requirements', type_='unique')
    op.drop_column('requirements', 'slug')
    op.drop_index(op.f('ix_projects_slug'), table_name='projects')
    op.drop_column('projects', 'slug')
    # ### end Alembic commands ###
