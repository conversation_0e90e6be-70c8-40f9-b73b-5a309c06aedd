"""create_code_generation_tables

Revision ID: b4b563ec218d
Revises: 5c93c9375862
Create Date: 2025-07-23 01:51:21.768549

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b4b563ec218d'
down_revision: Union[str, None] = '5c93c9375862'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create code_generation_sessions table
    op.create_table('code_generation_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('requirement_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_id', sa.String(length=255), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('generation_type', sa.String(length=50), nullable=False),
        sa.Column('git_url', sa.String(length=500), nullable=True),
        sa.Column('automation_framework', sa.String(length=100), nullable=False),
        sa.Column('programming_language', sa.String(length=100), nullable=False),
        sa.Column('test_cases_data', sa.Text(), nullable=True),
        sa.Column('page_elements_data', sa.Text(), nullable=True),
        sa.Column('existing_code_context', sa.Text(), nullable=True),
        sa.Column('generated_files_count', sa.Integer(), nullable=False, default=0),
        sa.Column('zip_file_path', sa.String(length=500), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.ForeignKeyConstraint(['requirement_id'], ['requirements.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_code_generation_sessions_session_id'), 'code_generation_sessions', ['session_id'], unique=True)

    # Create generated_files table
    op.create_table('generated_files',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('session_id', sa.Integer(), nullable=False),
        sa.Column('file_path', sa.String(length=500), nullable=False),
        sa.Column('file_type', sa.String(length=100), nullable=False),
        sa.Column('file_size', sa.Integer(), nullable=False),
        sa.Column('content_hash', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['session_id'], ['code_generation_sessions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create code_generation_logs table
    op.create_table('code_generation_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('session_id', sa.Integer(), nullable=False),
        sa.Column('log_level', sa.String(length=20), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['session_id'], ['code_generation_sessions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create project_code_metadata table
    op.create_table('project_code_metadata',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('repository_url', sa.String(length=500), nullable=False),
        sa.Column('is_empty_repository', sa.Boolean(), nullable=False),
        sa.Column('detected_frameworks', sa.Text(), nullable=True),
        sa.Column('detected_languages', sa.Text(), nullable=True),
        sa.Column('file_structure', sa.Text(), nullable=True),
        sa.Column('last_analyzed_at', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('project_code_metadata')
    op.drop_table('code_generation_logs')
    op.drop_table('generated_files')
    op.drop_index(op.f('ix_code_generation_sessions_session_id'), table_name='code_generation_sessions')
    op.drop_table('code_generation_sessions')
