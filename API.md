# API Documentation

This document provides detailed information about the IntelliTest API endpoints.

## Base URL

- Development: `http://localhost:8000/api/v1`
- Production: `https://your-domain.com/api/v1`

## Authentication

The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Authentication

#### Register User
```http
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "full_name": "<PERSON>"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

#### Login User
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

#### Get Current User
```http
GET /auth/me
```

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Projects

#### List Projects
```http
GET /projects/
```

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "My Test Project",
    "description": "A sample project",
    "created_at": "2024-01-01T00:00:00Z",
    "member_count": 1,
    "requirement_count": 5
  }
]
```

#### Create Project
```http
POST /projects/
```

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "name": "New Project",
  "description": "Project description",
  "code_source": "https://github.com/user/repo",
  "automation_framework": "selenium",
  "programming_language": "python"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "New Project",
  "description": "Project description",
  "code_source": "https://github.com/user/repo",
  "automation_framework": "selenium",
  "programming_language": "python",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### Get Project
```http
GET /projects/{project_id}
```

#### Update Project
```http
PUT /projects/{project_id}
```

#### Delete Project
```http
DELETE /projects/{project_id}
```

### Project Variables

#### List Project Variables
```http
GET /projects/{project_id}/variables
```

**Response:**
```json
[
  {
    "id": 1,
    "project_id": 1,
    "key": "base_url",
    "value": "https://example.com",
    "description": "Application base URL",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### Create Project Variable
```http
POST /projects/{project_id}/variables
```

**Request Body:**
```json
{
  "key": "api_key",
  "value": "secret-key-123",
  "description": "API authentication key"
}
```

### Requirements

#### List Requirements
```http
GET /requirements/?project_id={project_id}
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "User Login",
    "created_at": "2024-01-01T00:00:00Z",
    "created_by": 1,
    "tags": [
      {"id": 1, "name": "authentication", "color": "#blue"}
    ],
    "test_case_count": 3
  }
]
```

#### Create Requirement
```http
POST /requirements/
```

**Request Body:**
```json
{
  "name": "User Registration",
  "description": "Users should be able to register with email and password. The system should validate @base_url and send confirmation email.",
  "project_id": 1,
  "tag_names": ["authentication", "email"]
}
```

#### Get Requirement
```http
GET /requirements/{requirement_id}
```

**Response:**
```json
{
  "id": 1,
  "name": "User Login",
  "description": "Users should be able to login with email and password",
  "refined_description": "The system shall provide a secure login mechanism...",
  "project_id": 1,
  "created_by": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "tags": [
    {"id": 1, "name": "authentication", "color": "#blue"}
  ],
  "test_cases": [
    {
      "id": 1,
      "title": "Valid Login",
      "description": "Test successful login with valid credentials",
      "steps": "1. Navigate to login page\n2. Enter valid email\n3. Enter valid password\n4. Click login",
      "expected_result": "User is logged in and redirected to dashboard"
    }
  ]
}
```

#### Update Requirement
```http
PUT /requirements/{requirement_id}
```

#### Delete Requirement
```http
DELETE /requirements/{requirement_id}
```

#### Refine Requirement
```http
POST /requirements/{requirement_id}/refine
```

**Response:**
```json
{
  "message": "Requirement refined successfully",
  "refined_description": "The system shall provide a secure authentication mechanism that allows registered users to access their accounts using valid email addresses and passwords. The login process must include input validation, secure password verification, session management, and appropriate error handling for invalid credentials."
}
```

#### Generate Test Cases
```http
POST /requirements/{requirement_id}/generate-tests
```

**Response:**
```json
[
  {
    "id": 10,
    "title": "Valid Login Test",
    "description": "Verify successful login with valid credentials",
    "steps": "1. Navigate to login page\n2. Enter valid email\n3. Enter valid password\n4. Click login button",
    "expected_result": "User is authenticated and redirected to dashboard",
    "requirement_id": 1,
    "created_at": "2024-01-01T00:00:00Z"
  },
  {
    "id": 11,
    "title": "Invalid Password Test",
    "description": "Verify error handling for incorrect password",
    "steps": "1. Navigate to login page\n2. Enter valid email\n3. Enter incorrect password\n4. Click login button",
    "expected_result": "Error message displayed: 'Invalid credentials'",
    "requirement_id": 1,
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

### Test Cases

#### List Test Cases
```http
GET /requirements/{requirement_id}/test-cases
```

#### Create Test Case
```http
POST /requirements/{requirement_id}/test-cases
```

**Request Body:**
```json
{
  "title": "Manual Test Case",
  "description": "A manually created test case",
  "steps": "1. Step one\n2. Step two\n3. Step three",
  "expected_result": "Expected outcome"
}
```

#### Update Test Case
```http
PUT /requirements/{requirement_id}/test-cases/{test_case_id}
```

#### Delete Test Case
```http
DELETE /requirements/{requirement_id}/test-cases/{test_case_id}
```

### Tags

#### List Tags
```http
GET /requirements/tags/
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "authentication",
    "color": "#3b82f6",
    "created_at": "2024-01-01T00:00:00Z"
  },
  {
    "id": 2,
    "name": "ui",
    "color": "#10b981",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

## Error Responses

### Standard Error Format

```json
{
  "detail": "Error message description"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Common Errors

#### Authentication Required
```http
HTTP/1.1 401 Unauthorized
```
```json
{
  "detail": "Could not validate credentials"
}
```

#### Validation Error
```http
HTTP/1.1 422 Unprocessable Entity
```
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

#### Resource Not Found
```http
HTTP/1.1 404 Not Found
```
```json
{
  "detail": "Project not found"
}
```

## Rate Limiting

- API endpoints: 10 requests per second
- Authentication endpoints: 5 requests per minute
- Test generation: 2 requests per minute

## Variable Substitution

Requirements support variable substitution using the `@variable_name` syntax. Variables are defined at the project level and automatically replaced when processing requirements.

Example:
```
Requirement: "Users should be able to access @base_url/login"
Variable: base_url = "https://myapp.com"
Result: "Users should be able to access https://myapp.com/login"
```

## Webhooks (Future Feature)

The API will support webhooks for real-time notifications:

- Requirement created/updated
- Test cases generated
- Project member added

## SDK and Client Libraries

Official client libraries will be available for:

- Python
- JavaScript/TypeScript
- Java
- C#

## OpenAPI Specification

The complete OpenAPI specification is available at:
- Development: `http://localhost:8000/docs`
- Production: `https://your-domain.com/docs`
