# IntelliTest Deployment Guide

This guide covers deployment scenarios for IntelliTest with clear separation between development and production environments.

## Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ disk space

---

## 🔧 Development Environment

### Option 1: Docker with External Database (Recommended for Development)

This setup uses Docker for services but connects to an external PostgreSQL database for easier development.

#### Prerequisites
- PostgreSQL 15+ installed locally
- Database named `intellitest` created

#### Setup Steps

1. **Prepare Database**
   ```bash
   # Install PostgreSQL (Ubuntu/Debian)
   sudo apt-get update
   sudo apt-get install postgresql postgresql-contrib

   # Or macOS with Homebrew
   brew install postgresql
   brew services start postgresql

   # Create database
   sudo -u postgres createdb intellitest
   # Or on macOS: createdb intellitest
   ```

2. **Configure Environment**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd IntelliTest

   # Create environment file
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database credentials
   ```

3. **Start Services**
   ```bash
   # Start Docker services (without database)
   docker-compose -f docker-compose.dev-no-db.yml up --build

   # Or use the script
   ./scripts/setup-dev-no-db.sh
   ```

4. **Run Database Migrations**
   ```bash
   cd backend
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   python3 -m alembic upgrade head
   ```

**Services Available:**
- Frontend: http://localhost:3000
- Backend: http://localhost:8000
- Qdrant: http://localhost:6333
- Ollama: http://localhost:11434
- PostgreSQL: localhost:5432 (external)

### Option 2: Full Docker Development Environment

This includes PostgreSQL in Docker for a completely containerized development environment.

```bash
# Start all services including database
docker-compose -f docker-compose.dev.yml up --build

# Or use the script
./scripts/setup-dev.sh
```

**Services Available:**
- Frontend: http://localhost:3000
- Backend: http://localhost:8000
- PostgreSQL: localhost:5432
- Qdrant: http://localhost:6333
- Ollama: http://localhost:11434

### Manual Development Setup (Without Docker)

#### Backend Setup

1. **Install Python 3.9+**
   ```bash
   cd backend
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database and service URLs
   ```

3. **Set up Database**
   ```bash
   # Ensure PostgreSQL is running
   # Create database: createdb intellitest

   # Run migrations
   python3 -m alembic upgrade head
   ```

4. **Start Backend**
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

#### Frontend Setup

1. **Install Node.js 18+**
   ```bash
   cd frontend
   npm install
   ```

2. **Configure Environment**
   ```bash
   # Create .env.local
   echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1" > .env.local
   ```

3. **Start Frontend**
   ```bash
   npm run dev
   ```

#### External Services

You'll need to run these services separately:

1. **PostgreSQL**: Install and run locally
2. **Qdrant**: `docker run -p 6333:6333 qdrant/qdrant:latest`
3. **Ollama**: Install from https://ollama.ai and run `ollama serve`

---

## 🚀 Production Environment

### Docker Production Deployment (Recommended)

This setup includes all services with production optimizations, security, and monitoring.

#### Setup Steps

1. **Prepare Environment**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd IntelliTest

   # Create production environment file
   cp .env.prod.example .env.prod
   ```

2. **Configure Production Variables**
   ```bash
   # Edit .env.prod with production values
   nano .env.prod
   ```

   Required variables:
   ```env
   # Database
   POSTGRES_PASSWORD=your-secure-password
   POSTGRES_USER=postgres
   POSTGRES_DB=intellitest

   # Security
   SECRET_KEY=your-very-secure-secret-key-min-32-chars

   # LLM
   OLLAMA_MODEL=llama3.2:latest
   ```

3. **Start Production Environment**
   ```bash
   # Using script (recommended)
   ./scripts/setup-prod.sh

   # Or manually
   docker-compose -f docker-compose.prod.yml up --build -d
   ```

4. **Verify Deployment**
   ```bash
   # Check service status
   docker-compose -f docker-compose.prod.yml ps

   # View logs
   docker-compose -f docker-compose.prod.yml logs -f
   ```

**Production Services:**
- Application: http://localhost (via Nginx)
- HTTPS: https://localhost (self-signed cert)
- All internal services are networked and secured

### Manual Production Setup

For production deployment without Docker:

#### Backend Production Setup

1. **Install Python and Dependencies**
   ```bash
   cd backend
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   pip install gunicorn  # Production WSGI server
   ```

2. **Configure Production Environment**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

3. **Set up Database**
   ```bash
   # Install PostgreSQL
   sudo apt-get install postgresql postgresql-contrib

   # Create production database
   sudo -u postgres createdb intellitest

   # Run migrations
   python3 -m alembic upgrade head
   ```

4. **Start with Gunicorn**
   ```bash
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
   ```

#### Frontend Production Setup

1. **Build for Production**
   ```bash
   cd frontend
   npm install
   npm run build
   ```

2. **Serve with PM2**
   ```bash
   npm install -g pm2
   pm2 start npm --name "intellitest-frontend" -- start
   pm2 startup  # Configure auto-start
   pm2 save
   ```

---

## ☁️ Cloud Deployment

### AWS Deployment

#### Using ECS with Fargate

1. **Build and push images**
   ```bash
   # Build images
   docker build -t intellitest-backend:latest ./backend
   docker build -t intellitest-frontend:latest ./frontend

   # Tag for ECR
   docker tag intellitest-backend:latest <account>.dkr.ecr.<region>.amazonaws.com/intellitest-backend:latest
   docker tag intellitest-frontend:latest <account>.dkr.ecr.<region>.amazonaws.com/intellitest-frontend:latest

   # Push to ECR
   docker push <account>.dkr.ecr.<region>.amazonaws.com/intellitest-backend:latest
   docker push <account>.dkr.ecr.<region>.amazonaws.com/intellitest-frontend:latest
   ```

2. **Set up RDS PostgreSQL**
   ```bash
   aws rds create-db-instance \
     --db-instance-identifier intellitest-db \
     --db-instance-class db.t3.micro \
     --engine postgres \
     --master-username postgres \
     --master-user-password <password> \
     --allocated-storage 20
   ```

#### Using EC2

1. **Launch EC2 instance**
   - AMI: Amazon Linux 2
   - Instance type: t3.medium or larger
   - Security groups: Allow ports 80, 443, 22

2. **Install Docker**
   ```bash
   sudo yum update -y
   sudo yum install -y docker
   sudo systemctl start docker
   sudo systemctl enable docker
   sudo usermod -a -G docker ec2-user

   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

3. **Deploy application**
   ```bash
   git clone <repository-url>
   cd IntelliTest
   cp .env.prod.example .env.prod
   # Edit .env.prod
   ./scripts/setup-prod.sh
   ```

---

## 🗄️ Database Management

### Running Migrations

#### In Docker Environment
```bash
# For development
docker exec intellitest_backend_dev python3 -m alembic upgrade head

# For production
docker exec intellitest_backend_prod python3 -m alembic upgrade head
```

#### Manual Environment
```bash
cd backend
source venv/bin/activate  # Activate virtual environment
python3 -m alembic upgrade head
```

### Creating New Migrations

```bash
cd backend
source venv/bin/activate
python3 -m alembic revision --autogenerate -m "Description of changes"
python3 -m alembic upgrade head
```

### Database Backup and Restore

#### Docker Environment
```bash
# Backup
docker exec intellitest_postgres_prod pg_dump -U postgres intellitest > backup.sql

# Restore
docker exec -i intellitest_postgres_prod psql -U postgres intellitest < backup.sql
```

#### Manual Environment
```bash
# Backup
pg_dump -U postgres -h localhost intellitest > backup.sql

# Restore
psql -U postgres -h localhost intellitest < backup.sql
```

---

## 🔒 Security Considerations

### Production Security Checklist

1. **Environment Variables**
   - ✅ Use strong, unique SECRET_KEY (min 32 characters)
   - ✅ Use secure database passwords
   - ✅ Never commit .env files to version control
   - ✅ Use environment-specific configurations

2. **SSL/TLS**
   - ✅ Use proper SSL certificates (Let's Encrypt recommended)
   - ✅ Configure HTTPS redirects in Nginx
   - ✅ Set secure headers

3. **Database Security**
   - ✅ Use connection pooling
   - ✅ Enable SSL connections
   - ✅ Regular automated backups
   - ✅ Restrict database access

4. **Network Security**
   - ✅ Use firewalls
   - ✅ Limit exposed ports
   - ✅ Use private networks/VPC
   - ✅ Regular security updates

### Nginx Security Configuration

```nginx
# Add to nginx.conf
server {
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Hide server version
    server_tokens off;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
}
```

## 🔒 Security Considerations

### Production Security

1. **Environment Variables**
   - Use strong, unique SECRET_KEY
   - Use secure database passwords
   - Don't commit .env files

2. **SSL/TLS**
   - Use proper SSL certificates (Let's Encrypt)
   - Configure HTTPS redirects
   - Set secure headers

3. **Database Security**
   - Use connection pooling
   - Enable SSL connections
   - Regular backups

4. **Network Security**
   - Use firewalls
   - Limit exposed ports
   - Use VPC/private networks

### Nginx Security Configuration

```nginx
# Add to nginx.conf
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

---

## 📊 Monitoring and Logging

### Docker Logging

```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend

# Export logs
docker-compose -f docker-compose.prod.yml logs --no-color > app.log
```

### Health Checks

All services include health checks accessible at:
- Backend: `GET http://localhost:8000/health`
- Frontend: `GET http://localhost:3000`
- Database: `pg_isready` command
- Qdrant: `GET http://localhost:6333/health`
- Ollama: `GET http://localhost:11434/api/version`

### Log Files

Backend logs are stored in the `backend/logs/` directory:
- `app.log` - Application logs (WARN level and above for file logs)
- Console logs show all levels during development

---

## 🚨 Troubleshooting

### Common Development Issues

1. **Services not starting**
   ```bash
   # Check service status
   docker-compose -f docker-compose.dev.yml ps

   # View service logs
   docker-compose -f docker-compose.dev.yml logs <service-name>

   # Restart specific service
   docker-compose -f docker-compose.dev.yml restart <service-name>
   ```

2. **Database connection issues**
   ```bash
   # Check if PostgreSQL is running
   docker-compose -f docker-compose.dev.yml ps postgres

   # Test database connection
   docker exec intellitest_postgres_dev pg_isready -U postgres

   # Connect to database
   docker exec -it intellitest_postgres_dev psql -U postgres -d intellitest
   ```

3. **Migration issues**
   ```bash
   # Check migration status
   cd backend && python3 -m alembic current

   # View migration history
   python3 -m alembic history

   # Reset to specific migration
   python3 -m alembic downgrade <revision>
   python3 -m alembic upgrade head
   ```

4. **Ollama model issues**
   ```bash
   # Check available models
   docker exec intellitest_ollama_dev ollama list

   # Pull required model
   docker exec intellitest_ollama_dev ollama pull llama3.2:latest

   # Test model
   docker exec intellitest_ollama_dev ollama run llama3.2:latest "Hello"
   ```

5. **Frontend build issues**
   ```bash
   # Clear cache and reinstall
   cd frontend
   rm -rf node_modules .next
   npm install

   # Check Node.js version
   node --version  # Should be 18+

   # Verify environment variables
   cat .env.local
   ```

### Common Production Issues

1. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in ssl/cert.pem -text -noout

   # Renew Let's Encrypt certificate
   certbot renew --nginx
   ```

2. **Performance Issues**
   ```bash
   # Check resource usage
   docker stats

   # Monitor database performance
   docker exec intellitest_postgres_prod psql -U postgres -d intellitest -c "SELECT * FROM pg_stat_activity;"
   ```

3. **Disk Space Issues**
   ```bash
   # Check disk usage
   df -h

   # Clean up Docker
   docker system prune -a

   # Clean up old logs
   find backend/logs -name "*.log.*" -mtime +7 -delete
   ```

### Performance Optimization

1. **Database Performance**
   - Database indexes have been added for common queries
   - Monitor slow queries: `SELECT * FROM pg_stat_statements ORDER BY total_time DESC;`
   - Increase connection pool size if needed
   - Enable query caching

2. **Backend Performance**
   - Increase worker processes in production
   - Monitor memory usage
   - Use async database operations
   - Implement Redis caching if needed

3. **Frontend Performance**
   - Enable Nginx compression
   - Use CDN for static assets
   - Monitor bundle size
   - Implement code splitting

---

## 📞 Support

### Getting Help

1. **Check logs first**
   - Backend: `backend/logs/app.log`
   - Docker: `docker-compose logs`

2. **Common commands**
   ```bash
   # Restart all services
   docker-compose -f docker-compose.prod.yml restart

   # Rebuild and restart
   docker-compose -f docker-compose.prod.yml up --build -d

   # Check service health
   curl http://localhost:8000/health
   ```

3. **Database maintenance**
   ```bash
   # Backup before maintenance
   docker exec intellitest_postgres_prod pg_dump -U postgres intellitest > backup_$(date +%Y%m%d).sql

   # Run migrations
   docker exec intellitest_backend_prod python3 -m alembic upgrade head
   ```

### Environment Variables Reference

#### Required for Production
- `POSTGRES_PASSWORD`: Secure database password
- `SECRET_KEY`: JWT signing key (min 32 characters)
- `POSTGRES_USER`: Database username (default: postgres)
- `POSTGRES_DB`: Database name (default: intellitest)

#### Optional
- `OLLAMA_MODEL`: LLM model to use (default: llama3.2:latest)
- `LOG_LEVEL`: Logging level (default: INFO)
- `SMTP_*`: Email configuration for password reset
